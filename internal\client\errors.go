package client

import (
	"fmt"
	"net"
	"strings"
	"time"
)

// ClientError 表示客户端特有的错误
type ClientError struct {
	ClientID string
	Op       string // 失败的操作
	Err      error
	Code     ErrorCode
}

func (e *ClientError) Error() string {
	return fmt.Sprintf("client %s: %s failed: %v", e.ClientID, e.Op, e.Err)
}

func (e *ClientError) Unwrap() error {
	return e.Err
}

// ErrorCode 表示不同类型的客户端错误
type ErrorCode int

const (
	// ErrorCodeUnknown 表示未知错误
	ErrorCodeUnknown ErrorCode = iota

	// ErrorCodeNetwork 表示网络相关错误
	ErrorCodeNetwork

	// ErrorCodeTimeout 表示超时错误
	ErrorCodeTimeout

	// ErrorCodeProtocol 表示协议级别错误
	ErrorCodeProtocol

	// ErrorCodeServer 表示服务器端错误
	ErrorCodeServer

	// ErrorCodeConfiguration 表示配置错误
	ErrorCodeConfiguration

	// ErrorCodeNotReady 表示客户端未准备好错误
	ErrorCodeNotReady

	// ErrorCodeTestInProgress 表示测试已在进行中错误
	ErrorCodeTestInProgress
)

// String 返回 ErrorCode 的字符串表示
func (e ErrorCode) String() string {
	switch e {
	case ErrorCodeNetwork:
		return "network_error"
	case ErrorCodeTimeout:
		return "timeout_error"
	case ErrorCodeProtocol:
		return "protocol_error"
	case ErrorCodeServer:
		return "server_error"
	case ErrorCodeConfiguration:
		return "configuration_error"
	case ErrorCodeNotReady:
		return "not_ready_error"
	case ErrorCodeTestInProgress:
		return "test_in_progress_error"
	default:
		return "unknown_error"
	}
}

// IsRetryable 返回错误是否可重试
func (e ErrorCode) IsRetryable() bool {
	switch e {
	case ErrorCodeNetwork, ErrorCodeTimeout, ErrorCodeServer:
		return true
	case ErrorCodeProtocol, ErrorCodeConfiguration, ErrorCodeNotReady, ErrorCodeTestInProgress:
		return false
	default:
		return false
	}
}

// ClassifyError 将错误归类为 ErrorCode
func ClassifyError(err error) ErrorCode {
	if err == nil {
		return ErrorCodeUnknown
	}

	errStr := strings.ToLower(err.Error())

	// 检查超时错误
	if strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "deadline exceeded") ||
		strings.Contains(errStr, "context deadline exceeded") {
		return ErrorCodeTimeout
	}

	// 检查网络错误
	if isNetworkError(err) {
		return ErrorCodeNetwork
	}

	// 检查协议错误
	if strings.Contains(errStr, "invalid") ||
		strings.Contains(errStr, "malformed") ||
		strings.Contains(errStr, "parse") ||
		strings.Contains(errStr, "unmarshal") {
		return ErrorCodeProtocol
	}

	// 检查服务器错误
	if strings.Contains(errStr, "server") ||
		strings.Contains(errStr, "500") ||
		strings.Contains(errStr, "503") ||
		strings.Contains(errStr, "502") {
		return ErrorCodeServer
	}

	// 检查配置错误
	if strings.Contains(errStr, "config") ||
		strings.Contains(errStr, "invalid host") ||
		strings.Contains(errStr, "invalid port") {
		return ErrorCodeConfiguration
	}

	// 检查未就绪错误
	if strings.Contains(errStr, "not ready") ||
		strings.Contains(errStr, "not prepared") ||
		strings.Contains(errStr, "server not running") {
		return ErrorCodeNotReady
	}

	// 检查测试进行中错误
	if strings.Contains(errStr, "test in progress") ||
		strings.Contains(errStr, "already testing") {
		return ErrorCodeTestInProgress
	}

	return ErrorCodeUnknown
}

// isNetworkError 检查错误是否是网络相关错误
func isNetworkError(err error) bool {
	// 检查 net.Error 接口
	if netErr, ok := err.(net.Error); ok {
		return netErr.Temporary() || netErr.Timeout()
	}

	// 检查常见的网络错误字符串
	errStr := strings.ToLower(err.Error())
	networkErrors := []string{
		"connection refused",
		"connection reset",
		"connection aborted",
		"network unreachable",
		"host unreachable",
		"no route to host",
		"broken pipe",
		"connection timed out",
		"i/o timeout",
		"eof",
	}

	for _, netErr := range networkErrors {
		if strings.Contains(errStr, netErr) {
			return true
		}
	}

	return false
}

// NewClientError 创建一个新的 ClientError
func NewClientError(clientID, op string, err error) *ClientError {
	return &ClientError{
		ClientID: clientID,
		Op:       op,
		Err:      err,
		Code:     ClassifyError(err),
	}
}

// RetryableError 表示可重试的错误
type RetryableError struct {
	Err        error
	RetryAfter time.Duration // 重试间隔时间
}

func (e *RetryableError) Error() string {
	return fmt.Sprintf("retryable error (retry after %v): %v", e.RetryAfter, e.Err)
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

// NewRetryableError 创建一个新的 RetryableError
func NewRetryableError(err error, retryAfter time.Duration) *RetryableError {
	return &RetryableError{
		Err:        err,
		RetryAfter: retryAfter,
	}
}

// IsRetryableError 检查错误是否可重试
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 检查它是否被明确标记为可重试
	if _, ok := err.(*RetryableError); ok {
		return true
	}

	// 检查它是否是带有可重试代码的 ClientError
	if clientErr, ok := err.(*ClientError); ok {
		return clientErr.Code.IsRetryable()
	}

	// 对错误进行分类并检查它是否可重试
	code := ClassifyError(err)
	return code.IsRetryable()
}
