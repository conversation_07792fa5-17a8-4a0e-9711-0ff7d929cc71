package client

import (
	"encoding/json"
	"time"
)

// Protocol 定义了控制器和客户端服务器之间的 JSON over HTTP 通信协议
//

// MessageType 表示协议消息的类型
type MessageType string

const (
	// 请求消息类型
	MessageTypePrepareRequest   MessageType = "prepare_request"
	MessageTypeStartTestRequest MessageType = "start_test_request"
	MessageTypeStopTestRequest  MessageType = "stop_test_request"
	MessageTypeStopRequest      MessageType = "stop_request"
	MessageTypeStatusRequest    MessageType = "status_request"

	// 响应消息类型
	MessageTypePrepareResponse   MessageType = "prepare_response"
	MessageTypeStartTestResponse MessageType = "start_test_response"
	MessageTypeStopTestResponse  MessageType = "stop_test_response"
	MessageTypeStopResponse      MessageType = "stop_response"
	MessageTypeStatusResponse    MessageType = "status_response"

	// 错误消息类型
	MessageTypeError MessageType = "error"
)

// BaseMessage 表示所有协议消息的基本结构
type BaseMessage struct {
	Type      MessageType `json:"type"`
	RequestID string      `json:"request_id"`
	Timestamp time.Time   `json:"timestamp"`
}

// PrepareRequest 请求客户端准备进行测试（启动 iperf3 服务器）
type PrepareRequest struct {
	BaseMessage
	Port int `json:"port"` // iperf3 服务器监听的端口
}

// PrepareResponse 确认客户端已准备好进行测试
type PrepareResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Port    int    `json:"port"`    // Actual port iperf3 server is listening on
	Message string `json:"message"` // Additional information
}

// StartTestRequest 请求客户端启动测试
type StartTestRequest struct {
	BaseMessage
	TestConfig TestConfig `json:"test_config"`
	ServerHost string     `json:"server_host"` // 用于反向测试的控制器主机
	ServerPort int        `json:"server_port"` // 用于反向测试的控制器端口
}

// StartTestResponse 提供测试结果
type StartTestResponse struct {
	BaseMessage
	Success    bool        `json:"success"`
	TestResult *TestResult `json:"test_result,omitempty"`
	Message    string      `json:"message"`
}

// StopTestRequest 请求客户端停止当前测试
type StopTestRequest struct {
	BaseMessage
}

// StopTestResponse 确认测试已停止
type StopTestResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// StopRequest 请求客户端停止 iperf3 服务器
type StopRequest struct {
	BaseMessage
}

// StopResponse 确认 iperf3 服务器已停止
type StopResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// StatusRequest 请求客户端的当前状态
type StatusRequest struct {
	BaseMessage
}

// StatusResponse 提供客户端的当前状态
type StatusResponse struct {
	BaseMessage
	Status ClientStatus `json:"status"`
}

// ErrorResponse 表示错误响应
type ErrorResponse struct {
	BaseMessage
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Details      string `json:"details,omitempty"`
}

// 常用协议错误代码（JSON 的字符串常量）
const (
	ProtocolErrorInvalidRequest   = "INVALID_REQUEST"
	ProtocolErrorServerNotRunning = "SERVER_NOT_RUNNING"
	ProtocolErrorTestInProgress   = "TEST_IN_PROGRESS"
	ProtocolErrorTestFailed       = "TEST_FAILED"
	ProtocolErrorTimeout          = "TIMEOUT"
	ProtocolErrorInternalError    = "INTERNAL_ERROR"
)

// ProtocolMessage 是所有协议消息的联合类型
type ProtocolMessage struct {
	Type MessageType     `json:"type"`
	Data json.RawMessage `json:"data"`
}

// ParseMessage 将 JSON 消息解析为相应的消息类型
func ParseMessage(data []byte) (interface{}, error) {
	var pm ProtocolMessage
	if err := json.Unmarshal(data, &pm); err != nil {
		return nil, err
	}

	switch pm.Type {
	case MessageTypePrepareRequest:
		var msg PrepareRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypePrepareResponse:
		var msg PrepareResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStartTestRequest:
		var msg StartTestRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStartTestResponse:
		var msg StartTestResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopTestRequest:
		var msg StopTestRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopTestResponse:
		var msg StopTestResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopRequest:
		var msg StopRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopResponse:
		var msg StopResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStatusRequest:
		var msg StatusRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStatusResponse:
		var msg StatusResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeError:
		var msg ErrorResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	default:
		return nil, &ProtocolError{
			Code:    ProtocolErrorInvalidRequest,
			Message: "unknown message type: " + string(pm.Type),
		}
	}
}

// ProtocolError 表示协议级别错误
type ProtocolError struct {
	Code    string
	Message string
	Details string
}

func (e *ProtocolError) Error() string {
	if e.Details != "" {
		return e.Message + ": " + e.Details
	}
	return e.Message
}

// CreateMessage 使用给定类型和数据创建协议消息
func CreateMessage(msgType MessageType, data interface{}) (*ProtocolMessage, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	return &ProtocolMessage{
		Type: msgType,
		Data: dataBytes,
	}, nil
}
