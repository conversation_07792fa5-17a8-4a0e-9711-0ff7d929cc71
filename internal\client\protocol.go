package client

import (
	"encoding/json"
	"time"
)

// Protocol defines the JSON over HTTP communication protocol between
// the controller and client servers

// MessageType represents the type of protocol message
type MessageType string

const (
	// Request message types
	MessageTypePrepareRequest   MessageType = "prepare_request"
	MessageTypeStartTestRequest MessageType = "start_test_request"
	MessageTypeStopTestRequest  MessageType = "stop_test_request"
	MessageTypeStopRequest      MessageType = "stop_request"
	MessageTypeStatusRequest    MessageType = "status_request"

	// Response message types
	MessageTypePrepareResponse   MessageType = "prepare_response"
	MessageTypeStartTestResponse MessageType = "start_test_response"
	MessageTypeStopTestResponse  MessageType = "stop_test_response"
	MessageTypeStopResponse      MessageType = "stop_response"
	MessageTypeStatusResponse    MessageType = "status_response"

	// Error message type
	MessageTypeError MessageType = "error"
)

// BaseMessage represents the base structure for all protocol messages
type BaseMessage struct {
	Type      MessageType `json:"type"`
	RequestID string      `json:"request_id"`
	Timestamp time.Time   `json:"timestamp"`
}

// PrepareRequest requests the client to prepare for testing (start iperf3 server)
type PrepareRequest struct {
	BaseMessage
	Port int `json:"port"` // Port for iperf3 server to listen on
}

// PrepareResponse confirms that the client is prepared for testing
type PrepareResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Port    int    `json:"port"`    // Actual port iperf3 server is listening on
	Message string `json:"message"` // Additional information
}

// StartTestRequest requests the client to start a test
type StartTestRequest struct {
	BaseMessage
	TestConfig TestConfig `json:"test_config"`
	ServerHost string     `json:"server_host"` // Controller's host for reverse testing
	ServerPort int        `json:"server_port"` // Controller's port for reverse testing
}

// StartTestResponse provides the result of the test
type StartTestResponse struct {
	BaseMessage
	Success    bool        `json:"success"`
	TestResult *TestResult `json:"test_result,omitempty"`
	Message    string      `json:"message"`
}

// StopTestRequest requests the client to stop the current test
type StopTestRequest struct {
	BaseMessage
}

// StopTestResponse confirms that the test has been stopped
type StopTestResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// StopRequest requests the client to stop the iperf3 server
type StopRequest struct {
	BaseMessage
}

// StopResponse confirms that the iperf3 server has been stopped
type StopResponse struct {
	BaseMessage
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// StatusRequest requests the current status of the client
type StatusRequest struct {
	BaseMessage
}

// StatusResponse provides the current status of the client
type StatusResponse struct {
	BaseMessage
	Status ClientStatus `json:"status"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	BaseMessage
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Details      string `json:"details,omitempty"`
}

// Common protocol error codes (string constants for JSON)
const (
	ProtocolErrorInvalidRequest   = "INVALID_REQUEST"
	ProtocolErrorServerNotRunning = "SERVER_NOT_RUNNING"
	ProtocolErrorTestInProgress   = "TEST_IN_PROGRESS"
	ProtocolErrorTestFailed       = "TEST_FAILED"
	ProtocolErrorTimeout          = "TIMEOUT"
	ProtocolErrorInternalError    = "INTERNAL_ERROR"
)

// ProtocolMessage is a union type for all protocol messages
type ProtocolMessage struct {
	Type MessageType     `json:"type"`
	Data json.RawMessage `json:"data"`
}

// ParseMessage parses a JSON message into the appropriate message type
func ParseMessage(data []byte) (interface{}, error) {
	var pm ProtocolMessage
	if err := json.Unmarshal(data, &pm); err != nil {
		return nil, err
	}

	switch pm.Type {
	case MessageTypePrepareRequest:
		var msg PrepareRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypePrepareResponse:
		var msg PrepareResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStartTestRequest:
		var msg StartTestRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStartTestResponse:
		var msg StartTestResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopTestRequest:
		var msg StopTestRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopTestResponse:
		var msg StopTestResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopRequest:
		var msg StopRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStopResponse:
		var msg StopResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStatusRequest:
		var msg StatusRequest
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeStatusResponse:
		var msg StatusResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	case MessageTypeError:
		var msg ErrorResponse
		err := json.Unmarshal(pm.Data, &msg)
		return &msg, err

	default:
		return nil, &ProtocolError{
			Code:    ProtocolErrorInvalidRequest,
			Message: "unknown message type: " + string(pm.Type),
		}
	}
}

// ProtocolError represents a protocol-level error
type ProtocolError struct {
	Code    string
	Message string
	Details string
}

func (e *ProtocolError) Error() string {
	if e.Details != "" {
		return e.Message + ": " + e.Details
	}
	return e.Message
}

// CreateMessage creates a protocol message with the given type and data
func CreateMessage(msgType MessageType, data interface{}) (*ProtocolMessage, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	return &ProtocolMessage{
		Type: msgType,
		Data: dataBytes,
	}, nil
}
