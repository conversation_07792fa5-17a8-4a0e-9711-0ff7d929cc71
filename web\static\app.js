// iperf3 控制器前端应用
class Iperf3Controller {
    constructor() {
        this.apiBase = '/api/v1';
        this.refreshInterval = 5000; // 5秒刷新一次
        this.refreshTimer = null;
        
        this.init();
    }
    
    init() {
        console.log('🚀 iperf3 控制器初始化...');
        this.startAutoRefresh();
        this.loadInitialData();
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshTimer = setInterval(() => {
            this.refreshData();
        }, this.refreshInterval);
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    // 加载初始数据
    async loadInitialData() {
        await this.refreshData();
    }
    
    // 刷新所有数据
    async refreshData() {
        try {
            await Promise.all([
                this.loadSystemStatus(),
                this.loadPerformanceStats(),
                this.loadClients(),
                this.loadScheduleStatus(),
                this.loadSyncStatus()
            ]);
        } catch (error) {
            console.error('刷新数据失败:', error);
        }
    }
    
    // 加载系统状态
    async loadSystemStatus() {
        try {
            const response = await fetch(`${this.apiBase}/status`);
            const data = await response.json();
            
            if (data.success) {
                this.updateSystemStatus(data.data);
            }
        } catch (error) {
            console.error('加载系统状态失败:', error);
        }
    }
    
    // 更新系统状态显示
    updateSystemStatus(data) {
        const components = data.components;
        
        // 更新调度器状态
        const schedulerStatus = document.getElementById('scheduler-status');
        const schedulerText = document.getElementById('scheduler-text');
        if (components.scheduler.running) {
            schedulerStatus.className = 'status-indicator status-running';
            schedulerText.textContent = '运行中';
        } else {
            schedulerStatus.className = 'status-indicator status-stopped';
            schedulerText.textContent = '已停止';
        }
        
        // 更新同步状态
        const syncStatus = document.getElementById('sync-status');
        const syncText = document.getElementById('sync-text');
        if (components.sync.running) {
            syncStatus.className = 'status-indicator status-running';
            syncText.textContent = '运行中';
        } else {
            syncStatus.className = 'status-indicator status-stopped';
            syncText.textContent = '已停止';
        }
        
        // 更新对端连接状态
        const peerStatus = document.getElementById('peer-status');
        const peerText = document.getElementById('peer-text');
        if (components.sync.peer_connected) {
            peerStatus.className = 'status-indicator status-running';
            peerText.textContent = '已连接';
        } else {
            peerStatus.className = 'status-indicator status-warning';
            peerText.textContent = '未连接';
        }
        
        // 更新数据同步状态
        const dataSyncStatus = document.getElementById('data-sync-status');
        dataSyncStatus.textContent = components.sync.data_in_sync ? '已同步' : '未同步';
        
        // 更新测试统计
        document.getElementById('total-tests').textContent = components.scheduler.total_tests || 0;
        document.getElementById('successful-tests').textContent = components.scheduler.successful_tests || 0;
        document.getElementById('failed-tests').textContent = components.scheduler.failed_tests || 0;
        
        const totalTests = components.scheduler.total_tests || 0;
        const successfulTests = components.scheduler.successful_tests || 0;
        const successRate = totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0;
        document.getElementById('success-rate').textContent = `${successRate}%`;
        
        // 更新客户端数量
        document.getElementById('total-clients').textContent = components.clients.total_count || 0;
        document.getElementById('enabled-clients').textContent = components.clients.enabled_count || 0;
    }
    
    // 加载性能统计
    async loadPerformanceStats() {
        try {
            const response = await fetch(`${this.apiBase}/stats/performance`);
            const data = await response.json();
            
            if (data.success) {
                this.updatePerformanceStats(data.data);
            }
        } catch (error) {
            console.error('加载性能统计失败:', error);
        }
    }
    
    // 更新性能统计显示
    updatePerformanceStats(data) {
        const performance = data.performance;
        
        document.getElementById('avg-tcp-speed').textContent = `${performance.avg_tcp_speed} Mbps`;
        document.getElementById('avg-udp-speed').textContent = `${performance.avg_udp_speed} Mbps`;
        document.getElementById('max-tcp-speed').textContent = `${performance.max_tcp_speed} Mbps`;
        document.getElementById('avg-latency').textContent = `${performance.avg_latency} ms`;
        document.getElementById('packet-loss').textContent = `${performance.packet_loss_rate}%`;
    }
    
    // 加载客户端列表
    async loadClients() {
        try {
            const response = await fetch(`${this.apiBase}/clients`);
            const data = await response.json();
            
            if (data.success) {
                this.updateClientsDisplay(data.data.clients);
            }
        } catch (error) {
            console.error('加载客户端列表失败:', error);
        }
    }
    
    // 更新客户端显示
    updateClientsDisplay(clients) {
        const clientsGrid = document.getElementById('clients-grid');
        clientsGrid.innerHTML = '';
        
        clients.forEach(client => {
            const clientCard = document.createElement('div');
            clientCard.className = 'client-card';
            clientCard.innerHTML = `
                <div class="client-name">${client.name}</div>
                <div class="client-info">${client.host}:${client.port}</div>
                <div class="client-info">状态: ${client.status}</div>
            `;
            clientsGrid.appendChild(clientCard);
        });
    }
    
    // 加载调度状态
    async loadScheduleStatus() {
        try {
            const response = await fetch(`${this.apiBase}/schedule/status`);
            const data = await response.json();
            
            if (data.success) {
                this.updateScheduleStatus(data.data);
            }
        } catch (error) {
            console.error('加载调度状态失败:', error);
        }
    }
    
    // 更新调度状态显示
    updateScheduleStatus(data) {
        if (data.next_test_time) {
            const nextTime = new Date(data.next_test_time);
            document.getElementById('next-test-time').textContent = nextTime.toLocaleTimeString();
        } else {
            document.getElementById('next-test-time').textContent = '--';
        }
    }
    
    // 加载同步状态
    async loadSyncStatus() {
        try {
            const response = await fetch(`${this.apiBase}/sync/status`);
            const data = await response.json();
            
            if (data.success) {
                // 同步状态已在系统状态中更新
            }
        } catch (error) {
            console.error('加载同步状态失败:', error);
        }
    }
    
    // 显示提示消息
    showAlert(message, type = 'success') {
        const alertContainer = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        alertContainer.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    
    // API调用辅助方法
    async apiCall(endpoint, method = 'GET', body = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (body) {
                options.body = JSON.stringify(body);
            }
            
            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            const data = await response.json();
            
            return data;
        } catch (error) {
            console.error(`API调用失败 ${endpoint}:`, error);
            throw error;
        }
    }
}

// 全局控制器实例
let controller;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    controller = new Iperf3Controller();
});

// 控制面板函数
async function startScheduler() {
    try {
        const data = await controller.apiCall('/schedule/start', 'POST');
        if (data.success) {
            controller.showAlert('调度器启动成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`启动调度器失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('启动调度器失败', 'error');
    }
}

async function stopScheduler() {
    try {
        const data = await controller.apiCall('/schedule/stop', 'POST');
        if (data.success) {
            controller.showAlert('调度器停止成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`停止调度器失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('停止调度器失败', 'error');
    }
}

async function triggerTest() {
    try {
        const data = await controller.apiCall('/tests/trigger', 'POST');
        if (data.success) {
            controller.showAlert('手动测试已触发', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`触发测试失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('触发测试失败', 'error');
    }
}

async function startSync() {
    try {
        const data = await controller.apiCall('/sync/start', 'POST');
        if (data.success) {
            controller.showAlert('同步服务启动成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`启动同步服务失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('启动同步服务失败', 'error');
    }
}

async function stopSync() {
    try {
        const data = await controller.apiCall('/sync/stop', 'POST');
        if (data.success) {
            controller.showAlert('同步服务停止成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`停止同步服务失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('停止同步服务失败', 'error');
    }
}

async function refreshData() {
    await controller.refreshData();
    controller.showAlert('数据刷新完成', 'success');
}
