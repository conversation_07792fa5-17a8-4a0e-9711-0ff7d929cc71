package scheduler

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// scheduleRoutine is the main scheduling loop
func (s *DefaultScheduler) scheduleRoutine(ctx context.Context) {
	s.logger.Info("Starting schedule routine")

	// Calculate initial delay until next test
	delay := s.getTimeUntilNextTest()
	s.logger.WithField("delay", delay).Info("Waiting for next scheduled test")

	// Create ticker for the next test time
	timer := time.NewTimer(delay)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Schedule routine stopped due to context cancellation")
			return
		case <-s.stopChan:
			s.logger.Info("Schedule routine stopped")
			return
		case <-timer.C:
			// Execute scheduled test
			now := time.Now().In(s.timezone)
			hour := now.Hour()

			s.logger.WithFields(logrus.Fields{
				"hour":     hour,
				"time":     now.Format("2006-01-02 15:04:05"),
				"timezone": s.timezone.String(),
			}).Info("Scheduled test time reached")

			// Check if we should run the test for this hour
			if s.shouldRunTest(hour) {
				// Check if test already executed for this hour
				executed, err := s.isTestAlreadyExecuted(ctx, hour)
				if err != nil {
					s.logger.WithError(err).Error("Failed to check if test already executed")
				} else if executed {
					s.logger.WithField("hour", hour).Info("Test already executed for this hour, skipping")
				} else {
					// Execute the test
					if err := s.executeTest(ctx, hour, "scheduled"); err != nil {
						s.logger.WithError(err).Error("Scheduled test execution failed")
					}
				}
			} else {
				s.logger.WithField("hour", hour).Debug("Test not scheduled for this hour")
			}

			// Calculate next test time and reset timer
			nextDelay := s.getTimeUntilNextTest()
			s.logger.WithField("next_delay", nextDelay).Info("Scheduling next test")
			timer.Reset(nextDelay)
		}
	}
}

// executeTest executes a test for the given hour
func (s *DefaultScheduler) executeTest(ctx context.Context, hour int, triggerType string) error {
	s.mu.Lock()

	// Check if there's already a test running
	if s.currentExecution != nil && s.currentExecution.Status == "running" {
		s.mu.Unlock()
		return fmt.Errorf("test is already running")
	}

	// Create new test execution record
	execution := &TestExecution{
		ID:        uuid.New().String(),
		Hour:      hour,
		StartTime: time.Now().In(s.timezone),
		Status:    "running",
		Triggered: triggerType,
	}

	s.currentExecution = execution
	s.mu.Unlock()

	s.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"hour":         hour,
		"trigger_type": triggerType,
	}).Info("Starting test execution")

	// 执行抢占式测试 - 所有服务器同时开始抢占，谁先响应谁先测速
	result, err := s.coordinator.ExecuteTestRound(ctx, hour)

	// Update execution record
	s.mu.Lock()
	endTime := time.Now().In(s.timezone)
	execution.EndTime = &endTime

	if err != nil {
		execution.Status = "failed"
		errStr := err.Error()
		execution.Error = &errStr
		s.lastError = err
		s.failedTests++
		s.totalTests++

		s.logger.WithFields(logrus.Fields{
			"execution_id": execution.ID,
			"hour":         hour,
			"error":        err.Error(),
			"duration":     endTime.Sub(execution.StartTime),
		}).Error("Test execution failed")
	} else {
		execution.Status = "completed"
		s.successfulTests++
		s.totalTests++
		s.lastTestTime = &endTime

		s.logger.WithFields(logrus.Fields{
			"execution_id":     execution.ID,
			"hour":             hour,
			"duration":         endTime.Sub(execution.StartTime),
			"total_tests":      result.TotalTests,
			"successful_tests": result.SuccessfulTests,
			"failed_tests":     result.FailedTests,
		}).Info("Test execution completed successfully")
	}

	// Store execution record in database (if repository is available)
	if s.repository != nil {
		if err := s.storeExecutionRecord(ctx, execution); err != nil {
			s.logger.WithError(err).Warn("Failed to store execution record")
		}
	}

	s.currentExecution = nil
	s.mu.Unlock()

	return err
}

// storeExecutionRecord stores the test execution record in the database
func (s *DefaultScheduler) storeExecutionRecord(ctx context.Context, execution *TestExecution) error {
	// This would implement storing the execution record in the database
	// For now, we'll just log it
	s.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"hour":         execution.Hour,
		"status":       execution.Status,
		"start_time":   execution.StartTime,
		"end_time":     execution.EndTime,
		"triggered":    execution.Triggered,
	}).Debug("Storing execution record")

	// 实现数据库存储（当前为占位符实现）
	// 在实际部署时，这里会将执行记录存储到数据库中

	return nil
}

// GetCurrentExecution returns the current test execution if any
func (s *DefaultScheduler) GetCurrentExecution() *TestExecution {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.currentExecution != nil {
		// Return a copy to avoid race conditions
		execution := *s.currentExecution
		return &execution
	}

	return nil
}

// GetExecutionHistory returns the execution history (placeholder)
func (s *DefaultScheduler) GetExecutionHistory(ctx context.Context, limit int) ([]*TestExecution, error) {
	// This would query the database for execution history
	// For now, return empty slice
	return []*TestExecution{}, nil
}

// CancelCurrentExecution cancels the current test execution if any
func (s *DefaultScheduler) CancelCurrentExecution(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.currentExecution == nil {
		return fmt.Errorf("no test execution is currently running")
	}

	if s.currentExecution.Status != "running" {
		return fmt.Errorf("current execution is not in running state")
	}

	s.logger.WithField("execution_id", s.currentExecution.ID).Info("Cancelling current test execution")

	// 实现取消逻辑（当前为占位符实现）
	// 在实际部署时，这里会取消传递给协调器的上下文

	// Update execution status
	endTime := time.Now().In(s.timezone)
	s.currentExecution.EndTime = &endTime
	s.currentExecution.Status = "cancelled"

	// Store the cancelled execution record
	if s.repository != nil {
		if err := s.storeExecutionRecord(ctx, s.currentExecution); err != nil {
			s.logger.WithError(err).Warn("Failed to store cancelled execution record")
		}
	}

	s.currentExecution = nil

	return nil
}

// GetScheduleInfo returns detailed schedule information
func (s *DefaultScheduler) GetScheduleInfo() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	info := map[string]interface{}{
		"mode":               s.config.Mode,
		"mode_description":   s.formatScheduleMode(),
		"timezone":           s.config.Timezone,
		"is_running":         s.isRunning,
		"current_hour":       s.getCurrentHourInTimezone(),
		"should_run_current": s.shouldRunTest(s.getCurrentHourInTimezone()),
		"next_test_time":     s.calculateNextTestTime(),
		"time_until_next":    s.getTimeUntilNextTest(),
		"total_tests":        s.totalTests,
		"successful_tests":   s.successfulTests,
		"failed_tests":       s.failedTests,
	}

	if s.lastTestTime != nil {
		info["last_test_time"] = s.lastTestTime
	}

	if s.lastError != nil {
		info["last_error"] = s.lastError.Error()
	}

	if s.currentExecution != nil {
		info["current_execution"] = s.currentExecution
	}

	return info
}
