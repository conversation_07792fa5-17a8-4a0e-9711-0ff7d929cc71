package database

import (
	"fmt"
	"time"
)

// Server 表示服务器客户端配置
type Server struct {
	ID        int       `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Host      string    `json:"host" db:"host"`
	Port      int       `json:"port" db:"port"`
	Enabled   bool      `json:"enabled" db:"enabled"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// HourlyResult 表示特定小时的测试结果
type HourlyResult struct {
	ID           int       `json:"id" db:"id"`
	ServerName   string    `json:"server_name" db:"server_name"`
	TestHour     time.Time `json:"test_hour" db:"test_hour"`
	TCPSpeedMbps *float64  `json:"tcp_speed_mbps" db:"tcp_speed_mbps"`
	UDPSpeedMbps *float64  `json:"udp_speed_mbps" db:"udp_speed_mbps"`
	TCPStatus    string    `json:"tcp_status" db:"tcp_status"`
	UDPStatus    string    `json:"udp_status" db:"udp_status"`
	TCPError     *string   `json:"tcp_error" db:"tcp_error"`
	UDPError     *string   `json:"udp_error" db:"udp_error"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// SyncStatus 表示与对等 OpenWRT 的同步状态
type SyncStatus struct {
	ID           int        `json:"id" db:"id"`
	PeerIP       string     `json:"peer_ip" db:"peer_ip"`
	LastSync     *time.Time `json:"last_sync" db:"last_sync"`
	SyncStatus   string     `json:"sync_status" db:"sync_status"`
	ErrorMessage *string    `json:"error_message" db:"error_message"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
}

// SystemConfig 表示系统配置键值对
type SystemConfig struct {
	Key         string    `json:"key" db:"key"`
	Value       string    `json:"value" db:"value"`
	Description *string   `json:"description" db:"description"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TestStatus 常量
const (
	StatusPending = "pending" // 待处理
	StatusSuccess = "success" // 成功
	StatusFailed  = "failed"  // 失败
	StatusTimeout = "timeout" // 超时
)

// Sync status 常量
const (
	SyncStatusPending = "pending" // 待处理
	SyncStatusSuccess = "success" // 成功
	SyncStatusFailed  = "failed"  // 失败
)

// System config keys 系统配置键
const (
	ConfigScheduleMode = "schedule_mode" // 调度模式
	ConfigPeerIP       = "peer_ip"       // 对等 IP
	ConfigPeerPort     = "peer_port"     // 对等端口
	ConfigWebPort      = "web_port"      // Web 端口
)

// ServerSummary 表示聚合的服务器统计信息
type ServerSummary struct {
	ServerName      string     `json:"server_name"`
	LastTestTime    *time.Time `json:"last_test_time"`
	LastTCPSpeed    *float64   `json:"last_tcp_speed"`
	LastUDPSpeed    *float64   `json:"last_udp_speed"`
	TCPStatus       string     `json:"tcp_status"`
	UDPStatus       string     `json:"udp_status"`
	TestsToday      int        `json:"tests_today"`
	SuccessfulTests int        `json:"successful_tests"`
	AvgTCPSpeed24h  *float64   `json:"avg_tcp_speed_24h"`
	AvgUDPSpeed24h  *float64   `json:"avg_udp_speed_24h"`
}

// HourlyStats 表示用于图表的每小时统计信息
type HourlyStats struct {
	Hour         time.Time `json:"hour"`
	ServerName   string    `json:"server_name"`
	TCPSpeedMbps *float64  `json:"tcp_speed_mbps"`
	UDPSpeedMbps *float64  `json:"udp_speed_mbps"`
	TCPStatus    string    `json:"tcp_status"`
	UDPStatus    string    `json:"udp_status"`
}

// SyncData 表示要在 OpenWRT 设备之间同步的数据
type SyncData struct {
	Timestamp     time.Time      `json:"timestamp"`
	HourlyResults []HourlyResult `json:"hourly_results"`
	Servers       []Server       `json:"servers"`
	LastSyncTime  time.Time      `json:"last_sync_time"`
	SourceIP      string         `json:"source_ip"`
}

// DatabaseStats 表示数据库统计信息
type DatabaseStats struct {
	TotalServers    int        `json:"total_servers"`
	EnabledServers  int        `json:"enabled_servers"`
	TotalResults    int        `json:"total_results"`
	ResultsToday    int        `json:"results_today"`
	LastTestTime    *time.Time `json:"last_test_time"`
	DatabaseSize    int64      `json:"database_size_bytes"`
	OldestResult    *time.Time `json:"oldest_result"`
	SyncStatusCount int        `json:"sync_status_count"`
}

// Validation methods 验证方法

// Validate 验证 Server 字段
func (s *Server) Validate() error {
	if s.Name == "" {
		return ErrInvalidServerName
	}
	if s.Host == "" {
		return ErrInvalidServerHost
	}
	if s.Port <= 0 || s.Port > 65535 {
		return ErrInvalidServerPort
	}
	return nil
}

// Validate 验证 HourlyResult 字段
func (hr *HourlyResult) Validate() error {
	if hr.ServerName == "" {
		return ErrInvalidServerName
	}
	if hr.TestHour.IsZero() {
		return ErrInvalidTestHour
	}
	if hr.TCPStatus != StatusPending && hr.TCPStatus != StatusSuccess &&
		hr.TCPStatus != StatusFailed && hr.TCPStatus != StatusTimeout {
		return ErrInvalidStatus
	}
	if hr.UDPStatus != StatusPending && hr.UDPStatus != StatusSuccess &&
		hr.UDPStatus != StatusFailed && hr.UDPStatus != StatusTimeout {
		return ErrInvalidStatus
	}
	return nil
}

// Custom errors 自定义错误
var (
	ErrInvalidServerName = fmt.Errorf("服务器名称无效")
	ErrInvalidServerHost = fmt.Errorf("服务器主机无效")
	ErrInvalidServerPort = fmt.Errorf("服务器端口无效")
	ErrInvalidTestHour   = fmt.Errorf("测试时间无效")
	ErrInvalidStatus     = fmt.Errorf("状态无效")
	ErrServerNotFound    = fmt.Errorf("未找到服务器")
	ErrResultNotFound    = fmt.Errorf("未找到结果")
	ErrDuplicateResult   = fmt.Errorf("服务器和时间重复结果")
)
