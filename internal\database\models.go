package database

import (
	"fmt"
	"time"
)

// Server represents a server client configuration
type Server struct {
	ID        int       `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Host      string    `json:"host" db:"host"`
	Port      int       `json:"port" db:"port"`
	Enabled   bool      `json:"enabled" db:"enabled"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// HourlyResult represents test results for a specific hour
type HourlyResult struct {
	ID           int       `json:"id" db:"id"`
	ServerName   string    `json:"server_name" db:"server_name"`
	TestHour     time.Time `json:"test_hour" db:"test_hour"`
	TCPSpeedMbps *float64  `json:"tcp_speed_mbps" db:"tcp_speed_mbps"`
	UDPSpeedMbps *float64  `json:"udp_speed_mbps" db:"udp_speed_mbps"`
	TCPStatus    string    `json:"tcp_status" db:"tcp_status"`
	UDPStatus    string    `json:"udp_status" db:"udp_status"`
	TCPError     *string   `json:"tcp_error" db:"tcp_error"`
	UDPError     *string   `json:"udp_error" db:"udp_error"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// SyncStatus represents synchronization status with peer OpenWRT
type SyncStatus struct {
	ID           int        `json:"id" db:"id"`
	PeerIP       string     `json:"peer_ip" db:"peer_ip"`
	LastSync     *time.Time `json:"last_sync" db:"last_sync"`
	SyncStatus   string     `json:"sync_status" db:"sync_status"`
	ErrorMessage *string    `json:"error_message" db:"error_message"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
}

// SystemConfig represents system configuration key-value pairs
type SystemConfig struct {
	Key         string    `json:"key" db:"key"`
	Value       string    `json:"value" db:"value"`
	Description *string   `json:"description" db:"description"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TestStatus constants
const (
	StatusPending = "pending"
	StatusSuccess = "success"
	StatusFailed  = "failed"
	StatusTimeout = "timeout"
)

// Sync status constants
const (
	SyncStatusPending = "pending"
	SyncStatusSuccess = "success"
	SyncStatusFailed  = "failed"
)

// System config keys
const (
	ConfigScheduleMode = "schedule_mode"
	ConfigPeerIP       = "peer_ip"
	ConfigPeerPort     = "peer_port"
	ConfigWebPort      = "web_port"
)

// ServerSummary represents aggregated server statistics
type ServerSummary struct {
	ServerName      string     `json:"server_name"`
	LastTestTime    *time.Time `json:"last_test_time"`
	LastTCPSpeed    *float64   `json:"last_tcp_speed"`
	LastUDPSpeed    *float64   `json:"last_udp_speed"`
	TCPStatus       string     `json:"tcp_status"`
	UDPStatus       string     `json:"udp_status"`
	TestsToday      int        `json:"tests_today"`
	SuccessfulTests int        `json:"successful_tests"`
	AvgTCPSpeed24h  *float64   `json:"avg_tcp_speed_24h"`
	AvgUDPSpeed24h  *float64   `json:"avg_udp_speed_24h"`
}

// HourlyStats represents hourly statistics for charts
type HourlyStats struct {
	Hour         time.Time `json:"hour"`
	ServerName   string    `json:"server_name"`
	TCPSpeedMbps *float64  `json:"tcp_speed_mbps"`
	UDPSpeedMbps *float64  `json:"udp_speed_mbps"`
	TCPStatus    string    `json:"tcp_status"`
	UDPStatus    string    `json:"udp_status"`
}

// SyncData represents data to be synchronized between OpenWRT devices
type SyncData struct {
	Timestamp     time.Time      `json:"timestamp"`
	HourlyResults []HourlyResult `json:"hourly_results"`
	Servers       []Server       `json:"servers"`
	LastSyncTime  time.Time      `json:"last_sync_time"`
	SourceIP      string         `json:"source_ip"`
}

// DatabaseStats represents database statistics
type DatabaseStats struct {
	TotalServers    int        `json:"total_servers"`
	EnabledServers  int        `json:"enabled_servers"`
	TotalResults    int        `json:"total_results"`
	ResultsToday    int        `json:"results_today"`
	LastTestTime    *time.Time `json:"last_test_time"`
	DatabaseSize    int64      `json:"database_size_bytes"`
	OldestResult    *time.Time `json:"oldest_result"`
	SyncStatusCount int        `json:"sync_status_count"`
}

// Validation methods

// Validate validates Server fields
func (s *Server) Validate() error {
	if s.Name == "" {
		return ErrInvalidServerName
	}
	if s.Host == "" {
		return ErrInvalidServerHost
	}
	if s.Port <= 0 || s.Port > 65535 {
		return ErrInvalidServerPort
	}
	return nil
}

// Validate validates HourlyResult fields
func (hr *HourlyResult) Validate() error {
	if hr.ServerName == "" {
		return ErrInvalidServerName
	}
	if hr.TestHour.IsZero() {
		return ErrInvalidTestHour
	}
	if hr.TCPStatus != StatusPending && hr.TCPStatus != StatusSuccess &&
		hr.TCPStatus != StatusFailed && hr.TCPStatus != StatusTimeout {
		return ErrInvalidStatus
	}
	if hr.UDPStatus != StatusPending && hr.UDPStatus != StatusSuccess &&
		hr.UDPStatus != StatusFailed && hr.UDPStatus != StatusTimeout {
		return ErrInvalidStatus
	}
	return nil
}

// Custom errors
var (
	ErrInvalidServerName = fmt.Errorf("invalid server name")
	ErrInvalidServerHost = fmt.Errorf("invalid server host")
	ErrInvalidServerPort = fmt.Errorf("invalid server port")
	ErrInvalidTestHour   = fmt.Errorf("invalid test hour")
	ErrInvalidStatus     = fmt.Errorf("invalid status")
	ErrServerNotFound    = fmt.Errorf("server not found")
	ErrResultNotFound    = fmt.Errorf("result not found")
	ErrDuplicateResult   = fmt.Errorf("duplicate result for server and hour")
)
