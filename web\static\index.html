<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iperf3 控制器 - 网络性能测试管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background: #f44336;
        }
        
        .status-warning {
            background: #ff9800;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
            color: #666;
        }
        
        .metric-value {
            font-weight: bold;
            color: #333;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
        }
        
        .clients-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .client-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #667eea;
        }
        
        .client-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .client-info {
            font-size: 0.9em;
            color: #666;
        }
        
        .performance-chart {
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin-top: 15px;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 iperf3 控制器</h1>
            <p>网络性能测试管理系统 - 抢占式测试架构</p>
        </div>
        
        <div class="dashboard">
            <!-- 系统状态卡片 -->
            <div class="card">
                <h3>📊 系统状态</h3>
                <div class="metric">
                    <span class="metric-label">调度器</span>
                    <span class="metric-value">
                        <span class="status-indicator status-stopped" id="scheduler-status"></span>
                        <span id="scheduler-text">已停止</span>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">同步服务</span>
                    <span class="metric-value">
                        <span class="status-indicator status-stopped" id="sync-status"></span>
                        <span id="sync-text">已停止</span>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">对端连接</span>
                    <span class="metric-value">
                        <span class="status-indicator status-warning" id="peer-status"></span>
                        <span id="peer-text">未连接</span>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">数据同步</span>
                    <span class="metric-value" id="data-sync-status">未同步</span>
                </div>
            </div>
            
            <!-- 测试统计卡片 -->
            <div class="card">
                <h3>📈 测试统计</h3>
                <div class="metric">
                    <span class="metric-label">总测试次数</span>
                    <span class="metric-value" id="total-tests">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功次数</span>
                    <span class="metric-value" id="successful-tests">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">失败次数</span>
                    <span class="metric-value" id="failed-tests">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value" id="success-rate">0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">下次测试</span>
                    <span class="metric-value" id="next-test-time">--</span>
                </div>
            </div>
            
            <!-- 性能指标卡片 -->
            <div class="card">
                <h3>⚡ 性能指标</h3>
                <div class="metric">
                    <span class="metric-label">平均TCP速度</span>
                    <span class="metric-value" id="avg-tcp-speed">-- Mbps</span>
                </div>
                <div class="metric">
                    <span class="metric-label">平均UDP速度</span>
                    <span class="metric-value" id="avg-udp-speed">-- Mbps</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最大TCP速度</span>
                    <span class="metric-value" id="max-tcp-speed">-- Mbps</span>
                </div>
                <div class="metric">
                    <span class="metric-label">平均延迟</span>
                    <span class="metric-value" id="avg-latency">-- ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">丢包率</span>
                    <span class="metric-value" id="packet-loss">-- %</span>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="card">
            <h3>🎮 控制面板</h3>
            <div style="text-align: center;">
                <button class="btn" onclick="startScheduler()">启动调度器</button>
                <button class="btn btn-danger" onclick="stopScheduler()">停止调度器</button>
                <button class="btn btn-success" onclick="triggerTest()">手动测试</button>
                <button class="btn" onclick="startSync()">启动同步</button>
                <button class="btn btn-danger" onclick="stopSync()">停止同步</button>
                <button class="btn" onclick="refreshData()">刷新数据</button>
            </div>
            <div id="alert-container"></div>
        </div>
        
        <!-- 客户端状态 -->
        <div class="card">
            <h3>🖥️ 客户端状态</h3>
            <div class="metric">
                <span class="metric-label">总客户端数</span>
                <span class="metric-value" id="total-clients">0</span>
            </div>
            <div class="metric">
                <span class="metric-label">启用客户端</span>
                <span class="metric-value" id="enabled-clients">0</span>
            </div>
            <div class="clients-grid" id="clients-grid">
                <!-- 客户端列表将在这里动态加载 -->
            </div>
        </div>
        
        <!-- 性能图表 -->
        <div class="card">
            <h3>📊 性能趋势</h3>
            <div class="performance-chart">
                <div>性能图表将在这里显示<br>（需要图表库支持）</div>
            </div>
        </div>
        
        <div class="footer">
            <p>iperf3 控制器 v1.0.0 | 抢占式测试架构 | 双OpenWRT同步</p>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
