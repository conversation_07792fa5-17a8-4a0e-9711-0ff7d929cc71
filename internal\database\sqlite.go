package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
	_ "modernc.org/sqlite" // SQLite driver
)

// DB wraps the sql.DB with additional functionality
type DB struct {
	*sql.DB
	logger   *logrus.Logger
	dbPath   string
	migrator *Migrator
}

// Config holds database configuration
type Config struct {
	Path            string        `yaml:"path"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time"`
	BusyTimeout     time.Duration `yaml:"busy_timeout"`
	JournalMode     string        `yaml:"journal_mode"`
	Synchronous     string        `yaml:"synchronous"`
	CacheSize       int           `yaml:"cache_size"`
}

// DefaultConfig returns default database configuration
func DefaultConfig() *Config {
	return &Config{
		Path:            "/opt/iperf3-controller/data.db",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 15,
		BusyTimeout:     time.Second * 30,
		JournalMode:     "WAL",
		Synchronous:     "NORMAL",
		CacheSize:       -64000, // 64MB cache
	}
}

// New creates a new database connection
func New(config *Config, logger *logrus.Logger) (*DB, error) {
	if config == nil {
		config = DefaultConfig()
	}

	if logger == nil {
		logger = logrus.New()
	}

	// Ensure directory exists
	if err := os.MkdirAll(filepath.Dir(config.Path), 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Build connection string with pragmas
	dsn := fmt.Sprintf("%s?_busy_timeout=%d&_journal_mode=%s&_synchronous=%s&_cache_size=%d&_foreign_keys=on",
		config.Path,
		int(config.BusyTimeout.Milliseconds()),
		config.JournalMode,
		config.Synchronous,
		config.CacheSize,
	)

	// Open database connection
	sqlDB, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		sqlDB.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	db := &DB{
		DB:     sqlDB,
		logger: logger,
		dbPath: config.Path,
	}

	// Initialize migrator
	db.migrator = NewMigrator(sqlDB, logger)

	logger.WithFields(logrus.Fields{
		"path":             config.Path,
		"max_open_conns":   config.MaxOpenConns,
		"max_idle_conns":   config.MaxIdleConns,
		"journal_mode":     config.JournalMode,
		"synchronous":      config.Synchronous,
		"cache_size":       config.CacheSize,
	}).Info("Database connection established")

	return db, nil
}

// Migrate runs database migrations
func (db *DB) Migrate() error {
	return db.migrator.Migrate()
}

// Close closes the database connection
func (db *DB) Close() error {
	db.logger.Info("Closing database connection")
	return db.DB.Close()
}

// GetStats returns database statistics
func (db *DB) GetStats() (*DatabaseStats, error) {
	stats := &DatabaseStats{}

	// Get total servers
	err := db.QueryRow("SELECT COUNT(*) FROM servers").Scan(&stats.TotalServers)
	if err != nil {
		return nil, fmt.Errorf("failed to get total servers: %w", err)
	}

	// Get enabled servers
	err = db.QueryRow("SELECT COUNT(*) FROM servers WHERE enabled = 1").Scan(&stats.EnabledServers)
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled servers: %w", err)
	}

	// Get total results
	err = db.QueryRow("SELECT COUNT(*) FROM hourly_results").Scan(&stats.TotalResults)
	if err != nil {
		return nil, fmt.Errorf("failed to get total results: %w", err)
	}

	// Get results today
	today := time.Now().Format("2006-01-02")
	err = db.QueryRow("SELECT COUNT(*) FROM hourly_results WHERE DATE(test_hour) = ?", today).Scan(&stats.ResultsToday)
	if err != nil {
		return nil, fmt.Errorf("failed to get results today: %w", err)
	}

	// Get last test time
	var lastTestTime sql.NullTime
	err = db.QueryRow("SELECT MAX(test_hour) FROM hourly_results").Scan(&lastTestTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get last test time: %w", err)
	}
	if lastTestTime.Valid {
		stats.LastTestTime = &lastTestTime.Time
	}

	// Get oldest result
	var oldestResult sql.NullTime
	err = db.QueryRow("SELECT MIN(test_hour) FROM hourly_results").Scan(&oldestResult)
	if err != nil {
		return nil, fmt.Errorf("failed to get oldest result: %w", err)
	}
	if oldestResult.Valid {
		stats.OldestResult = &oldestResult.Time
	}

	// Get database file size
	if fileInfo, err := os.Stat(db.dbPath); err == nil {
		stats.DatabaseSize = fileInfo.Size()
	}

	// Get sync status count
	err = db.QueryRow("SELECT COUNT(*) FROM sync_status").Scan(&stats.SyncStatusCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get sync status count: %w", err)
	}

	return stats, nil
}

// Vacuum performs database maintenance
func (db *DB) Vacuum() error {
	db.logger.Info("Starting database vacuum")
	start := time.Now()

	if _, err := db.Exec("VACUUM"); err != nil {
		return fmt.Errorf("failed to vacuum database: %w", err)
	}

	duration := time.Since(start)
	db.logger.WithField("duration", duration).Info("Database vacuum completed")
	return nil
}

// Backup creates a backup of the database
func (db *DB) Backup(backupPath string) error {
	db.logger.WithField("backup_path", backupPath).Info("Starting database backup")

	// Ensure backup directory exists
	if err := os.MkdirAll(filepath.Dir(backupPath), 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// Use SQLite backup API
	query := fmt.Sprintf("VACUUM INTO '%s'", backupPath)
	if _, err := db.Exec(query); err != nil {
		return fmt.Errorf("failed to backup database: %w", err)
	}

	db.logger.WithField("backup_path", backupPath).Info("Database backup completed")
	return nil
}

// CleanupOldData removes old data based on retention policy
func (db *DB) CleanupOldData(retentionDays int) error {
	if retentionDays <= 0 {
		return fmt.Errorf("retention days must be positive")
	}

	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	db.logger.WithFields(logrus.Fields{
		"retention_days": retentionDays,
		"cutoff_date":    cutoffDate,
	}).Info("Starting data cleanup")

	// Clean up old hourly results
	result, err := db.Exec("DELETE FROM hourly_results WHERE test_hour < ?", cutoffDate)
	if err != nil {
		return fmt.Errorf("failed to cleanup hourly results: %w", err)
	}

	deletedResults, _ := result.RowsAffected()

	// Clean up old sync status records
	result, err = db.Exec("DELETE FROM sync_status WHERE created_at < ?", cutoffDate)
	if err != nil {
		return fmt.Errorf("failed to cleanup sync status: %w", err)
	}

	deletedSyncStatus, _ := result.RowsAffected()

	db.logger.WithFields(logrus.Fields{
		"deleted_results":     deletedResults,
		"deleted_sync_status": deletedSyncStatus,
	}).Info("Data cleanup completed")

	return nil
}

// Health checks database health
func (db *DB) Health() error {
	// Check if database is accessible
	if err := db.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	// Check if tables exist
	tables := []string{"servers", "hourly_results", "sync_status", "system_config", "schema_migrations"}
	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='%s'", table)).Scan(&count)
		if err != nil {
			return fmt.Errorf("failed to check table %s: %w", table, err)
		}
		if count == 0 {
			return fmt.Errorf("table %s does not exist", table)
		}
	}

	return nil
}

// GetMigrationStatus returns migration status
func (db *DB) GetMigrationStatus() ([]MigrationStatus, error) {
	return db.migrator.GetMigrationStatus()
}
