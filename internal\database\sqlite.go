package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
	_ "modernc.org/sqlite" // SQLite driver
)

// DB 封装了 sql.DB 并增加了额外功能
type DB struct {
	*sql.DB
	logger   *logrus.Logger
	dbPath   string
	migrator *Migrator
}

// Config 包含数据库配置
type Config struct {
	Path            string        `yaml:"path"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time"`
	BusyTimeout     time.Duration `yaml:"busy_timeout"`
	JournalMode     string        `yaml:"journal_mode"`
	Synchronous     string        `yaml:"synchronous"`
	CacheSize       int           `yaml:"cache_size"`
}

// DefaultConfig 返回默认数据库配置
func DefaultConfig() *Config {
	return &Config{
		Path:            "/opt/iperf3-controller/data.db",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 15,
		BusyTimeout:     time.Second * 30,
		JournalMode:     "WAL",
		Synchronous:     "NORMAL",
		CacheSize:       -64000, // 64MB 缓存
	}
}

// New 创建一个新的数据库连接
func New(config *Config, logger *logrus.Logger) (*DB, error) {
	if config == nil {
		config = DefaultConfig()
	}

	if logger == nil {
		logger = logrus.New()
	}

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(config.Path), 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %w", err)
	}

	// 使用 pragmas 构建连接字符串
	dsn := fmt.Sprintf("%s?_busy_timeout=%d&_journal_mode=%s&_synchronous=%s&_cache_size=%d&_foreign_keys=on",
		config.Path,
		int(config.BusyTimeout.Milliseconds()),
		config.JournalMode,
		config.Synchronous,
		config.CacheSize,
	)

	// 打开数据库连接
	sqlDB, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		sqlDB.Close()
		return nil, fmt.Errorf("ping 数据库失败: %w", err)
	}

	db := &DB{
		DB:     sqlDB,
		logger: logger,
		dbPath: config.Path,
	}

	// 初始化迁移器
	db.migrator = NewMigrator(sqlDB, logger)

	logger.WithFields(logrus.Fields{
		"path":           config.Path,
		"max_open_conns": config.MaxOpenConns,
		"max_idle_conns": config.MaxIdleConns,
		"journal_mode":   config.JournalMode,
		"synchronous":    config.Synchronous,
		"cache_size":     config.CacheSize,
	}).Info("数据库连接已建立")

	return db, nil
}

// Migrate 运行数据库迁移
func (db *DB) Migrate() error {
	return db.migrator.Migrate()
}

// Close 关闭数据库连接
func (db *DB) Close() error {
	db.logger.Info("正在关闭数据库连接")
	return db.DB.Close()
}

// GetStats 返回数据库统计信息
func (db *DB) GetStats() (*DatabaseStats, error) {
	stats := &DatabaseStats{}

	// 获取总服务器数
	err := db.QueryRow("SELECT COUNT(*) FROM servers").Scan(&stats.TotalServers)
	if err != nil {
		return nil, fmt.Errorf("获取总服务器数失败: %w", err)
	}

	// 获取已启用服务器数
	err = db.QueryRow("SELECT COUNT(*) FROM servers WHERE enabled = 1").Scan(&stats.EnabledServers)
	if err != nil {
		return nil, fmt.Errorf("获取已启用服务器数失败: %w", err)
	}

	// 获取总结果数
	err = db.QueryRow("SELECT COUNT(*) FROM hourly_results").Scan(&stats.TotalResults)
	if err != nil {
		return nil, fmt.Errorf("获取总结果数失败: %w", err)
	}

	// 获取今天的结果数
	today := time.Now().Format("2006-01-02")
	err = db.QueryRow("SELECT COUNT(*) FROM hourly_results WHERE DATE(test_hour) = ?", today).Scan(&stats.ResultsToday)
	if err != nil {
		return nil, fmt.Errorf("获取今天结果数失败: %w", err)
	}

	// 获取最后测试时间
	var lastTestTime sql.NullTime
	err = db.QueryRow("SELECT MAX(test_hour) FROM hourly_results").Scan(&lastTestTime)
	if err != nil {
		return nil, fmt.Errorf("获取最后测试时间失败: %w", err)
	}
	if lastTestTime.Valid {
		stats.LastTestTime = &lastTestTime.Time
	}

	// 获取最旧结果
	var oldestResult sql.NullTime
	err = db.QueryRow("SELECT MIN(test_hour) FROM hourly_results").Scan(&oldestResult)
	if err != nil {
		return nil, fmt.Errorf("获取最旧结果失败: %w", err)
	}
	if oldestResult.Valid {
		stats.OldestResult = &oldestResult.Time
	}

	// 获取数据库文件大小
	if fileInfo, err := os.Stat(db.dbPath); err == nil {
		stats.DatabaseSize = fileInfo.Size()
	}

	// 获取同步状态计数
	err = db.QueryRow("SELECT COUNT(*) FROM sync_status").Scan(&stats.SyncStatusCount)
	if err != nil {
		return nil, fmt.Errorf("获取同步状态计数失败: %w", err)
	}

	return stats, nil
}

// Vacuum 执行数据库维护
func (db *DB) Vacuum() error {
	db.logger.Info("开始数据库清理")
	start := time.Now()

	if _, err := db.Exec("VACUUM"); err != nil {
		return fmt.Errorf("清理数据库失败: %w", err)
	}

	duration := time.Since(start)
	db.logger.WithField("duration", duration).Info("数据库清理完成")
	return nil
}

// Backup 创建数据库备份
func (db *DB) Backup(backupPath string) error {
	db.logger.WithField("backup_path", backupPath).Info("开始数据库备份")

	// 确保备份目录存在
	if err := os.MkdirAll(filepath.Dir(backupPath), 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 使用 SQLite 备份 API
	query := fmt.Sprintf("VACUUM INTO '%s'", backupPath)
	if _, err := db.Exec(query); err != nil {
		return fmt.Errorf("备份数据库失败: %w", err)
	}

	db.logger.WithField("backup_path", backupPath).Info("数据库备份完成")
	return nil
}

// CleanupOldData 根据保留策略删除旧数据
func (db *DB) CleanupOldData(retentionDays int) error {
	if retentionDays <= 0 {
		return fmt.Errorf("保留天数必须为正数")
	}

	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	db.logger.WithFields(logrus.Fields{
		"retention_days": retentionDays,
		"cutoff_date":    cutoffDate,
	}).Info("开始数据清理")

	// 清理旧的每小时结果
	result, err := db.Exec("DELETE FROM hourly_results WHERE test_hour < ?", cutoffDate)
	if err != nil {
		return fmt.Errorf("清理每小时结果失败: %w", err)
	}

	deletedResults, _ := result.RowsAffected()

	// 清理旧的同步状态记录
	result, err = db.Exec("DELETE FROM sync_status WHERE created_at < ?", cutoffDate)
	if err != nil {
		return fmt.Errorf("清理同步状态失败: %w", err)
	}

	deletedSyncStatus, _ := result.RowsAffected()

	db.logger.WithFields(logrus.Fields{
		"deleted_results":     deletedResults,
		"deleted_sync_status": deletedSyncStatus,
	}).Info("数据清理完成")

	return nil
}

// Health 检查数据库健康状况
func (db *DB) Health() error {
	// 检查数据库是否可访问
	if err := db.Ping(); err != nil {
		return fmt.Errorf("数据库 ping 失败: %w", err)
	}

	// 检查表是否存在
	tables := []string{"servers", "hourly_results", "sync_status", "system_config", "schema_migrations"}
	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='%s'", table)).Scan(&count)
		if err != nil {
			return fmt.Errorf("检查表 %s 失败: %w", table, err)
		}
		if count == 0 {
			return fmt.Errorf("表 %s 不存在", table)
		}
	}

	return nil
}

// GetMigrationStatus 返回迁移状态
func (db *DB) GetMigrationStatus() ([]MigrationStatus, error) {
	return db.migrator.GetMigrationStatus()
}
