package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

// 颜色定义
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorGreen  = "\033[32m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorPurple = "\033[35m"
	ColorCyan   = "\033[36m"
	ColorWhite  = "\033[37m"
)

// TestSuite 测试套件
type TestSuite struct {
	Name        string
	Description string
	TestFile    string
	Functions   []string
}

// TestResult 测试结果
type TestResult struct {
	Suite    string
	Function string
	Status   string
	Duration time.Duration
	Output   string
}

func main() {
	fmt.Printf("%s🚀 iperf3-controller 完整测试套件%s\n", ColorBlue, ColorReset)
	fmt.Printf("%s========================================%s\n", ColorBlue, ColorReset)
	fmt.Println()

	// 定义测试套件
	testSuites := []TestSuite{
		{
			Name:        "客户端管理器测试",
			Description: "测试客户端的添加、删除、查询和状态管理",
			TestFile:    "client_manager_test.go",
			Functions: []string{
				"TestClientManager",
				"BenchmarkClientManager",
			},
		},
		{
			Name:        "测试协调器测试",
			Description: "测试抢占式测试协调和并发控制",
			TestFile:    "coordinator_test.go",
			Functions: []string{
				"TestCoordinator",
				"TestCoordinatorConcurrency",
				"BenchmarkCoordinator",
			},
		},
		{
			Name:        "调度器测试",
			Description: "测试奇数/偶数小时调度和时区处理",
			TestFile:    "scheduler_test.go",
			Functions: []string{
				"TestScheduler",
				"BenchmarkScheduler",
			},
		},
		{
			Name:        "同步管理器测试",
			Description: "测试双OpenWRT数据同步功能",
			TestFile:    "sync_test.go",
			Functions: []string{
				"TestSyncManager",
				"BenchmarkSyncManager",
			},
		},
		{
			Name:        "API服务器测试",
			Description: "测试RESTful API接口和Web服务",
			TestFile:    "api_test.go",
			Functions: []string{
				"TestAPIServer",
				"TestAPIServerConcurrency",
				"BenchmarkAPIServer",
			},
		},
	}

	var allResults []TestResult
	totalTests := 0
	passedTests := 0
	failedTests := 0

	startTime := time.Now()

	// 运行每个测试套件
	for _, suite := range testSuites {
		fmt.Printf("%s📋 运行测试套件: %s%s\n", ColorCyan, suite.Name, ColorReset)
		fmt.Printf("%s   描述: %s%s\n", ColorWhite, suite.Description, ColorReset)
		fmt.Printf("%s   文件: %s%s\n", ColorWhite, suite.TestFile, ColorReset)
		fmt.Println()

		// 运行测试套件中的每个测试函数
		for _, function := range suite.Functions {
			totalTests++
			result := runTest(suite.TestFile, function)
			result.Suite = suite.Name
			result.Function = function
			allResults = append(allResults, result)

			if result.Status == "PASS" {
				passedTests++
				fmt.Printf("%s   ✅ %s - 通过 (%v)%s\n", ColorGreen, function, result.Duration, ColorReset)
			} else {
				failedTests++
				fmt.Printf("%s   ❌ %s - 失败 (%v)%s\n", ColorRed, function, result.Duration, ColorReset)
				if result.Output != "" {
					fmt.Printf("%s      错误信息: %s%s\n", ColorRed, result.Output, ColorReset)
				}
			}
		}
		fmt.Println()
	}

	totalDuration := time.Since(startTime)

	// 显示测试结果汇总
	fmt.Printf("%s========================================%s\n", ColorBlue, ColorReset)
	fmt.Printf("%s📊 测试结果汇总%s\n", ColorBlue, ColorReset)
	fmt.Printf("%s========================================%s\n", ColorBlue, ColorReset)
	fmt.Printf("总测试数: %s%d%s\n", ColorWhite, totalTests, ColorReset)
	fmt.Printf("通过测试: %s%d%s\n", ColorGreen, passedTests, ColorReset)
	fmt.Printf("失败测试: %s%d%s\n", ColorRed, failedTests, ColorReset)
	fmt.Printf("总耗时: %s%v%s\n", ColorWhite, totalDuration, ColorReset)

	successRate := float64(passedTests) / float64(totalTests) * 100
	fmt.Printf("成功率: %s%.1f%%%s\n", ColorGreen, successRate, ColorReset)
	fmt.Println()

	// 显示详细结果
	if failedTests > 0 {
		fmt.Printf("%s❌ 失败的测试:%s\n", ColorRed, ColorReset)
		for _, result := range allResults {
			if result.Status == "FAIL" {
				fmt.Printf("   - %s::%s\n", result.Suite, result.Function)
			}
		}
		fmt.Println()
	}

	// 显示性能测试结果
	fmt.Printf("%s⚡ 性能测试结果:%s\n", ColorYellow, ColorReset)
	for _, result := range allResults {
		if strings.HasPrefix(result.Function, "Benchmark") && result.Status == "PASS" {
			fmt.Printf("   - %s::%s - %v\n", result.Suite, result.Function, result.Duration)
		}
	}
	fmt.Println()

	// 显示测试覆盖率信息
	fmt.Printf("%s📈 测试覆盖的功能模块:%s\n", ColorPurple, ColorReset)
	modules := []string{
		"✅ 客户端管理 - 添加、删除、查询、状态管理",
		"✅ 抢占式测试协调 - 并发控制、资源管理",
		"✅ 智能调度 - 时间计算、模式切换、时区处理",
		"✅ 数据同步 - 双机同步、心跳检测、故障恢复",
		"✅ RESTful API - 接口响应、错误处理、并发安全",
		"✅ Web服务 - 路由处理、中间件、静态文件",
		"✅ 配置管理 - 参数验证、默认值处理",
		"✅ 错误处理 - 异常捕获、优雅降级",
		"✅ 并发安全 - 线程安全、资源竞争",
		"✅ 性能优化 - 响应时间、资源使用",
	}

	for _, module := range modules {
		fmt.Printf("   %s\n", module)
	}
	fmt.Println()

	// 最终结果
	if failedTests == 0 {
		fmt.Printf("%s🎉 所有测试通过！系统质量优秀，可以部署到生产环境！%s\n", ColorGreen, ColorReset)
		os.Exit(0)
	} else {
		fmt.Printf("%s💥 有 %d 个测试失败，请检查并修复问题%s\n", ColorRed, failedTests, ColorReset)
		os.Exit(1)
	}
}

// runTest 运行单个测试函数
func runTest(testFile, function string) TestResult {
	startTime := time.Now()

	// 构建go test命令
	cmd := exec.Command("go", "test", "-v", "-run", function, testFile)
	cmd.Dir = "."

	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime)

	result := TestResult{
		Duration: duration,
		Output:   string(output),
	}

	if err != nil {
		result.Status = "FAIL"
		// 提取错误信息
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "FAIL") || strings.Contains(line, "Error") {
				result.Output = strings.TrimSpace(line)
				break
			}
		}
	} else {
		result.Status = "PASS"
	}

	return result
}

// runCoverageTest 运行覆盖率测试
func runCoverageTest() {
	fmt.Printf("%s📊 运行测试覆盖率分析...%s\n", ColorYellow, ColorReset)

	cmd := exec.Command("go", "test", "-coverprofile=coverage.out", "./...")
	cmd.Dir = ".."

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("%s覆盖率测试失败: %v%s\n", ColorRed, err, ColorReset)
		return
	}

	fmt.Printf("%s覆盖率测试完成%s\n", ColorGreen, ColorReset)
	fmt.Printf("%s%s%s\n", ColorWhite, string(output), ColorReset)

	// 生成HTML覆盖率报告
	cmd = exec.Command("go", "tool", "cover", "-html=coverage.out", "-o", "coverage.html")
	cmd.Dir = ".."

	err = cmd.Run()
	if err != nil {
		fmt.Printf("%s生成HTML覆盖率报告失败: %v%s\n", ColorRed, err, ColorReset)
	} else {
		fmt.Printf("%s✅ HTML覆盖率报告已生成: coverage.html%s\n", ColorGreen, ColorReset)
	}
}

// runIntegrationTest 运行集成测试
func runIntegrationTest() {
	fmt.Printf("%s🔗 运行集成测试...%s\n", ColorYellow, ColorReset)

	cmd := exec.Command("go", "run", "../test_integration.go")
	cmd.Dir = "."

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("%s集成测试失败: %v%s\n", ColorRed, err, ColorReset)
		fmt.Printf("%s输出: %s%s\n", ColorWhite, string(output), ColorReset)
	} else {
		fmt.Printf("%s✅ 集成测试通过%s\n", ColorGreen, ColorReset)
		fmt.Printf("%s%s%s\n", ColorWhite, string(output), ColorReset)
	}
}

// checkTestEnvironment 检查测试环境
func checkTestEnvironment() bool {
	fmt.Printf("%s🔍 检查测试环境...%s\n", ColorYellow, ColorReset)

	// 检查Go版本
	cmd := exec.Command("go", "version")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("%s❌ Go未安装或不在PATH中%s\n", ColorRed, ColorReset)
		return false
	}
	fmt.Printf("%s✅ %s%s", ColorGreen, strings.TrimSpace(string(output)), ColorReset)

	// 检查依赖
	cmd = exec.Command("go", "mod", "tidy")
	cmd.Dir = ".."
	err = cmd.Run()
	if err != nil {
		fmt.Printf("%s❌ 依赖检查失败: %v%s\n", ColorRed, err, ColorReset)
		return false
	}
	fmt.Printf("%s✅ 依赖检查通过%s\n", ColorGreen, ColorReset)

	return true
}
