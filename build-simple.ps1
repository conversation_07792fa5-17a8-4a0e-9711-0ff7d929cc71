# iperf3-controller 简化构建脚本
# Windows PowerShell 版本

param(
    [string]$Platform = "windows-amd64",
    [switch]$Clean = $false
)

# 项目信息
$ProjectName = "iperf3-controller"
$Version = "1.0.0"
$BuildTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"

try {
    $GitCommit = git rev-parse --short HEAD 2>$null
} catch {
    $GitCommit = "unknown"
}

Write-Host "iperf3-controller Windows Build Script" -ForegroundColor Blue
Write-Host "Version: $Version" -ForegroundColor Blue
Write-Host "Build Time: $BuildTime" -ForegroundColor Blue
Write-Host "Git Commit: $GitCommit" -ForegroundColor Blue
Write-Host ""

# 构建目录
$BuildDir = "build"

# 清理旧文件
if ($Clean -or (Test-Path $BuildDir)) {
    Write-Host "Cleaning old build files..." -ForegroundColor Yellow
    if (Test-Path $BuildDir) { 
        Remove-Item -Recurse -Force $BuildDir 
    }
}

# 创建构建目录
New-Item -ItemType Directory -Force -Path $BuildDir | Out-Null

# 构建标志
$LdFlags = "-X main.Version=$Version -X main.BuildTime=$BuildTime -X main.GitCommit=$GitCommit -w -s"

# 检查Go环境
Write-Host "Checking Go environment..." -ForegroundColor Cyan
try {
    $GoVersion = go version
    Write-Host "[OK] Go environment: $GoVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Go not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# 检查依赖
Write-Host "Checking dependencies..." -ForegroundColor Cyan
try {
    go mod tidy
    Write-Host "[OK] Dependencies checked" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Dependency check failed: $_" -ForegroundColor Red
    exit 1
}

# 支持的平台
$Platforms = @{
    "windows-amd64" = @{OS="windows"; Arch="amd64"; Name="Windows 64-bit"}
    "linux-amd64" = @{OS="linux"; Arch="amd64"; Name="Linux 64-bit"}
    "linux-arm64" = @{OS="linux"; Arch="arm64"; Name="Linux ARM64"}
    "darwin-amd64" = @{OS="darwin"; Arch="amd64"; Name="macOS Intel"}
    "darwin-arm64" = @{OS="darwin"; Arch="arm64"; Name="macOS Apple Silicon"}
    "openwrt-mips" = @{OS="linux"; Arch="mips"; Name="OpenWRT MIPS"}
    "openwrt-mipsle" = @{OS="linux"; Arch="mipsle"; Name="OpenWRT MIPSLE"}
}

# 构建函数
function Build-Platform {
    param(
        [string]$OS,
        [string]$Arch,
        [string]$Name,
        [string]$PlatformKey
    )
    
    $OutputName = if ($PlatformKey.StartsWith("openwrt")) {
        "$ProjectName-$PlatformKey"
    } else {
        "$ProjectName-$OS-$Arch"
    }
    
    if ($OS -eq "windows") {
        $OutputName += ".exe"
    }
    
    Write-Host "Building $Name..." -ForegroundColor Cyan
    
    $env:GOOS = $OS
    $env:GOARCH = $Arch
    
    if ($PlatformKey.StartsWith("openwrt")) {
        $env:CGO_ENABLED = "0"
    }
    
    try {
        $BuildCmd = "go build -ldflags `"$LdFlags`" -o `"$BuildDir\$OutputName`" .\cmd\iperf3-controller"
        
        if ($PlatformKey.StartsWith("openwrt")) {
            $BuildCmd = "go build -tags=netgo -ldflags `"$LdFlags`" -o `"$BuildDir\$OutputName`" .\cmd\iperf3-controller"
        }
        
        Invoke-Expression $BuildCmd
        
        if ($LASTEXITCODE -eq 0) {
            $FileSize = (Get-Item "$BuildDir\$OutputName").Length
            $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
            Write-Host "[OK] $Name build successful ($FileSizeMB MB)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] $Name build failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] $Name build failed: $_" -ForegroundColor Red
        return $false
    } finally {
        # 清理环境变量
        Remove-Item Env:GOOS -ErrorAction SilentlyContinue
        Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
        Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue
    }
}

Write-Host "Starting build..." -ForegroundColor Cyan
Write-Host ""

$SuccessCount = 0
$TotalCount = 0

# 构建指定平台或所有平台
if ($Platform -eq "all") {
    foreach ($PlatformKey in $Platforms.Keys) {
        $p = $Platforms[$PlatformKey]
        $TotalCount++
        if (Build-Platform -OS $p.OS -Arch $p.Arch -Name $p.Name -PlatformKey $PlatformKey) {
            $SuccessCount++
        }
        Write-Host ""
    }
} else {
    if ($Platforms.ContainsKey($Platform)) {
        $p = $Platforms[$Platform]
        $TotalCount++
        if (Build-Platform -OS $p.OS -Arch $p.Arch -Name $p.Name -PlatformKey $Platform) {
            $SuccessCount++
        }
    } else {
        Write-Host "[ERROR] Unknown platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms:" -ForegroundColor Cyan
        foreach ($key in $Platforms.Keys) {
            Write-Host "  $key ($($Platforms[$key].Name))" -ForegroundColor White
        }
        exit 1
    }
}

# 复制静态文件
Write-Host "Copying static files..." -ForegroundColor Cyan
if (Test-Path "web\static") {
    Copy-Item -Recurse -Force "web\static" "$BuildDir\"
    Write-Host "[OK] Static files copied" -ForegroundColor Green
}

if (Test-Path "config.example.yaml") {
    Copy-Item "config.example.yaml" "$BuildDir\"
    Write-Host "[OK] Config file copied" -ForegroundColor Green
}

if (Test-Path "README.md") {
    Copy-Item "README.md" "$BuildDir\"
    Write-Host "[OK] README copied" -ForegroundColor Green
}

# 显示构建结果
Write-Host ""
Write-Host "Build completed!" -ForegroundColor Green
Write-Host "Build files location: $BuildDir" -ForegroundColor Blue
Write-Host ""
Write-Host "Build statistics:" -ForegroundColor Yellow
Write-Host "Successful builds: $SuccessCount/$TotalCount" -ForegroundColor White

Write-Host ""
Write-Host "Binary files:" -ForegroundColor Yellow
Get-ChildItem $BuildDir -File -Filter "$ProjectName-*" | ForEach-Object {
    $Size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  $($_.Name) ($Size MB)" -ForegroundColor White
}

if ($SuccessCount -eq $TotalCount) {
    Write-Host ""
    Write-Host "[OK] All platforms built successfully!" -ForegroundColor Green
    
    # 显示使用说明
    Write-Host ""
    Write-Host "Usage instructions:" -ForegroundColor Cyan
    Write-Host "1. For OpenWRT controller: use iperf3-controller-openwrt-* files" -ForegroundColor White
    Write-Host "2. For test servers: use iperf3-controller-linux-amd64 or iperf3-controller-windows-amd64.exe" -ForegroundColor White
    Write-Host "3. Copy config.example.yaml to config.yaml and modify as needed" -ForegroundColor White
    Write-Host "4. Deploy web/static files to the web directory" -ForegroundColor White
    Write-Host ""
    Write-Host "Quick start:" -ForegroundColor Cyan
    Write-Host "  .\$BuildDir\iperf3-controller-windows-amd64.exe -config config.yaml" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "[WARN] Some platforms failed to build, please check error messages" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Build script completed!" -ForegroundColor Blue
