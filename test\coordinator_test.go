package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"

	"github.com/sirupsen/logrus"
)

// TestCoordinator 测试协调器
func TestCoordinator(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 创建客户端管理器
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)

	// 添加测试客户端
	testClients := []client.ClientInfo{
		{
			ID:      "coord-client-1",
			Name:    "协调器测试客户端1",
			Host:    "*************",
			Port:    55200,
			Enabled: true,
		},
		{
			ID:      "coord-client-2",
			Name:    "协调器测试客户端2",
			Host:    "*************",
			Port:    55201,
			Enabled: true,
		},
		{
			ID:      "coord-client-3",
			Name:    "协调器测试客户端3",
			Host:    "*************",
			Port:    55202,
			Enabled: false, // 禁用状态
		},
	}

	for _, clientInfo := range testClients {
		if err := clientManager.AddClient(clientInfo); err != nil {
			t.Fatalf("添加客户端失败: %v", err)
		}
	}

	// 创建协调器
	maxConcurrent := 2
	testCoordinator := coordinator.NewTestCoordinator(clientManager, maxConcurrent, logger)
	if testCoordinator == nil {
		t.Fatal("协调器创建失败")
	}

	t.Run("协调器启动和停止", func(t *testing.T) {
		ctx := context.Background()

		// 启动协调器
		err := testCoordinator.Start(ctx)
		if err != nil {
			t.Fatalf("启动协调器失败: %v", err)
		}

		// 检查状态
		status := testCoordinator.GetStatus()
		if !status.IsRunning {
			t.Fatal("协调器状态显示未运行")
		}

		t.Log("✅ 协调器启动成功")

		// 停止协调器
		err = testCoordinator.Stop()
		if err != nil {
			t.Fatalf("停止协调器失败: %v", err)
		}

		// 检查状态
		status = testCoordinator.GetStatus()
		if status.IsRunning {
			t.Fatal("协调器状态显示仍在运行")
		}

		t.Log("✅ 协调器停止成功")
	})

	t.Run("重复启动协调器", func(t *testing.T) {
		ctx := context.Background()

		// 启动协调器
		err := testCoordinator.Start(ctx)
		if err != nil {
			t.Fatalf("启动协调器失败: %v", err)
		}

		// 重复启动
		err = testCoordinator.Start(ctx)
		if err == nil {
			t.Fatal("重复启动协调器应该失败")
		}

		t.Log("✅ 重复启动协调器正确失败")

		// 清理
		testCoordinator.Stop()
	})

	t.Run("重复停止协调器", func(t *testing.T) {
		// 确保协调器已停止
		testCoordinator.Stop()

		// 重复停止
		err := testCoordinator.Stop()
		if err == nil {
			t.Fatal("重复停止协调器应该失败")
		}

		t.Log("✅ 重复停止协调器正确失败")
	})

	t.Run("协调器状态查询", func(t *testing.T) {
		ctx := context.Background()

		// 启动协调器
		testCoordinator.Start(ctx)

		status := testCoordinator.GetStatus()

		// 验证状态字段
		if !status.IsRunning {
			t.Fatal("协调器应该在运行状态")
		}

		if status.TotalRounds < 0 {
			t.Fatalf("总轮次数不能为负数: %d", status.TotalRounds)
		}

		if status.SuccessfulRounds < 0 {
			t.Fatalf("成功轮次数不能为负数: %d", status.SuccessfulRounds)
		}

		if status.FailedRounds < 0 {
			t.Fatalf("失败轮次数不能为负数: %d", status.FailedRounds)
		}

		t.Log("✅ 协调器状态查询正常")

		// 清理
		testCoordinator.Stop()
	})

	t.Run("模拟测试执行", func(t *testing.T) {
		ctx := context.Background()

		// 启动协调器
		testCoordinator.Start(ctx)

		// 执行测试轮次（模拟）
		hour := time.Now().Hour()
		_, err := testCoordinator.ExecuteTestRound(ctx, hour)
		if err != nil {
			t.Log("⚠️ 执行测试轮次失败（这在测试环境中是正常的）:", err)
		} else {
			t.Log("✅ 测试轮次执行成功")
		}

		// 等待一小段时间让测试开始
		time.Sleep(100 * time.Millisecond)

		// 检查状态
		status := testCoordinator.GetStatus()
		if status.TotalRounds == 0 {
			t.Log("⚠️ 注意: 总轮次数为0，可能是因为客户端连接失败（这在测试环境中是正常的）")
		}

		t.Log("✅ 模拟测试执行完成")

		// 清理
		testCoordinator.Stop()
	})

	t.Run("并发限制测试", func(t *testing.T) {
		// 创建一个最大并发数为1的协调器
		limitedCoordinator := coordinator.NewTestCoordinator(clientManager, 1, logger)
		ctx := context.Background()

		limitedCoordinator.Start(ctx)

		// 检查协调器状态
		status := limitedCoordinator.GetStatus()
		if !status.IsRunning {
			t.Fatal("协调器应该在运行状态")
		}

		t.Log("✅ 并发限制测试通过")

		// 清理
		limitedCoordinator.Stop()
	})

	t.Run("无客户端情况测试", func(t *testing.T) {
		// 创建空的客户端管理器
		emptyClientManager := client.NewManager(clientConfig, logger)
		emptyCoordinator := coordinator.NewTestCoordinator(emptyClientManager, 2, logger)

		ctx := context.Background()

		// 启动协调器
		err := emptyCoordinator.Start(ctx)
		if err != nil {
			t.Fatalf("启动空协调器失败: %v", err)
		}

		// 执行测试轮次
		hour := time.Now().Hour()
		_, err = emptyCoordinator.ExecuteTestRound(ctx, hour)
		if err != nil {
			t.Log("✅ 无客户端时执行测试轮次正确失败")
		} else {
			t.Log("⚠️ 无客户端时执行测试轮次未失败，可能是设计如此")
		}

		// 清理
		emptyCoordinator.Stop()
	})

	t.Run("上下文取消测试", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())

		// 启动协调器
		testCoordinator.Start(ctx)

		// 取消上下文
		cancel()

		// 等待一小段时间让取消生效
		time.Sleep(100 * time.Millisecond)

		// 尝试执行测试轮次
		newCtx := context.Background()
		hour := time.Now().Hour()
		_, err := testCoordinator.ExecuteTestRound(newCtx, hour)
		if err != nil {
			t.Log("✅ 上下文取消后操作正确处理")
		}

		// 清理
		testCoordinator.Stop()
	})
}

// TestCoordinatorConcurrency 协调器并发测试
func TestCoordinatorConcurrency(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 1 * time.Second,
		TestTimeout:    5 * time.Second,
		StopTimeout:    1 * time.Second,
		RetryAttempts:  1,
		RetryDelay:     100 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)

	// 添加多个客户端
	for i := 0; i < 5; i++ {
		clientInfo := client.ClientInfo{
			ID:      fmt.Sprintf("concurrent-client-%d", i),
			Name:    fmt.Sprintf("并发测试客户端%d", i),
			Host:    fmt.Sprintf("192.168.1.%d", 100+i),
			Port:    55200 + i,
			Enabled: true,
		}
		clientManager.AddClient(clientInfo)
	}

	testCoordinator := coordinator.NewTestCoordinator(clientManager, 3, logger)
	ctx := context.Background()

	t.Run("并发启动停止", func(t *testing.T) {
		done := make(chan bool, 10)

		// 并发启动和停止
		for i := 0; i < 5; i++ {
			go func() {
				testCoordinator.Start(ctx)
				done <- true
			}()
		}

		for i := 0; i < 5; i++ {
			go func() {
				time.Sleep(50 * time.Millisecond)
				testCoordinator.Stop()
				done <- true
			}()
		}

		// 等待所有goroutine完成
		for i := 0; i < 10; i++ {
			<-done
		}

		t.Log("✅ 并发启动停止测试完成")
	})

	t.Run("并发状态查询", func(t *testing.T) {
		testCoordinator.Start(ctx)

		done := make(chan bool, 20)

		// 并发查询状态
		for i := 0; i < 20; i++ {
			go func() {
				status := testCoordinator.GetStatus()
				if status == nil {
					t.Error("状态查询返回nil")
				}
				done <- true
			}()
		}

		// 等待所有goroutine完成
		for i := 0; i < 20; i++ {
			<-done
		}

		testCoordinator.Stop()
		t.Log("✅ 并发状态查询测试完成")
	})
}

// BenchmarkCoordinator 协调器性能测试
func BenchmarkCoordinator(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 1 * time.Second,
		TestTimeout:    5 * time.Second,
		StopTimeout:    1 * time.Second,
		RetryAttempts:  1,
		RetryDelay:     100 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 4, logger)

	ctx := context.Background()
	testCoordinator.Start(ctx)

	b.Run("GetStatus", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			testCoordinator.GetStatus()
		}
	})

	testCoordinator.Stop()
}
