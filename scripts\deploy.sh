#!/bin/bash

# iperf3-controller 部署脚本
# 支持OpenWRT和Linux系统部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
PROJECT_NAME="iperf3-controller"
INSTALL_DIR="/opt/iperf3-controller"
CONFIG_DIR="/etc/iperf3-controller"
LOG_DIR="/var/log/iperf3-controller"
DATA_DIR="/var/lib/iperf3-controller"
SERVICE_NAME="iperf3-controller"

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ 请使用root权限运行此脚本${NC}"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [ -f /etc/openwrt_release ]; then
        SYSTEM_TYPE="openwrt"
        echo -e "${BLUE}🔍 检测到OpenWRT系统${NC}"
    elif [ -f /etc/debian_version ]; then
        SYSTEM_TYPE="debian"
        echo -e "${BLUE}🔍 检测到Debian/Ubuntu系统${NC}"
    elif [ -f /etc/redhat-release ]; then
        SYSTEM_TYPE="redhat"
        echo -e "${BLUE}🔍 检测到RedHat/CentOS系统${NC}"
    else
        SYSTEM_TYPE="generic"
        echo -e "${BLUE}🔍 检测到通用Linux系统${NC}"
    fi
}

# 检测架构
detect_arch() {
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            ARCH="amd64"
            ;;
        aarch64)
            ARCH="arm64"
            ;;
        armv7l)
            ARCH="arm"
            ;;
        mips)
            ARCH="mips"
            ;;
        mipsel)
            ARCH="mipsle"
            ;;
        *)
            echo -e "${YELLOW}⚠️ 未知架构: $ARCH，使用amd64${NC}"
            ARCH="amd64"
            ;;
    esac
    echo -e "${BLUE}🏗️ 系统架构: $ARCH${NC}"
}

# 创建目录
create_directories() {
    echo -e "${YELLOW}📁 创建目录结构...${NC}"
    
    mkdir -p $INSTALL_DIR
    mkdir -p $CONFIG_DIR
    mkdir -p $LOG_DIR
    mkdir -p $DATA_DIR
    
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        mkdir -p $INSTALL_DIR/web
    fi
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装依赖包...${NC}"
    
    case $SYSTEM_TYPE in
        "openwrt")
            opkg update
            opkg install iperf3 sqlite3-cli
            ;;
        "debian")
            apt-get update
            apt-get install -y iperf3 sqlite3 curl
            ;;
        "redhat")
            yum update -y
            yum install -y iperf3 sqlite curl
            ;;
        *)
            echo -e "${YELLOW}⚠️ 请手动安装iperf3和sqlite3${NC}"
            ;;
    esac
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 下载或复制二进制文件
install_binary() {
    echo -e "${YELLOW}📥 安装二进制文件...${NC}"
    
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        BINARY_NAME="${PROJECT_NAME}-openwrt-${ARCH}"
    else
        BINARY_NAME="${PROJECT_NAME}-linux-${ARCH}"
    fi
    
    # 如果本地有构建文件，直接复制
    if [ -f "build/${BINARY_NAME}" ]; then
        cp "build/${BINARY_NAME}" "$INSTALL_DIR/${PROJECT_NAME}"
        echo -e "${GREEN}✅ 从本地复制二进制文件${NC}"
    else
        echo -e "${RED}❌ 未找到二进制文件: build/${BINARY_NAME}${NC}"
        echo -e "${YELLOW}请先运行构建脚本: ./scripts/build.sh${NC}"
        exit 1
    fi
    
    chmod +x "$INSTALL_DIR/${PROJECT_NAME}"
}

# 安装Web文件
install_web_files() {
    echo -e "${YELLOW}🌐 安装Web文件...${NC}"
    
    if [ -d "web/static" ]; then
        cp -r web/static/* "$INSTALL_DIR/web/"
        echo -e "${GREEN}✅ Web文件安装完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 未找到Web文件目录${NC}"
    fi
}

# 创建配置文件
create_config() {
    echo -e "${YELLOW}⚙️ 创建配置文件...${NC}"
    
    cat > "$CONFIG_DIR/config.yaml" << EOF
# iperf3-controller 配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "server"  # server 或 client

# 客户端管理配置
client_management:
  prepare_timeout: "10s"
  test_timeout: "60s"
  stop_timeout: "5s"
  retry_attempts: 3
  retry_delay: "1s"

# 调度配置
schedule:
  mode: "odd"  # odd, even, always
  timezone: "Asia/Shanghai"

# 同步配置
sync:
  peer_host: "*************"
  peer_port: 8080
  sync_interval: "30s"
  heartbeat_interval: "10s"
  connect_timeout: "5s"
  sync_timeout: "30s"
  retry_attempts: 3
  retry_delay: "2s"
  enable_compression: true
  enable_encryption: false

# 数据库配置
database:
  type: "sqlite"
  path: "$DATA_DIR/iperf3.db"

# 日志配置
logging:
  level: "info"
  file: "$LOG_DIR/iperf3-controller.log"
  max_size: "100MB"
  max_backups: 5
  max_age: 30

# Web配置
web:
  static_dir: "$INSTALL_DIR/web"
  enable_cors: true
  enable_auth: false

# 客户端列表（示例）
clients:
  - id: "client-1"
    name: "测试客户端 1"
    host: "*************"
    port: 55201
    enabled: true
  - id: "client-2"
    name: "测试客户端 2"
    host: "*************"
    port: 55202
    enabled: true
EOF
    
    echo -e "${GREEN}✅ 配置文件创建完成${NC}"
}

# 创建systemd服务
create_systemd_service() {
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        create_openwrt_service
        return
    fi
    
    echo -e "${YELLOW}🔧 创建systemd服务...${NC}"
    
    cat > "/etc/systemd/system/${SERVICE_NAME}.service" << EOF
[Unit]
Description=iperf3 Controller Service
Documentation=https://github.com/your-repo/iperf3-controller
After=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=$INSTALL_DIR/$PROJECT_NAME -config $CONFIG_DIR/config.yaml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$DATA_DIR $LOG_DIR

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    echo -e "${GREEN}✅ systemd服务创建完成${NC}"
}

# 创建OpenWRT服务
create_openwrt_service() {
    echo -e "${YELLOW}🔧 创建OpenWRT服务...${NC}"
    
    cat > "/etc/init.d/${SERVICE_NAME}" << 'EOF'
#!/bin/sh /etc/rc.common

START=99
STOP=10

USE_PROCD=1
PROG="/opt/iperf3-controller/iperf3-controller"
CONF="/etc/iperf3-controller/config.yaml"

start_service() {
    procd_open_instance
    procd_set_param command $PROG -config $CONF
    procd_set_param respawn
    procd_set_param stdout 1
    procd_set_param stderr 1
    procd_close_instance
}

stop_service() {
    killall iperf3-controller
}

reload_service() {
    stop_service
    start_service
}
EOF
    
    chmod +x "/etc/init.d/${SERVICE_NAME}"
    "/etc/init.d/${SERVICE_NAME}" enable
    
    echo -e "${GREEN}✅ OpenWRT服务创建完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${YELLOW}🚀 启动服务...${NC}"
    
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        "/etc/init.d/${SERVICE_NAME}" start
    else
        systemctl start $SERVICE_NAME
    fi
    
    sleep 2
    
    # 检查服务状态
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        if pgrep -f "$PROJECT_NAME" > /dev/null; then
            echo -e "${GREEN}✅ 服务启动成功${NC}"
        else
            echo -e "${RED}❌ 服务启动失败${NC}"
            exit 1
        fi
    else
        if systemctl is-active --quiet $SERVICE_NAME; then
            echo -e "${GREEN}✅ 服务启动成功${NC}"
        else
            echo -e "${RED}❌ 服务启动失败${NC}"
            systemctl status $SERVICE_NAME
            exit 1
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 部署信息:${NC}"
    echo "  安装目录: $INSTALL_DIR"
    echo "  配置目录: $CONFIG_DIR"
    echo "  日志目录: $LOG_DIR"
    echo "  数据目录: $DATA_DIR"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  Web界面: http://$(hostname -I | awk '{print $1}'):8080"
    echo "  API接口: http://$(hostname -I | awk '{print $1}'):8080/api/v1/status"
    echo ""
    echo -e "${BLUE}🔧 服务管理:${NC}"
    if [ "$SYSTEM_TYPE" = "openwrt" ]; then
        echo "  启动服务: /etc/init.d/$SERVICE_NAME start"
        echo "  停止服务: /etc/init.d/$SERVICE_NAME stop"
        echo "  重启服务: /etc/init.d/$SERVICE_NAME restart"
        echo "  查看状态: ps | grep $PROJECT_NAME"
    else
        echo "  启动服务: systemctl start $SERVICE_NAME"
        echo "  停止服务: systemctl stop $SERVICE_NAME"
        echo "  重启服务: systemctl restart $SERVICE_NAME"
        echo "  查看状态: systemctl status $SERVICE_NAME"
        echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    fi
    echo ""
    echo -e "${YELLOW}⚠️ 请根据实际环境修改配置文件: $CONFIG_DIR/config.yaml${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}🚀 iperf3-controller 部署脚本${NC}"
    echo ""
    
    check_root
    detect_system
    detect_arch
    create_directories
    install_dependencies
    install_binary
    install_web_files
    create_config
    create_systemd_service
    start_service
    show_deployment_info
}

# 运行主函数
main "$@"
