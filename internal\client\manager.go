package client

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/config"

	"github.com/sirupsen/logrus"
)

// Manager 管理多个客户端并协调其操作
type Manager struct {
	clients map[string]Client
	config  *config.ClientManagementConfig
	logger  *logrus.Logger

	// 用于线程安全操作的互斥锁
	mu sync.RWMutex

	// 健康检查计时器
	healthTicker *time.Ticker
	stopChan     chan struct{}

	// 统计数据
	stats ManagerStats
}

// ManagerStats 包含客户端管理器的统计信息
type ManagerStats struct {
	TotalClients        int       `json:"total_clients"`
	EnabledClients      int       `json:"enabled_clients"`
	IdleClients         int       `json:"idle_clients"`
	TestingClients      int       `json:"testing_clients"`
	ErrorClients        int       `json:"error_clients"`
	DisconnectedClients int       `json:"disconnected_clients"`
	LastUpdate          time.Time `json:"last_update"`
}

// NewManager 创建一个新的客户端管理器
func NewManager(config *config.ClientManagementConfig, logger *logrus.Logger) *Manager {
	if logger == nil {
		logger = logrus.New()
	}

	return &Manager{
		clients:  make(map[string]Client),
		config:   config,
		logger:   logger,
		stopChan: make(chan struct{}),
	}
}

// AddClient 将客户端添加到管理器
func (m *Manager) AddClient(info ClientInfo) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[info.ID]; exists {
		return fmt.Errorf("client with ID %s already exists", info.ID)
	}

	client := NewHTTPClient(info, m.logger, m.config)
	m.clients[info.ID] = client

	m.logger.WithFields(logrus.Fields{
		"client_id": info.ID,
		"name":      info.Name,
		"host":      info.Host,
		"port":      info.Port,
	}).Info("Client added to manager")

	return nil
}

// RemoveClient 从管理器中移除客户端
func (m *Manager) RemoveClient(clientID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	client, exists := m.clients[clientID]
	if !exists {
		return fmt.Errorf("client with ID %s not found", clientID)
	}

	// 关闭客户端连接
	if err := client.Close(); err != nil {
		m.logger.WithFields(logrus.Fields{
			"client_id": clientID,
			"error":     err.Error(),
		}).Warn("Error closing client connection")
	}

	delete(m.clients, clientID)

	m.logger.WithField("client_id", clientID).Info("Client removed from manager")
	return nil
}

// GetClient 根据 ID 返回客户端
func (m *Manager) GetClient(clientID string) (Client, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, exists := m.clients[clientID]
	if !exists {
		return nil, fmt.Errorf("client with ID %s not found", clientID)
	}

	return client, nil
}

// GetAllClients 返回所有客户端
func (m *Manager) GetAllClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	clients := make([]Client, 0, len(m.clients))
	for _, client := range m.clients {
		clients = append(clients, client)
	}

	return clients
}

// GetEnabledClients 返回所有启用的客户端
func (m *Manager) GetEnabledClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var enabledClients []Client
	for _, client := range m.clients {
		if client.IsEnabled() {
			enabledClients = append(enabledClients, client)
		}
	}

	return enabledClients
}

// GetIdleClients 返回所有空闲和启用的客户端
func (m *Manager) GetIdleClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var idleClients []Client
	for _, client := range m.clients {
		if client.IsEnabled() && client.GetState() == StateIdle {
			idleClients = append(idleClients, client)
		}
	}

	return idleClients
}

// PrepareClients 准备多个客户端进行测试
func (m *Manager) PrepareClients(ctx context.Context, clientIDs []string) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(clientIDs))

	for _, clientID := range clientIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			client, err := m.GetClient(id)
			if err != nil {
				errChan <- fmt.Errorf("client %s: %w", id, err)
				return
			}

			if err := client.Prepare(ctx); err != nil {
				errChan <- fmt.Errorf("client %s prepare failed: %w", id, err)
				return
			}
		}(clientID)
	}

	wg.Wait()
	close(errChan)

	// Collect errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("prepare failed for %d clients: %v", len(errors), errors)
	}

	m.logger.WithField("client_count", len(clientIDs)).Info("All clients prepared successfully")
	return nil
}

// StopClients 停止多个客户端
func (m *Manager) StopClients(ctx context.Context, clientIDs []string) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(clientIDs))

	for _, clientID := range clientIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			client, err := m.GetClient(id)
			if err != nil {
				errChan <- fmt.Errorf("client %s: %w", id, err)
				return
			}

			if err := client.Stop(ctx); err != nil {
				errChan <- fmt.Errorf("client %s stop failed: %w", id, err)
				return
			}
		}(clientID)
	}

	wg.Wait()
	close(errChan)

	// Collect errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("stop failed for %d clients: %v", len(errors), errors)
	}

	m.logger.WithField("client_count", len(clientIDs)).Info("All clients stopped successfully")
	return nil
}

// GetStats 返回当前管理器统计信息
func (m *Manager) GetStats() ManagerStats {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := ManagerStats{
		TotalClients: len(m.clients),
		LastUpdate:   time.Now(),
	}

	for _, client := range m.clients {
		if client.IsEnabled() {
			stats.EnabledClients++
		}

		switch client.GetState() {
		case StateIdle:
			stats.IdleClients++
		case StateTesting:
			stats.TestingClients++
		case StateError:
			stats.ErrorClients++
		case StateDisconnected:
			stats.DisconnectedClients++
		}
	}

	m.stats = stats
	return stats
}

// Start 启动客户端管理器并开始健康检查
func (m *Manager) Start(ctx context.Context) error {
	m.logger.Info("Starting client manager")

	// 启动健康检查例程
	m.healthTicker = time.NewTicker(30 * time.Second) // Health check every 30 seconds

	go m.healthCheckRoutine(ctx)

	m.logger.Info("Client manager started successfully")
	return nil
}

// Stop 停止客户端管理器
func (m *Manager) Stop() error {
	m.logger.Info("Stopping client manager")

	// 停止健康检查例程
	if m.healthTicker != nil {
		m.healthTicker.Stop()
	}

	close(m.stopChan)

	// 关闭所有客户端连接
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, client := range m.clients {
		if err := client.Close(); err != nil {
			m.logger.WithFields(logrus.Fields{
				"client_id": client.GetID(),
				"error":     err.Error(),
			}).Warn("Error closing client connection")
		}
	}

	m.logger.Info("Client manager stopped")
	return nil
}

// healthCheckRoutine 对所有客户端执行定期健康检查
func (m *Manager) healthCheckRoutine(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case <-m.healthTicker.C:
			m.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 检查所有客户端的健康状况
func (m *Manager) performHealthCheck(ctx context.Context) {
	clients := m.GetAllClients()

	for _, client := range clients {
		if !client.IsEnabled() {
			continue
		}

		// 如果客户端当前正在测试，则跳过
		if client.GetState() == StateTesting {
			continue
		}

		go func(c Client) {
			// 为健康检查创建一个超时上下文
			healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
			defer cancel()

			_, err := c.GetStatus(healthCtx)
			if err != nil {
				m.logger.WithFields(logrus.Fields{
					"client_id": c.GetID(),
					"error":     err.Error(),
				}).Warn("Client health check failed")
			}
		}(client)
	}
}

// LoadClientsFromConfig 从配置中加载客户端
func (m *Manager) LoadClientsFromConfig(servers []config.ServerConfig) error {
	m.logger.Info("Loading clients from configuration")

	for _, server := range servers {
		clientInfo := ClientInfo{
			ID:      server.Name, // 使用服务器名称作为客户端 ID
			Name:    server.Name,
			Host:    server.Host,
			Port:    server.Port,
			Enabled: server.Enabled,
		}

		if err := m.AddClient(clientInfo); err != nil {
			m.logger.WithFields(logrus.Fields{
				"server_name": server.Name,
				"error":       err.Error(),
			}).Error("Failed to add client from configuration")
			continue
		}
	}

	stats := m.GetStats()
	m.logger.WithFields(logrus.Fields{
		"total_clients":   stats.TotalClients,
		"enabled_clients": stats.EnabledClients,
	}).Info("Clients loaded from configuration")

	return nil
}
