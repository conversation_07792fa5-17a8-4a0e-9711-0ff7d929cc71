package client

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/config"

	"github.com/sirupsen/logrus"
)

// Manager manages multiple clients and coordinates their operations
type Manager struct {
	clients map[string]Client
	config  *config.ClientManagementConfig
	logger  *logrus.Logger

	// Mutex for thread-safe operations
	mu sync.RWMutex

	// Health check ticker
	healthTicker *time.Ticker
	stopChan     chan struct{}

	// Statistics
	stats ManagerStats
}

// ManagerStats contains statistics about the client manager
type ManagerStats struct {
	TotalClients        int       `json:"total_clients"`
	EnabledClients      int       `json:"enabled_clients"`
	IdleClients         int       `json:"idle_clients"`
	TestingClients      int       `json:"testing_clients"`
	ErrorClients        int       `json:"error_clients"`
	DisconnectedClients int       `json:"disconnected_clients"`
	LastUpdate          time.Time `json:"last_update"`
}

// NewManager creates a new client manager
func NewManager(config *config.ClientManagementConfig, logger *logrus.Logger) *Manager {
	if logger == nil {
		logger = logrus.New()
	}

	return &Manager{
		clients:  make(map[string]Client),
		config:   config,
		logger:   logger,
		stopChan: make(chan struct{}),
	}
}

// AddClient adds a client to the manager
func (m *Manager) AddClient(info ClientInfo) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[info.ID]; exists {
		return fmt.Errorf("client with ID %s already exists", info.ID)
	}

	client := NewHTTPClient(info, m.logger, m.config)
	m.clients[info.ID] = client

	m.logger.WithFields(logrus.Fields{
		"client_id": info.ID,
		"name":      info.Name,
		"host":      info.Host,
		"port":      info.Port,
	}).Info("Client added to manager")

	return nil
}

// RemoveClient removes a client from the manager
func (m *Manager) RemoveClient(clientID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	client, exists := m.clients[clientID]
	if !exists {
		return fmt.Errorf("client with ID %s not found", clientID)
	}

	// Close the client connection
	if err := client.Close(); err != nil {
		m.logger.WithFields(logrus.Fields{
			"client_id": clientID,
			"error":     err.Error(),
		}).Warn("Error closing client connection")
	}

	delete(m.clients, clientID)

	m.logger.WithField("client_id", clientID).Info("Client removed from manager")
	return nil
}

// GetClient returns a client by ID
func (m *Manager) GetClient(clientID string) (Client, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, exists := m.clients[clientID]
	if !exists {
		return nil, fmt.Errorf("client with ID %s not found", clientID)
	}

	return client, nil
}

// GetAllClients returns all clients
func (m *Manager) GetAllClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	clients := make([]Client, 0, len(m.clients))
	for _, client := range m.clients {
		clients = append(clients, client)
	}

	return clients
}

// GetEnabledClients returns all enabled clients
func (m *Manager) GetEnabledClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var enabledClients []Client
	for _, client := range m.clients {
		if client.IsEnabled() {
			enabledClients = append(enabledClients, client)
		}
	}

	return enabledClients
}

// GetIdleClients returns all idle and enabled clients
func (m *Manager) GetIdleClients() []Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var idleClients []Client
	for _, client := range m.clients {
		if client.IsEnabled() && client.GetState() == StateIdle {
			idleClients = append(idleClients, client)
		}
	}

	return idleClients
}

// PrepareClients prepares multiple clients for testing
func (m *Manager) PrepareClients(ctx context.Context, clientIDs []string) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(clientIDs))

	for _, clientID := range clientIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			client, err := m.GetClient(id)
			if err != nil {
				errChan <- fmt.Errorf("client %s: %w", id, err)
				return
			}

			if err := client.Prepare(ctx); err != nil {
				errChan <- fmt.Errorf("client %s prepare failed: %w", id, err)
				return
			}
		}(clientID)
	}

	wg.Wait()
	close(errChan)

	// Collect errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("prepare failed for %d clients: %v", len(errors), errors)
	}

	m.logger.WithField("client_count", len(clientIDs)).Info("All clients prepared successfully")
	return nil
}

// StopClients stops multiple clients
func (m *Manager) StopClients(ctx context.Context, clientIDs []string) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(clientIDs))

	for _, clientID := range clientIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			client, err := m.GetClient(id)
			if err != nil {
				errChan <- fmt.Errorf("client %s: %w", id, err)
				return
			}

			if err := client.Stop(ctx); err != nil {
				errChan <- fmt.Errorf("client %s stop failed: %w", id, err)
				return
			}
		}(clientID)
	}

	wg.Wait()
	close(errChan)

	// Collect errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("stop failed for %d clients: %v", len(errors), errors)
	}

	m.logger.WithField("client_count", len(clientIDs)).Info("All clients stopped successfully")
	return nil
}

// GetStats returns current manager statistics
func (m *Manager) GetStats() ManagerStats {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := ManagerStats{
		TotalClients: len(m.clients),
		LastUpdate:   time.Now(),
	}

	for _, client := range m.clients {
		if client.IsEnabled() {
			stats.EnabledClients++
		}

		switch client.GetState() {
		case StateIdle:
			stats.IdleClients++
		case StateTesting:
			stats.TestingClients++
		case StateError:
			stats.ErrorClients++
		case StateDisconnected:
			stats.DisconnectedClients++
		}
	}

	m.stats = stats
	return stats
}

// Start starts the client manager and begins health checking
func (m *Manager) Start(ctx context.Context) error {
	m.logger.Info("Starting client manager")

	// Start health check routine
	m.healthTicker = time.NewTicker(30 * time.Second) // Health check every 30 seconds

	go m.healthCheckRoutine(ctx)

	m.logger.Info("Client manager started successfully")
	return nil
}

// Stop stops the client manager
func (m *Manager) Stop() error {
	m.logger.Info("Stopping client manager")

	// Stop health check routine
	if m.healthTicker != nil {
		m.healthTicker.Stop()
	}

	close(m.stopChan)

	// Close all client connections
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, client := range m.clients {
		if err := client.Close(); err != nil {
			m.logger.WithFields(logrus.Fields{
				"client_id": client.GetID(),
				"error":     err.Error(),
			}).Warn("Error closing client connection")
		}
	}

	m.logger.Info("Client manager stopped")
	return nil
}

// healthCheckRoutine performs periodic health checks on all clients
func (m *Manager) healthCheckRoutine(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case <-m.healthTicker.C:
			m.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck checks the health of all clients
func (m *Manager) performHealthCheck(ctx context.Context) {
	clients := m.GetAllClients()

	for _, client := range clients {
		if !client.IsEnabled() {
			continue
		}

		// Skip if client is currently testing
		if client.GetState() == StateTesting {
			continue
		}

		go func(c Client) {
			// Create a timeout context for health check
			healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
			defer cancel()

			_, err := c.GetStatus(healthCtx)
			if err != nil {
				m.logger.WithFields(logrus.Fields{
					"client_id": c.GetID(),
					"error":     err.Error(),
				}).Warn("Client health check failed")
			}
		}(client)
	}
}

// LoadClientsFromConfig loads clients from configuration
func (m *Manager) LoadClientsFromConfig(servers []config.ServerConfig) error {
	m.logger.Info("Loading clients from configuration")

	for _, server := range servers {
		clientInfo := ClientInfo{
			ID:      server.Name, // Use server name as client ID
			Name:    server.Name,
			Host:    server.Host,
			Port:    server.Port,
			Enabled: server.Enabled,
		}

		if err := m.AddClient(clientInfo); err != nil {
			m.logger.WithFields(logrus.Fields{
				"server_name": server.Name,
				"error":       err.Error(),
			}).Error("Failed to add client from configuration")
			continue
		}
	}

	stats := m.GetStats()
	m.logger.WithFields(logrus.Fields{
		"total_clients":   stats.TotalClients,
		"enabled_clients": stats.EnabledClients,
	}).Info("Clients loaded from configuration")

	return nil
}
