# iperf3-controller 客户端1配置
# 客户端IP: *************
# 连接到两个OpenWRT服务端

# 服务器配置
server:
  host: "0.0.0.0"
  port: 55201              # 客户端监听端口
  mode: "client"           # 客户端模式
  read_timeout: "30s"
  write_timeout: "30s"

# 控制器连接配置（主要连接服务端1）
controller:
  primary:
    host: "***********"    # 主OpenWRT服务端IP
    port: 6060             # 主服务端端口
  secondary:
    host: "************"   # 备OpenWRT服务端IP
    port: 6066             # 备服务端端口
  connect_timeout: "10s"
  heartbeat_interval: "30s"
  retry_attempts: 5
  retry_delay: "5s"

# 客户端标识
client:
  id: "client-138-2-114-254"
  name: "外网客户端 *************"
  description: "外网测试客户端1"
  location: "外网节点1"

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"
  # 连接到两个服务端的iperf3端口
  servers:
    - host: "***********"
      port: 5201
      name: "OpenWRT服务端1"
    - host: "************"
      port: 5202
      name: "OpenWRT服务端2"
  default_duration: 30
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
  udp:
    bandwidth: "500M"
    packet_size: 1472

# 日志配置
logging:
  level: "info"
  file: "/var/log/iperf3-controller/client1.log"
  max_size: "50MB"
  max_backups: 3
  max_age: 7

# 性能配置
performance:
  max_concurrent_tests: 1   # 同时进行的测试数量
  test_queue_size: 10       # 测试队列大小
  result_buffer_size: 100   # 结果缓冲区大小

# 网络配置
network:
  bind_interface: ""        # 绑定网络接口（空表示自动选择）
  source_ip: "*************" # 源IP地址
  tcp_no_delay: true
  tcp_keepalive: true

# 监控配置
monitoring:
  enable_system_monitoring: true
  cpu_threshold: 80         # CPU使用率阈值
  memory_threshold: 80      # 内存使用率阈值
  disk_threshold: 90        # 磁盘使用率阈值

# 安全配置
security:
  allowed_controllers:      # 允许连接的控制器IP
    - "***********"
    - "************"
  enable_encryption: false
  api_key: "client1-secret-key"
