package config

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Validate validates the configuration
func (m *Manager) Validate() error {
	if m.config == nil {
		return fmt.Errorf("configuration is nil")
	}

	// Validate server configuration
	if err := m.validateServer(); err != nil {
		return fmt.Errorf("server config validation failed: %w", err)
	}

	// Validate schedule configuration
	if err := m.validateSchedule(); err != nil {
		return fmt.Errorf("schedule config validation failed: %w", err)
	}

	// Validate peer configuration
	if err := m.validatePeer(); err != nil {
		return fmt.Errorf("peer config validation failed: %w", err)
	}

	// Validate database configuration
	if err := m.validateDatabase(); err != nil {
		return fmt.Errorf("database config validation failed: %w", err)
	}

	// Validate servers list
	if err := m.validateServers(); err != nil {
		return fmt.Errorf("servers config validation failed: %w", err)
	}

	// Validate performance configuration
	if err := m.validatePerformance(); err != nil {
		return fmt.Errorf("performance config validation failed: %w", err)
	}

	// Validate client management configuration
	if err := m.validateClientManagement(); err != nil {
		return fmt.Errorf("client management config validation failed: %w", err)
	}

	// Validate sync configuration
	if err := m.validateSync(); err != nil {
		return fmt.Errorf("sync config validation failed: %w", err)
	}

	// Validate logging configuration
	if err := m.validateLogging(); err != nil {
		return fmt.Errorf("logging config validation failed: %w", err)
	}

	return nil
}

// validateServer validates server configuration
func (m *Manager) validateServer() error {
	server := &m.config.Server

	// Validate listen port
	if server.ListenPort <= 0 || server.ListenPort > 65535 {
		return fmt.Errorf("invalid listen port: %d", server.ListenPort)
	}

	// Validate web port
	if server.WebPort <= 0 || server.WebPort > 65535 {
		return fmt.Errorf("invalid web port: %d", server.WebPort)
	}

	// Ensure ports are different
	if server.ListenPort == server.WebPort {
		return fmt.Errorf("listen port and web port cannot be the same: %d", server.ListenPort)
	}

	return nil
}

// validateSchedule validates schedule configuration
func (m *Manager) validateSchedule() error {
	schedule := &m.config.Schedule

	// Validate mode
	validModes := []string{"odd", "even", "always"}
	if !contains(validModes, schedule.Mode) {
		return fmt.Errorf("invalid schedule mode: %s, must be one of: %s", 
			schedule.Mode, strings.Join(validModes, ", "))
	}

	// Validate timezone
	if schedule.Timezone == "" {
		return fmt.Errorf("timezone cannot be empty")
	}

	// Try to load timezone
	if _, err := time.LoadLocation(schedule.Timezone); err != nil {
		return fmt.Errorf("invalid timezone: %s", schedule.Timezone)
	}

	return nil
}

// validatePeer validates peer configuration
func (m *Manager) validatePeer() error {
	peer := &m.config.Peer

	// Validate IP address (if provided)
	if peer.IP != "" {
		if net.ParseIP(peer.IP) == nil {
			return fmt.Errorf("invalid peer IP address: %s", peer.IP)
		}
	}

	// Validate port
	if peer.Port <= 0 || peer.Port > 65535 {
		return fmt.Errorf("invalid peer port: %d", peer.Port)
	}

	// Validate sync interval
	if peer.SyncInterval <= 0 {
		return fmt.Errorf("sync interval must be positive: %v", peer.SyncInterval)
	}

	return nil
}

// validateDatabase validates database configuration
func (m *Manager) validateDatabase() error {
	db := &m.config.Database

	// Validate path
	if db.Path == "" {
		return fmt.Errorf("database path cannot be empty")
	}

	// Ensure directory exists or can be created
	dir := filepath.Dir(db.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("cannot create database directory %s: %w", dir, err)
	}

	// Validate backup interval
	if db.BackupInterval <= 0 {
		return fmt.Errorf("backup interval must be positive: %v", db.BackupInterval)
	}

	// Validate max size
	if db.MaxSizeMB <= 0 {
		return fmt.Errorf("max size must be positive: %d", db.MaxSizeMB)
	}

	// Validate retention days
	if db.RetentionDays <= 0 {
		return fmt.Errorf("retention days must be positive: %d", db.RetentionDays)
	}

	// Validate connection pool settings
	if db.MaxOpenConns <= 0 {
		return fmt.Errorf("max open connections must be positive: %d", db.MaxOpenConns)
	}

	if db.MaxIdleConns < 0 || db.MaxIdleConns > db.MaxOpenConns {
		return fmt.Errorf("max idle connections must be between 0 and max open connections: %d", db.MaxIdleConns)
	}

	// Validate timeouts
	if db.ConnMaxLifetime <= 0 {
		return fmt.Errorf("connection max lifetime must be positive: %v", db.ConnMaxLifetime)
	}

	if db.ConnMaxIdleTime <= 0 {
		return fmt.Errorf("connection max idle time must be positive: %v", db.ConnMaxIdleTime)
	}

	if db.BusyTimeout <= 0 {
		return fmt.Errorf("busy timeout must be positive: %v", db.BusyTimeout)
	}

	// Validate journal mode
	validJournalModes := []string{"DELETE", "TRUNCATE", "PERSIST", "MEMORY", "WAL", "OFF"}
	if !contains(validJournalModes, db.JournalMode) {
		return fmt.Errorf("invalid journal mode: %s, must be one of: %s", 
			db.JournalMode, strings.Join(validJournalModes, ", "))
	}

	// Validate synchronous mode
	validSyncModes := []string{"OFF", "NORMAL", "FULL", "EXTRA"}
	if !contains(validSyncModes, db.Synchronous) {
		return fmt.Errorf("invalid synchronous mode: %s, must be one of: %s", 
			db.Synchronous, strings.Join(validSyncModes, ", "))
	}

	return nil
}

// validateServers validates servers list
func (m *Manager) validateServers() error {
	if len(m.config.Servers) == 0 {
		return fmt.Errorf("at least one server must be configured")
	}

	serverNames := make(map[string]bool)
	for i, server := range m.config.Servers {
		// Validate name
		if server.Name == "" {
			return fmt.Errorf("server %d: name cannot be empty", i)
		}

		// Check for duplicate names
		if serverNames[server.Name] {
			return fmt.Errorf("duplicate server name: %s", server.Name)
		}
		serverNames[server.Name] = true

		// Validate host
		if server.Host == "" {
			return fmt.Errorf("server %s: host cannot be empty", server.Name)
		}

		// Validate IP address or hostname
		if net.ParseIP(server.Host) == nil {
			// If not an IP, try to resolve hostname
			if _, err := net.LookupHost(server.Host); err != nil {
				m.logger.WithField("server", server.Name).WithField("host", server.Host).
					Warn("Cannot resolve hostname, but continuing anyway")
			}
		}

		// Validate port
		if server.Port <= 0 || server.Port > 65535 {
			return fmt.Errorf("server %s: invalid port: %d", server.Name, server.Port)
		}
	}

	return nil
}

// validatePerformance validates performance configuration
func (m *Manager) validatePerformance() error {
	perf := &m.config.Performance

	// Validate TCP configuration
	if perf.TCP.ParallelStreams <= 0 {
		return fmt.Errorf("TCP parallel streams must be positive: %d", perf.TCP.ParallelStreams)
	}

	if perf.TCP.MSS <= 0 {
		return fmt.Errorf("TCP MSS must be positive: %d", perf.TCP.MSS)
	}

	if perf.TCP.Duration <= 0 {
		return fmt.Errorf("TCP duration must be positive: %v", perf.TCP.Duration)
	}

	// Validate UDP configuration
	if perf.UDP.PacketSize <= 0 {
		return fmt.Errorf("UDP packet size must be positive: %d", perf.UDP.PacketSize)
	}

	if perf.UDP.Duration <= 0 {
		return fmt.Errorf("UDP duration must be positive: %v", perf.UDP.Duration)
	}

	// Validate concurrency configuration
	if perf.Concurrency.MaxWorkers <= 0 {
		return fmt.Errorf("max workers must be positive: %d", perf.Concurrency.MaxWorkers)
	}

	if perf.Concurrency.BatchSize <= 0 {
		return fmt.Errorf("batch size must be positive: %d", perf.Concurrency.BatchSize)
	}

	if perf.Concurrency.BatchDelay < 0 {
		return fmt.Errorf("batch delay cannot be negative: %v", perf.Concurrency.BatchDelay)
	}

	return nil
}

// validateClientManagement validates client management configuration
func (m *Manager) validateClientManagement() error {
	cm := &m.config.ClientManagement

	if cm.PrepareTimeout <= 0 {
		return fmt.Errorf("prepare timeout must be positive: %v", cm.PrepareTimeout)
	}

	if cm.TestTimeout <= 0 {
		return fmt.Errorf("test timeout must be positive: %v", cm.TestTimeout)
	}

	if cm.StopTimeout <= 0 {
		return fmt.Errorf("stop timeout must be positive: %v", cm.StopTimeout)
	}

	if cm.RetryAttempts < 0 {
		return fmt.Errorf("retry attempts cannot be negative: %d", cm.RetryAttempts)
	}

	if cm.RetryDelay < 0 {
		return fmt.Errorf("retry delay cannot be negative: %v", cm.RetryDelay)
	}

	return nil
}

// validateSync validates sync configuration
func (m *Manager) validateSync() error {
	sync := &m.config.Sync

	if sync.Interval <= 0 {
		return fmt.Errorf("sync interval must be positive: %v", sync.Interval)
	}

	if sync.Timeout <= 0 {
		return fmt.Errorf("sync timeout must be positive: %v", sync.Timeout)
	}

	return nil
}

// validateLogging validates logging configuration
func (m *Manager) validateLogging() error {
	log := &m.config.Logging

	// Validate level
	validLevels := []string{"trace", "debug", "info", "warn", "error", "fatal", "panic"}
	if !contains(validLevels, log.Level) {
		return fmt.Errorf("invalid log level: %s, must be one of: %s", 
			log.Level, strings.Join(validLevels, ", "))
	}

	// Validate format
	validFormats := []string{"text", "json"}
	if !contains(validFormats, log.Format) {
		return fmt.Errorf("invalid log format: %s, must be one of: %s", 
			log.Format, strings.Join(validFormats, ", "))
	}

	// Validate output
	validOutputs := []string{"stdout", "stderr", "file"}
	if !contains(validOutputs, log.Output) {
		return fmt.Errorf("invalid log output: %s, must be one of: %s", 
			log.Output, strings.Join(validOutputs, ", "))
	}

	// If output is file, validate file path
	if log.Output == "file" {
		if log.FilePath == "" {
			return fmt.Errorf("log file path cannot be empty when output is file")
		}

		// Ensure directory exists or can be created
		dir := filepath.Dir(log.FilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("cannot create log directory %s: %w", dir, err)
		}
	}

	// Validate rotation settings
	if log.MaxSize <= 0 {
		return fmt.Errorf("log max size must be positive: %d", log.MaxSize)
	}

	if log.MaxBackups < 0 {
		return fmt.Errorf("log max backups cannot be negative: %d", log.MaxBackups)
	}

	if log.MaxAge < 0 {
		return fmt.Errorf("log max age cannot be negative: %d", log.MaxAge)
	}

	return nil
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
