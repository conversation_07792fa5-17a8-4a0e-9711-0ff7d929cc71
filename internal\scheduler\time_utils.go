package scheduler

import (
	"context"
	"time"
)

// calculateNextTestTime calculates the next test time based on the schedule mode
func (s *DefaultScheduler) calculateNextTestTime() time.Time {
	now := time.Now().In(s.timezone)

	switch ScheduleMode(s.config.Mode) {
	case ScheduleModeOdd:
		return s.calculateNextOddHour(now)
	case ScheduleModeEven:
		return s.calculateNextEvenHour(now)
	case ScheduleModeAlways:
		return s.calculateNextHour(now)
	default:
		// Default to odd mode
		return s.calculateNextOddHour(now)
	}
}

// calculateNextOddHour calculates the next odd hour
func (s *DefaultScheduler) calculateNextOddHour(now time.Time) time.Time {
	// Odd hours: 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23
	currentHour := now.Hour()

	// Start from the next hour
	nextHour := currentHour + 1

	// Find the next odd hour
	for nextHour%2 == 0 {
		nextHour++
		if nextHour >= 24 {
			nextHour = 1 // Next day, start with hour 1
			break
		}
	}

	// If we've gone past 23, move to next day
	if nextHour >= 24 {
		nextDay := now.AddDate(0, 0, 1)
		return time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 1, 0, 0, 0, s.timezone)
	}

	// Same day
	return time.Date(now.Year(), now.Month(), now.Day(), nextHour, 0, 0, 0, s.timezone)
}

// calculateNextEvenHour calculates the next even hour
func (s *DefaultScheduler) calculateNextEvenHour(now time.Time) time.Time {
	// Even hours: 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22
	currentHour := now.Hour()

	// Start from the next hour
	nextHour := currentHour + 1

	// Find the next even hour
	for nextHour%2 != 0 {
		nextHour++
		if nextHour >= 24 {
			nextHour = 0 // Next day, start with hour 0
			break
		}
	}

	// If we've gone past 23, move to next day
	if nextHour >= 24 {
		nextDay := now.AddDate(0, 0, 1)
		return time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, s.timezone)
	}

	// Same day
	return time.Date(now.Year(), now.Month(), now.Day(), nextHour, 0, 0, 0, s.timezone)
}

// calculateNextHour calculates the next hour (always mode)
func (s *DefaultScheduler) calculateNextHour(now time.Time) time.Time {
	nextHour := now.Hour() + 1

	if nextHour >= 24 {
		// Next day
		nextDay := now.AddDate(0, 0, 1)
		return time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, s.timezone)
	}

	// Same day
	return time.Date(now.Year(), now.Month(), now.Day(), nextHour, 0, 0, 0, s.timezone)
}

// shouldRunTest determines if a test should run for the given hour
func (s *DefaultScheduler) shouldRunTest(hour int) bool {
	switch ScheduleMode(s.config.Mode) {
	case ScheduleModeOdd:
		return hour%2 == 1 // Odd hours: 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23
	case ScheduleModeEven:
		return hour%2 == 0 // Even hours: 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22
	case ScheduleModeAlways:
		return true // Every hour
	default:
		return false
	}
}

// isTestAlreadyExecuted checks if a test has already been executed for the given hour
func (s *DefaultScheduler) isTestAlreadyExecuted(ctx context.Context, hour int) (bool, error) {
	now := time.Now().In(s.timezone)

	// 检查今天这个小时是否已有测试记录
	startOfHour := time.Date(now.Year(), now.Month(), now.Day(), hour, 0, 0, 0, s.timezone)
	// 在实际部署时，这里会查询数据库检查是否已有测试记录
	_ = startOfHour // 占位符，避免未使用变量警告

	// Query database for existing test records
	// This would need to be implemented based on your database schema
	// For now, we'll use a simple in-memory check

	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.lastTestTime != nil {
		lastTestHour := s.lastTestTime.In(s.timezone).Hour()
		lastTestDate := s.lastTestTime.In(s.timezone).Format("2006-01-02")
		currentDate := now.Format("2006-01-02")

		// If we already ran a test for this hour today, skip it
		if lastTestHour == hour && lastTestDate == currentDate {
			return true, nil
		}
	}

	return false, nil
}

// getTimeUntilNextTest calculates the duration until the next test
func (s *DefaultScheduler) getTimeUntilNextTest() time.Duration {
	nextTime := s.calculateNextTestTime()
	now := time.Now().In(s.timezone)

	duration := nextTime.Sub(now)
	if duration < 0 {
		// If the calculated time is in the past, add 24 hours
		duration += 24 * time.Hour
	}

	return duration
}

// formatScheduleMode returns a human-readable description of the schedule mode
func (s *DefaultScheduler) formatScheduleMode() string {
	switch ScheduleMode(s.config.Mode) {
	case ScheduleModeOdd:
		return "Odd hours (1,3,5,7,9,11,13,15,17,19,21,23)"
	case ScheduleModeEven:
		return "Even hours (0,2,4,6,8,10,12,14,16,18,20,22)"
	case ScheduleModeAlways:
		return "Every hour (0-23)"
	default:
		return "Unknown mode"
	}
}

// isValidHour checks if the hour is valid (0-23)
func isValidHour(hour int) bool {
	return hour >= 0 && hour <= 23
}

// getCurrentHourInTimezone returns the current hour in the scheduler's timezone
func (s *DefaultScheduler) getCurrentHourInTimezone() int {
	return time.Now().In(s.timezone).Hour()
}

// getStartOfHour returns the start of the given hour for today
func (s *DefaultScheduler) getStartOfHour(hour int) time.Time {
	now := time.Now().In(s.timezone)
	return time.Date(now.Year(), now.Month(), now.Day(), hour, 0, 0, 0, s.timezone)
}

// getEndOfHour returns the end of the given hour for today
func (s *DefaultScheduler) getEndOfHour(hour int) time.Time {
	return s.getStartOfHour(hour).Add(time.Hour)
}
