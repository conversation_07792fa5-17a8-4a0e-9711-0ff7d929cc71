package database

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
)

func TestNew(t *testing.T) {
	// 为测试数据库创建临时目录
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := &Config{
		Path:            dbPath,
		MaxOpenConns:    5,
		MaxIdleConns:    2,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 15,
		BusyTimeout:     time.Second * 10,
		JournalMode:     "WAL",
		Synchronous:     "NORMAL",
		CacheSize:       -32000,
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // 减少测试中的噪音

	db, err := New(config, logger)
	if err != nil {
		t.Fatalf("创建数据库失败: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.<PERSON>(); err != nil {
		t.Fatalf("ping 数据库失败: %v", err)
	}

	// 检查数据库文件是否已创建
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		t.Fatalf("数据库文件未创建: %s", dbPath)
	}
}

func TestMigrate(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	// 检查表是否已创建
	tables := []string{"servers", "hourly_results", "sync_status", "system_config", "schema_migrations"}
	for _, table := range tables {
		var count int
		err := db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", table).Scan(&count)
		if err != nil {
			t.Fatalf("检查表 %s 失败: %v", table, err)
		}
		if count == 0 {
			t.Fatalf("表 %s 未创建", table)
		}
	}

	// 检查迁移状态
	status, err := db.GetMigrationStatus()
	if err != nil {
		t.Fatalf("获取迁移状态失败: %v", err)
	}

	if len(status) == 0 {
		t.Fatal("未返回迁移状态")
	}

	// 所有迁移都应该已应用
	for _, s := range status {
		if !s.Applied {
			t.Fatalf("迁移 %d 未应用", s.Version)
		}
	}
}

func TestHealth(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 首先运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	// 测试健康检查
	if err := db.Health(); err != nil {
		t.Fatalf("健康检查失败: %v", err)
	}
}

func TestGetStats(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	// 获取初始统计信息
	stats, err := db.GetStats()
	if err != nil {
		t.Fatalf("获取统计信息失败: %v", err)
	}

	// 检查初始值
	if stats.TotalServers != 0 {
		t.Errorf("预期总服务器数为 0，得到 %d", stats.TotalServers)
	}
	if stats.EnabledServers != 0 {
		t.Errorf("预期启用服务器数为 0，得到 %d", stats.EnabledServers)
	}
	if stats.TotalResults != 0 {
		t.Errorf("预期总结果数为 0，得到 %d", stats.TotalResults)
	}
	if stats.ResultsToday != 0 {
		t.Errorf("预期今天结果数为 0，得到 %d", stats.ResultsToday)
	}
	if stats.DatabaseSize <= 0 {
		t.Errorf("预期数据库大小为正数，得到 %d", stats.DatabaseSize)
	}
}

func TestVacuum(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	// 测试清理
	if err := db.Vacuum(); err != nil {
		t.Fatalf("清理数据库失败: %v", err)
	}
}

func TestBackup(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	// 创建备份
	tempDir := t.TempDir()
	backupPath := filepath.Join(tempDir, "backup.db")

	if err := db.Backup(backupPath); err != nil {
		t.Fatalf("备份数据库失败: %v", err)
	}

	// 检查备份文件是否已创建
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		t.Fatalf("备份文件未创建: %s", backupPath)
	}

	// 通过打开备份来验证其有效性
	backupConfig := &Config{
		Path:        backupPath,
		JournalMode: "WAL",
		Synchronous: "NORMAL",
	}

	backupDB, err := New(backupConfig, logrus.New())
	if err != nil {
		t.Fatalf("打开备份数据库失败: %v", err)
	}
	defer backupDB.Close()

	// 测试备份健康状况
	if err := backupDB.Health(); err != nil {
		t.Fatalf("备份数据库健康检查失败: %v", err)
	}
}

func TestCleanupOldData(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 运行迁移
	if err := db.Migrate(); err != nil {
		t.Fatalf("运行迁移失败: %v", err)
	}

	repo := NewRepository(db, logrus.New())

	// 插入测试数据
	server := &Server{
		Name:    "test-server",
		Host:    "************",
		Port:    55201,
		Enabled: true,
	}
	if err := repo.CreateServer(server); err != nil {
		t.Fatalf("创建测试服务器失败: %v", err)
	}

	// 插入旧的每小时结果
	oldTime := time.Now().AddDate(0, 0, -35).Truncate(time.Second) // 35 天前
	oldResult := &HourlyResult{
		ServerName: "test-server",
		TestHour:   oldTime,
		TCPStatus:  StatusSuccess,
		UDPStatus:  StatusSuccess,
	}
	if err := repo.CreateOrUpdateHourlyResult(oldResult); err != nil {
		t.Fatalf("创建旧的每小时结果失败: %v", err)
	}

	// 插入最近的每小时结果
	recentTime := time.Now().AddDate(0, 0, -1).Truncate(time.Second) // 1 天前
	recentResult := &HourlyResult{
		ServerName: "test-server",
		TestHour:   recentTime,
		TCPStatus:  StatusSuccess,
		UDPStatus:  StatusSuccess,
	}
	if err := repo.CreateOrUpdateHourlyResult(recentResult); err != nil {
		t.Fatalf("创建最近的每小时结果失败: %v", err)
	}

	// 清理 30 天前的数据
	if err := db.CleanupOldData(30); err != nil {
		t.Fatalf("清理旧数据失败: %v", err)
	}

	// 验证旧数据是否已删除
	results, err := repo.GetHourlyResults("", time.Time{}, time.Time{}, 0)
	if err != nil {
		t.Fatalf("获取每小时结果失败: %v", err)
	}

	if len(results) != 1 {
		t.Fatalf("清理后预期 1 个结果，得到 %d", len(results))
	}

	// 验证最近的数据是否保留
	if !results[0].TestHour.Equal(recentTime) {
		t.Errorf("预期保留最近的结果，得到时间 %v，预期 %v", results[0].TestHour, recentTime)
	}
}

func TestCleanupOldDataInvalidRetention(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// 测试无效的保留天数
	if err := db.CleanupOldData(0); err == nil {
		t.Fatal("预期零保留天数会出错")
	}

	if err := db.CleanupOldData(-1); err == nil {
		t.Fatal("预期负保留天数会出错")
	}
}

// setupTestDB 创建一个测试数据库实例
func setupTestDB(t *testing.T) *DB {
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := &Config{
		Path:        dbPath,
		JournalMode: "WAL",
		Synchronous: "NORMAL",
		CacheSize:   -32000,
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // 减少测试中的噪音

	db, err := New(config, logger)
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}

	return db
}
