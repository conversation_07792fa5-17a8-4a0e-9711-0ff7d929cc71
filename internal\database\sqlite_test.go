package database

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
)

func TestNew(t *testing.T) {
	// Create temporary directory for test database
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := &Config{
		Path:            dbPath,
		MaxOpenConns:    5,
		MaxIdleConns:    2,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 15,
		BusyTimeout:     time.Second * 10,
		JournalMode:     "WAL",
		Synchronous:     "NORMAL",
		CacheSize:       -32000,
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	db, err := New(config, logger)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Test database connection
	if err := db.<PERSON>(); err != nil {
		t.Fatalf("Failed to ping database: %v", err)
	}

	// Check if database file was created
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		t.Fatalf("Database file was not created: %s", dbPath)
	}
}

func TestMigrate(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	// Check if tables were created
	tables := []string{"servers", "hourly_results", "sync_status", "system_config", "schema_migrations"}
	for _, table := range tables {
		var count int
		err := db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", table).Scan(&count)
		if err != nil {
			t.Fatalf("Failed to check table %s: %v", table, err)
		}
		if count == 0 {
			t.Fatalf("Table %s was not created", table)
		}
	}

	// Check migration status
	status, err := db.GetMigrationStatus()
	if err != nil {
		t.Fatalf("Failed to get migration status: %v", err)
	}

	if len(status) == 0 {
		t.Fatal("No migration status returned")
	}

	// All migrations should be applied
	for _, s := range status {
		if !s.Applied {
			t.Fatalf("Migration %d was not applied", s.Version)
		}
	}
}

func TestHealth(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations first
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	// Test health check
	if err := db.Health(); err != nil {
		t.Fatalf("Health check failed: %v", err)
	}
}

func TestGetStats(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	// Get initial stats
	stats, err := db.GetStats()
	if err != nil {
		t.Fatalf("Failed to get stats: %v", err)
	}

	// Check initial values
	if stats.TotalServers != 0 {
		t.Errorf("Expected 0 total servers, got %d", stats.TotalServers)
	}
	if stats.EnabledServers != 0 {
		t.Errorf("Expected 0 enabled servers, got %d", stats.EnabledServers)
	}
	if stats.TotalResults != 0 {
		t.Errorf("Expected 0 total results, got %d", stats.TotalResults)
	}
	if stats.ResultsToday != 0 {
		t.Errorf("Expected 0 results today, got %d", stats.ResultsToday)
	}
	if stats.DatabaseSize <= 0 {
		t.Errorf("Expected positive database size, got %d", stats.DatabaseSize)
	}
}

func TestVacuum(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	// Test vacuum
	if err := db.Vacuum(); err != nil {
		t.Fatalf("Failed to vacuum database: %v", err)
	}
}

func TestBackup(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	// Create backup
	tempDir := t.TempDir()
	backupPath := filepath.Join(tempDir, "backup.db")

	if err := db.Backup(backupPath); err != nil {
		t.Fatalf("Failed to backup database: %v", err)
	}

	// Check if backup file was created
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		t.Fatalf("Backup file was not created: %s", backupPath)
	}

	// Verify backup is valid by opening it
	backupConfig := &Config{
		Path:        backupPath,
		JournalMode: "WAL",
		Synchronous: "NORMAL",
	}

	backupDB, err := New(backupConfig, logrus.New())
	if err != nil {
		t.Fatalf("Failed to open backup database: %v", err)
	}
	defer backupDB.Close()

	// Test backup health
	if err := backupDB.Health(); err != nil {
		t.Fatalf("Backup database health check failed: %v", err)
	}
}

func TestCleanupOldData(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Run migrations
	if err := db.Migrate(); err != nil {
		t.Fatalf("Failed to run migrations: %v", err)
	}

	repo := NewRepository(db, logrus.New())

	// Insert test data
	server := &Server{
		Name:    "test-server",
		Host:    "************",
		Port:    55201,
		Enabled: true,
	}
	if err := repo.CreateServer(server); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	// Insert old hourly result
	oldTime := time.Now().AddDate(0, 0, -35).Truncate(time.Second) // 35 days ago
	oldResult := &HourlyResult{
		ServerName: "test-server",
		TestHour:   oldTime,
		TCPStatus:  StatusSuccess,
		UDPStatus:  StatusSuccess,
	}
	if err := repo.CreateOrUpdateHourlyResult(oldResult); err != nil {
		t.Fatalf("Failed to create old hourly result: %v", err)
	}

	// Insert recent hourly result
	recentTime := time.Now().AddDate(0, 0, -1).Truncate(time.Second) // 1 day ago
	recentResult := &HourlyResult{
		ServerName: "test-server",
		TestHour:   recentTime,
		TCPStatus:  StatusSuccess,
		UDPStatus:  StatusSuccess,
	}
	if err := repo.CreateOrUpdateHourlyResult(recentResult); err != nil {
		t.Fatalf("Failed to create recent hourly result: %v", err)
	}

	// Cleanup data older than 30 days
	if err := db.CleanupOldData(30); err != nil {
		t.Fatalf("Failed to cleanup old data: %v", err)
	}

	// Verify old data was deleted
	results, err := repo.GetHourlyResults("", time.Time{}, time.Time{}, 0)
	if err != nil {
		t.Fatalf("Failed to get hourly results: %v", err)
	}

	if len(results) != 1 {
		t.Fatalf("Expected 1 result after cleanup, got %d", len(results))
	}

	// Verify recent data was kept
	if !results[0].TestHour.Equal(recentTime) {
		t.Errorf("Expected recent result to be kept, got time %v, expected %v", results[0].TestHour, recentTime)
	}
}

func TestCleanupOldDataInvalidRetention(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Test invalid retention days
	if err := db.CleanupOldData(0); err == nil {
		t.Fatal("Expected error for zero retention days")
	}

	if err := db.CleanupOldData(-1); err == nil {
		t.Fatal("Expected error for negative retention days")
	}
}

// setupTestDB creates a test database instance
func setupTestDB(t *testing.T) *DB {
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := &Config{
		Path:        dbPath,
		JournalMode: "WAL",
		Synchronous: "NORMAL",
		CacheSize:   -32000,
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	db, err := New(config, logger)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}

	return db
}
