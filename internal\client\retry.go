package client

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/sirupsen/logrus"
)

// RetryStrategy 定义了重试策略的接口
type RetryStrategy interface {
	// ShouldRetry 判断操作是否应该重试
	ShouldRetry(attempt int, err error) bool

	// NextDelay 计算下次重试前的延迟
	NextDelay(attempt int) time.Duration

	// MaxAttempts 返回最大重试次数
	MaxAttempts() int
}

// ExponentialBackoffStrategy 实现了指数退避重试策略
type ExponentialBackoffStrategy struct {
	maxAttempts int
	baseDelay   time.Duration
	maxDelay    time.Duration
	multiplier  float64
	jitter      bool
	logger      *logrus.Logger
}

// NewExponentialBackoffStrategy 创建一个新的指数退避策略
func NewExponentialBackoffStrategy(maxAttempts int, baseDelay, maxDelay time.Duration, logger *logrus.Logger) *ExponentialBackoffStrategy {
	if logger == nil {
		logger = logrus.New()
	}

	return &ExponentialBackoffStrategy{
		maxAttempts: maxAttempts,
		baseDelay:   baseDelay,
		maxDelay:    maxDelay,
		multiplier:  2.0,
		jitter:      true,
		logger:      logger,
	}
}

// ShouldRetry 判断操作是否应该重试
func (s *ExponentialBackoffStrategy) ShouldRetry(attempt int, err error) bool {
	if attempt >= s.maxAttempts {
		return false
	}

	return IsRetryableError(err)
}

// NextDelay 使用指数退避计算下次重试前的延迟
func (s *ExponentialBackoffStrategy) NextDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return s.baseDelay
	}

	// 计算指数延迟
	delay := float64(s.baseDelay) * math.Pow(s.multiplier, float64(attempt-1))

	// Apply maximum delay limit
	if delay > float64(s.maxDelay) {
		delay = float64(s.maxDelay)
	}

	// 添加抖动以避免雷鸣般的羊群效应
	if s.jitter {
		jitterRange := delay * 0.1 // 10% jitter
		jitter := (rand.Float64() - 0.5) * 2 * jitterRange
		delay += jitter
	}

	// 确保延迟不为负
	if delay < 0 {
		delay = float64(s.baseDelay)
	}

	return time.Duration(delay)
}

// MaxAttempts 返回最大重试次数
func (s *ExponentialBackoffStrategy) MaxAttempts() int {
	return s.maxAttempts
}

// FixedDelayStrategy 实现了固定延迟重试策略
type FixedDelayStrategy struct {
	maxAttempts int
	delay       time.Duration
	logger      *logrus.Logger
}

// NewFixedDelayStrategy 创建一个新的固定延迟策略
func NewFixedDelayStrategy(maxAttempts int, delay time.Duration, logger *logrus.Logger) *FixedDelayStrategy {
	if logger == nil {
		logger = logrus.New()
	}

	return &FixedDelayStrategy{
		maxAttempts: maxAttempts,
		delay:       delay,
		logger:      logger,
	}
}

// ShouldRetry 判断操作是否应该重试
func (s *FixedDelayStrategy) ShouldRetry(attempt int, err error) bool {
	if attempt >= s.maxAttempts {
		return false
	}

	return IsRetryableError(err)
}

// NextDelay 返回固定延迟
func (s *FixedDelayStrategy) NextDelay(attempt int) time.Duration {
	return s.delay
}

// MaxAttempts 返回最大重试次数
func (s *FixedDelayStrategy) MaxAttempts() int {
	return s.maxAttempts
}

// Retrier 处理操作的重试逻辑
type Retrier struct {
	strategy RetryStrategy
	logger   *logrus.Logger
}

// NewRetrier 使用给定策略创建一个新的重试器
func NewRetrier(strategy RetryStrategy, logger *logrus.Logger) *Retrier {
	if logger == nil {
		logger = logrus.New()
	}

	return &Retrier{
		strategy: strategy,
		logger:   logger,
	}
}

// RetryFunc 表示可以重试的函数
type RetryFunc func(ctx context.Context, attempt int) error

// Execute 执行带有重试逻辑的函数
func (r *Retrier) Execute(ctx context.Context, operation string, fn RetryFunc) error {
	var lastErr error

	for attempt := 1; attempt <= r.strategy.MaxAttempts(); attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 执行函数
		err := fn(ctx, attempt)
		if err == nil {
			// 成功
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"operation": operation,
					"attempt":   attempt,
				}).Info("操作在重试后成功")
			}
			return nil
		}

		lastErr = err

		// 检查是否应该重试
		if !r.strategy.ShouldRetry(attempt, err) {
			r.logger.WithFields(logrus.Fields{
				"operation": operation,
				"attempt":   attempt,
				"error":     err.Error(),
			}).Debug("Operation failed, not retryable")
			break
		}

		// 检查是否已达到最大尝试次数
		if attempt >= r.strategy.MaxAttempts() {
			r.logger.WithFields(logrus.Fields{
				"operation":    operation,
				"max_attempts": r.strategy.MaxAttempts(),
				"final_error":  err.Error(),
			}).Warn("Operation failed after maximum retry attempts")
			break
		}

		// 计算下次尝试的延迟
		delay := r.strategy.NextDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"operation":    operation,
			"attempt":      attempt,
			"error":        err.Error(),
			"next_delay":   delay,
			"next_attempt": attempt + 1,
		}).Warn("Operation failed, retrying")

		// 在下次尝试前等待
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续下一次尝试
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", operation, r.strategy.MaxAttempts(), lastErr)
}

// ExecuteWithResult 执行带有重试逻辑的函数并返回结果
func (r *Retrier) ExecuteWithResult(ctx context.Context, operation string, fn func(ctx context.Context, attempt int) (interface{}, error)) (interface{}, error) {
	var result interface{}

	err := r.Execute(ctx, operation, func(ctx context.Context, attempt int) error {
		var err error
		result, err = fn(ctx, attempt)
		return err
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}
