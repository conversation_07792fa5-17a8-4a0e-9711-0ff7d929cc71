package client

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/sirupsen/logrus"
)

// RetryStrategy defines the interface for retry strategies
type RetryStrategy interface {
	// ShouldRetry determines if an operation should be retried
	ShouldRetry(attempt int, err error) bool

	// NextDelay calculates the delay before the next retry
	NextDelay(attempt int) time.Duration

	// MaxAttempts returns the maximum number of retry attempts
	MaxAttempts() int
}

// ExponentialBackoffStrategy implements exponential backoff retry strategy
type ExponentialBackoffStrategy struct {
	maxAttempts int
	baseDelay   time.Duration
	maxDelay    time.Duration
	multiplier  float64
	jitter      bool
	logger      *logrus.Logger
}

// NewExponentialBackoffStrategy creates a new exponential backoff strategy
func NewExponentialBackoffStrategy(maxAttempts int, baseDelay, maxDelay time.Duration, logger *logrus.Logger) *ExponentialBackoffStrategy {
	if logger == nil {
		logger = logrus.New()
	}

	return &ExponentialBackoffStrategy{
		maxAttempts: maxAttempts,
		baseDelay:   baseDelay,
		maxDelay:    maxDelay,
		multiplier:  2.0,
		jitter:      true,
		logger:      logger,
	}
}

// ShouldRetry determines if an operation should be retried
func (s *ExponentialBackoffStrategy) ShouldRetry(attempt int, err error) bool {
	if attempt >= s.maxAttempts {
		return false
	}

	return IsRetryableError(err)
}

// NextDelay calculates the delay before the next retry using exponential backoff
func (s *ExponentialBackoffStrategy) NextDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return s.baseDelay
	}

	// Calculate exponential delay
	delay := float64(s.baseDelay) * math.Pow(s.multiplier, float64(attempt-1))

	// Apply maximum delay limit
	if delay > float64(s.maxDelay) {
		delay = float64(s.maxDelay)
	}

	// Add jitter to avoid thundering herd
	if s.jitter {
		jitterRange := delay * 0.1 // 10% jitter
		jitter := (rand.Float64() - 0.5) * 2 * jitterRange
		delay += jitter
	}

	// Ensure delay is not negative
	if delay < 0 {
		delay = float64(s.baseDelay)
	}

	return time.Duration(delay)
}

// MaxAttempts returns the maximum number of retry attempts
func (s *ExponentialBackoffStrategy) MaxAttempts() int {
	return s.maxAttempts
}

// FixedDelayStrategy implements fixed delay retry strategy
type FixedDelayStrategy struct {
	maxAttempts int
	delay       time.Duration
	logger      *logrus.Logger
}

// NewFixedDelayStrategy creates a new fixed delay strategy
func NewFixedDelayStrategy(maxAttempts int, delay time.Duration, logger *logrus.Logger) *FixedDelayStrategy {
	if logger == nil {
		logger = logrus.New()
	}

	return &FixedDelayStrategy{
		maxAttempts: maxAttempts,
		delay:       delay,
		logger:      logger,
	}
}

// ShouldRetry determines if an operation should be retried
func (s *FixedDelayStrategy) ShouldRetry(attempt int, err error) bool {
	if attempt >= s.maxAttempts {
		return false
	}

	return IsRetryableError(err)
}

// NextDelay returns the fixed delay
func (s *FixedDelayStrategy) NextDelay(attempt int) time.Duration {
	return s.delay
}

// MaxAttempts returns the maximum number of retry attempts
func (s *FixedDelayStrategy) MaxAttempts() int {
	return s.maxAttempts
}

// Retrier handles retry logic for operations
type Retrier struct {
	strategy RetryStrategy
	logger   *logrus.Logger
}

// NewRetrier creates a new retrier with the given strategy
func NewRetrier(strategy RetryStrategy, logger *logrus.Logger) *Retrier {
	if logger == nil {
		logger = logrus.New()
	}

	return &Retrier{
		strategy: strategy,
		logger:   logger,
	}
}

// RetryFunc represents a function that can be retried
type RetryFunc func(ctx context.Context, attempt int) error

// Execute executes a function with retry logic
func (r *Retrier) Execute(ctx context.Context, operation string, fn RetryFunc) error {
	var lastErr error

	for attempt := 1; attempt <= r.strategy.MaxAttempts(); attempt++ {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute the function
		err := fn(ctx, attempt)
		if err == nil {
			// Success
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"operation": operation,
					"attempt":   attempt,
				}).Info("Operation succeeded after retry")
			}
			return nil
		}

		lastErr = err

		// Check if we should retry
		if !r.strategy.ShouldRetry(attempt, err) {
			r.logger.WithFields(logrus.Fields{
				"operation": operation,
				"attempt":   attempt,
				"error":     err.Error(),
			}).Debug("Operation failed, not retryable")
			break
		}

		// Check if we've reached max attempts
		if attempt >= r.strategy.MaxAttempts() {
			r.logger.WithFields(logrus.Fields{
				"operation":    operation,
				"max_attempts": r.strategy.MaxAttempts(),
				"final_error":  err.Error(),
			}).Warn("Operation failed after maximum retry attempts")
			break
		}

		// Calculate delay for next attempt
		delay := r.strategy.NextDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"operation":    operation,
			"attempt":      attempt,
			"error":        err.Error(),
			"next_delay":   delay,
			"next_attempt": attempt + 1,
		}).Warn("Operation failed, retrying")

		// Wait before next attempt
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", operation, r.strategy.MaxAttempts(), lastErr)
}

// ExecuteWithResult executes a function with retry logic and returns a result
func (r *Retrier) ExecuteWithResult(ctx context.Context, operation string, fn func(ctx context.Context, attempt int) (interface{}, error)) (interface{}, error) {
	var result interface{}

	err := r.Execute(ctx, operation, func(ctx context.Context, attempt int) error {
		var err error
		result, err = fn(ctx, attempt)
		return err
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}
