# iPerf3 控制程序技术设计清单

## 项目概述
- **目标平台**: OpenWRT ARM64
- **核心功能**: 双OpenWRT服务端控制12台服务器客户端进行单向测速
- **测试方向**: 服务器到OpenWRT的单向下行速度测试
- **协议支持**: TCP和UDP
- **性能要求**: 高性能、低资源占用、并发优化
- **部署架构**: 双OpenWRT奇偶小时调度，SQLite数据同步，Web可视化
- **数据同步**: 两台OpenWRT互相通信，保持SQLite数据一致

## 1. 技术栈选择

### 1.1 编程语言
- [x] **Go 1.21+**
  - 原生ARM64支持
  - 静态编译，单文件部署
  - 优秀的并发性能
  - 小内存占用

### 1.2 核心依赖
```go
// 最小化依赖原则
github.com/spf13/cobra    // CLI框架
github.com/spf13/viper    // 配置管理
github.com/sirupsen/logrus // 日志系统
gopkg.in/yaml.v3          // YAML解析
modernc.org/sqlite        // SQLite数据库（纯Go实现）
github.com/gin-gonic/gin  // Web框架（轻量级）
github.com/gorilla/websocket // WebSocket支持（实时数据）
```

### 1.3 避免的依赖
- [ ] 重型Web框架 (Gin, Echo等)
- [ ] ORM框架 (GORM等)
- [ ] 图形界面库

## 2. 架构设计

### 2.1 分层架构
```
├── cmd/                    # 应用入口
├── internal/              # 内部实现
│   ├── app/              # 应用层
│   ├── domain/           # 领域层
│   ├── infrastructure/   # 基础设施层
│   └── interfaces/       # 接口层
├── pkg/                  # 公共包
└── configs/              # 配置文件
```

### 2.2 核心模块
- [x] **主控制器** (Controller): 任务调度和流程控制
- [x] **客户端管理器** (ClientManager): 管理服务器客户端连接
- [x] **测试协调器** (TestCoordinator): 协调测试流程和客户端启停
- [x] **结果处理器** (ResultHandler): 数据收集和分析
- [x] **配置管理器** (ConfigManager): 参数和服务器管理
- [x] **数据存储模块** (Storage): SQLite数据持久化
- [x] **数据同步模块** (Sync): 双OpenWRT数据同步
- [x] **调度模块** (Scheduler): 奇偶小时调度控制
- [x] **Web服务模块** (WebServer): 可视化前端服务

## 3. 性能优化策略

### 3.1 并发控制
- [x] **分批测试**: 12台服务器分成3-4批
- [x] **错峰启动**: 时间偏移避免同时开始
- [x] **自适应并发**: 根据系统负载动态调整
- [x] **Worker Pool模式**: 控制goroutine数量

### 3.2 iperf3参数优化
#### TCP测试参数
```bash
iperf3 -c <server> -P 4 -w 512K -M 1460 -t 30 --reverse --get-server-output -f m
```
- `-P 4`: 4个并行流
- `-w 512K`: TCP窗口大小
- `-M 1460`: MSS大小
- `-t 30`: 测试30秒
- `--reverse`: 反向测试(服务器发送)

#### UDP测试参数  
```bash
iperf3 -c <server> -u -b 500M -l 1472 -t 10 --reverse -f m
```
- `-u`: UDP模式
- `-b 500M`: 目标带宽500Mbps
- `-l 1472`: UDP包大小(避免分片)
- `-t 10`: 测试10秒

### 3.3 系统级优化
```bash
# 网络缓冲区优化
echo 134217728 > /proc/sys/net/core/rmem_max
echo 134217728 > /proc/sys/net/core/wmem_max

# 文件描述符限制
ulimit -n 65536

# CPU绑定
taskset -c 0-3 ./iperf3-controller
```

## 4. 核心接口设计

### 4.1 测试执行器接口
```go
type TestExecutor interface {
    ExecuteTCP(ctx context.Context, target string, params TCPParams) (*TestResult, error)
    ExecuteUDP(ctx context.Context, target string, params UDPParams) (*TestResult, error)
    Validate(target string) error
}
```

### 4.2 工作池接口
```go
type WorkerPool interface {
    Submit(task *TestTask) error
    SetConcurrency(n int)
    Start(ctx context.Context) error
    Stop() error
    Results() <-chan *TestResult
}
```

### 4.3 结果处理接口
```go
type ResultHandler interface {
    Collect(result *TestResult) error
    Aggregate() (*AggregatedResult, error)
    GenerateReport(format string) ([]byte, error)
    Export(path string) error
}
```

## 5. 双服务端调度架构

### 5.1 架构概述
- **服务端A**: 奇数小时发起测试 (1,3,5,7,9,11,13,15,17,19,21,23点)
- **服务端B**: 偶数小时发起测试 (0,2,4,6,8,10,12,14,16,18,20,22点)
- **数据存储**: 各自使用SQLite本地存储，支持数据汇总
- **负载分散**: 有效分散系统负载，提高测试频率

### 5.2 调度策略实现
```go
type ScheduleConfig struct {
    Mode     string `yaml:"mode"`     // "odd", "even", "always"
    Timezone string `yaml:"timezone"` // 时区设置
}

func shouldRunTest(mode string) bool {
    hour := time.Now().Hour()
    switch mode {
    case "odd":
        return hour%2 == 1  // 奇数小时
    case "even":
        return hour%2 == 0  // 偶数小时
    default:
        return true // 默认每小时都运行
    }
}
```

### 5.3 SQLite数据库设计（重新设计）
```sql
-- 服务器客户端配置表
CREATE TABLE servers (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE, -- 服务器名称，如 "server-01"
    host TEXT NOT NULL,        -- 服务器IP地址
    port INTEGER DEFAULT 55201, -- 统一端口55201
    enabled BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 每小时测试结果表（简化设计，只存储关键数据）
CREATE TABLE hourly_results (
    id INTEGER PRIMARY KEY,
    server_name TEXT NOT NULL,     -- 服务器名称
    test_hour TIMESTAMP NOT NULL,  -- 测试小时（格式：2024-01-01 14:00:00）
    tcp_speed_mbps REAL,          -- TCP速度（Mbps）
    udp_speed_mbps REAL,          -- UDP速度（Mbps）
    tcp_status TEXT DEFAULT 'pending', -- 'success', 'failed', 'pending'
    udp_status TEXT DEFAULT 'pending', -- 'success', 'failed', 'pending'
    tcp_error TEXT,               -- TCP错误信息
    udp_error TEXT,               -- UDP错误信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_name, test_hour) -- 确保每小时每台服务器只有一条记录
);

-- 数据同步状态表
CREATE TABLE sync_status (
    id INTEGER PRIMARY KEY,
    peer_ip TEXT NOT NULL,        -- 对端OpenWRT IP
    last_sync TIMESTAMP,          -- 最后同步时间
    sync_status TEXT DEFAULT 'pending', -- 'success', 'failed', 'pending'
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_hourly_results_server_hour ON hourly_results(server_name, test_hour);
CREATE INDEX idx_hourly_results_hour ON hourly_results(test_hour);
CREATE INDEX idx_hourly_results_status ON hourly_results(tcp_status, udp_status);

-- 初始化系统配置
INSERT INTO system_config (key, value, description) VALUES
('schedule_mode', 'odd', '调度模式：odd/even'),
('peer_ip', '', '对端OpenWRT IP地址'),
('peer_port', '55201', '对端OpenWRT端口'),
('web_port', '6066', 'Web服务端口');
```

### 5.4 测试流程设计
```go
// 测试协调流程
func (tc *TestCoordinator) RunHourlyTest() error {
    hour := time.Now().Truncate(time.Hour)

    for _, server := range tc.servers {
        // 1. 通知服务器客户端准备测试
        if err := tc.notifyClient(server, "prepare"); err != nil {
            continue
        }

        // 2. 等待客户端启动iperf3
        time.Sleep(2 * time.Second)

        // 3. 执行TCP测试
        tcpResult := tc.runTCPTest(server)

        // 4. 执行UDP测试
        udpResult := tc.runUDPTest(server)

        // 5. 通知客户端关闭iperf3
        tc.notifyClient(server, "stop")

        // 6. 保存结果到数据库
        tc.saveResults(server.Name, hour, tcpResult, udpResult)

        // 7. 同步数据到对端OpenWRT
        tc.syncToPeer(server.Name, hour, tcpResult, udpResult)
    }

    return nil
}
```

### 5.5 客户端通信协议
```go
// 客户端命令结构
type ClientCommand struct {
    Action string `json:"action"` // "prepare", "stop"
    Port   int    `json:"port"`   // 55201
}

// 服务器客户端参数
// iperf3 -c <openwrt_ip> -p 55201 -n "server-01"
```

### 5.6 Web前端设计
```html
<!-- 使用Tailwind CSS的响应式设计 -->
<div class="container mx-auto p-4">
    <h1 class="text-3xl font-bold mb-6">网络测速监控</h1>

    <!-- 12台服务器网格布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- 每台服务器的卡片 -->
        <div class="bg-white rounded-lg shadow-md p-4" v-for="server in servers">
            <h3 class="text-lg font-semibold mb-2">{{ server.name }}</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>TCP:</span>
                    <span class="font-mono">{{ server.tcp_speed }} Mbps</span>
                </div>
                <div class="flex justify-between">
                    <span>UDP:</span>
                    <span class="font-mono">{{ server.udp_speed }} Mbps</span>
                </div>
                <div class="text-sm text-gray-500">
                    更新时间: {{ server.last_update }}
                </div>
            </div>
        </div>
    </div>
</div>
```

### 5.7 方案优势
- **数据一致性**: 两台OpenWRT数据完全同步，每小时都有完整数据
- **测试频率提升**: 每小时都有测试，数据更及时
- **故障隔离**: 一台OpenWRT故障不影响另一台
- **可视化友好**: Web界面实时显示12台机器状态
- **部署简单**: 统一端口55201，配置清晰
- **扩展性好**: 支持更多服务器和功能扩展

## 6. 配置管理

### 6.1 服务器客户端配置
```yaml
servers:
  - name: "server-01"
    host: "************"
    port: 55201  # 统一端口
    enabled: true
  - name: "server-02"
    host: "************"
    port: 55201
    enabled: true
  # ... 其他10台服务器
```

### 6.2 OpenWRT服务端配置
```yaml
# OpenWRT A (奇数小时)
schedule:
  mode: "odd"  # 奇数小时调度
  timezone: "Asia/Shanghai"

server:
  listen_port: 55201      # iperf3监听端口
  web_port: 6066         # Web界面端口

peer:
  ip: "************"     # 另一台OpenWRT IP
  port: 55201            # 另一台OpenWRT端口
  sync_interval: "5m"    # 数据同步间隔

database:
  path: "/opt/iperf3-controller/data.db"
  backup_interval: "24h"

client_management:
  prepare_timeout: "10s"  # 客户端准备超时
  test_timeout: "60s"     # 测试超时
  stop_timeout: "5s"      # 停止超时
```

### 6.3 客户端启动参数
```bash
# 服务器端客户端启动命令
iperf3 -c <openwrt_ip> -p 55201 -n "server-01"

# OpenWRT服务端启动命令
iperf3-controller -s -p 55201 -aip <peer_openwrt_ip> -ap 55201
```

### 6.3 性能配置
```yaml
performance:
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
    duration: 30
  udp:
    bandwidth: "500M"
    packet_size: 1472
    duration: 10
  concurrency:
    max_workers: 4
    batch_size: 3
    batch_delay: "2s"
```

## 7. 交叉编译和部署

### 7.1 编译命令
```bash
CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build \
  -ldflags="-s -w" \
  -trimpath \
  -o iperf3-controller-arm64 \
  ./cmd/iperf3-controller
```

### 7.2 双OpenWRT部署结构
```
# OpenWRT A（奇数小时，IP: ************）
/opt/iperf3-controller/
├── iperf3-controller-arm64    # 主程序
├── config.yaml               # 奇数小时配置
├── data.db                   # SQLite数据库（与B同步）
├── web/                      # Web前端文件
│   ├── index.html            # 主页面
│   ├── app.js                # Vue.js应用
│   └── style.css             # Tailwind CSS
└── logs/                     # 日志目录

# OpenWRT B（偶数小时，IP: ************）
/opt/iperf3-controller/
├── iperf3-controller-arm64    # 主程序
├── config.yaml               # 偶数小时配置
├── data.db                   # SQLite数据库（与A同步）
├── web/                      # Web前端文件
│   ├── index.html            # 主页面
│   ├── app.js                # Vue.js应用
│   └── style.css             # Tailwind CSS
└── logs/                     # 日志目录

# 12台服务器客户端（示例）
server-01 (************): iperf3 -c ************ -p 55201 -n "server-01"
server-02 (************): iperf3 -c ************ -p 55201 -n "server-02"
...
server-12 (************): iperf3 -c ************ -p 55201 -n "server-12"

# Web访问地址
OpenWRT A: http://************:6066
OpenWRT B: http://************:6066 (数据完全相同)
```

## 8. 监控和日志

### 8.1 关键指标
- 测试成功率
- 平均吞吐量
- 延迟统计
- 系统资源使用率
- 错误率和类型

### 8.2 日志级别
- ERROR: 系统错误和测试失败
- WARN: 性能警告和重试
- INFO: 测试进度和结果
- DEBUG: 详细执行信息

## 9. 错误处理和恢复

### 9.1 重试策略
- 网络超时: 最多重试3次
- 连接失败: 指数退避重试
- 资源不足: 降低并发数重试

### 9.2 故障恢复
- 单个服务器失败不影响其他测试
- 系统资源不足时自动降级
- 异常退出时保存已完成的结果

## 10. 测试策略

### 10.1 单元测试
- 各模块接口测试
- 参数解析和验证
- 错误处理逻辑

### 10.2 集成测试
- 端到端测试流程
- 并发场景测试
- 资源限制测试

### 10.3 性能测试
- 内存使用测试
- CPU占用测试
- 网络性能测试

## 11. 实施路线图（重新规划）

### 11.1 第一阶段：核心功能开发
- [ ] 重新设计SQLite数据库表结构
- [ ] 实现客户端管理和通信协议
- [ ] 开发测试协调器和流程控制
- [ ] 实现奇数/偶数小时调度逻辑
- [ ] 单元测试和集成测试

### 11.2 第二阶段：数据同步功能
- [ ] 实现双OpenWRT数据同步机制
- [ ] 添加同步状态监控和错误处理
- [ ] 数据一致性验证和冲突解决
- [ ] 同步功能压力测试

### 11.3 第三阶段：Web前端开发
- [ ] 使用Tailwind CSS设计响应式界面
- [ ] 实现12台服务器状态展示
- [ ] 添加实时数据更新（WebSocket）
- [ ] 历史数据查询和图表展示
- [ ] 移动端适配和优化

### 11.4 第四阶段：部署和优化
- [ ] 双OpenWRT环境部署和配置
- [ ] 12台服务器客户端配置
- [ ] 生产环境测试和调优
- [ ] 监控告警和运维工具
- [ ] 文档完善和培训

## 12. 审查检查点

### 12.1 技术选择
- [ ] Go语言是否适合OpenWRT ARM64环境？
- [ ] 依赖库是否过重？
- [ ] 是否需要CGO支持？

### 12.2 架构设计
- [ ] 模块划分是否合理？
- [ ] 接口设计是否清晰？
- [ ] 是否考虑了扩展性？

### 12.3 性能优化
- [ ] 并发策略是否合适？
- [ ] iperf3参数是否最优？
- [ ] 系统优化是否充分？

### 12.4 部署运维
- [ ] 交叉编译是否正确？
- [ ] 配置管理是否灵活？
- [ ] 监控指标是否完整？

### 12.5 双服务端方案评估
- [ ] 奇数/偶数小时调度是否合理？
- [ ] SQLite性能是否满足需求？
- [ ] 数据同步策略是否完善？
- [ ] 故障切换机制是否可靠？

## 13. 风险评估

### 13.1 技术风险
- ARM64兼容性问题
- 内存泄漏风险
- 网络超时处理

### 13.2 性能风险
- 带宽竞争导致结果不准确
- 系统资源不足导致测试失败
- 并发过高导致系统不稳定

### 13.3 双服务端特有风险
- 时间同步问题导致调度冲突
- 数据分散存储带来的管理复杂性
- 两个服务端配置不一致的风险

### 13.4 缓解措施
- 充分的测试验证
- 渐进式部署
- 完善的监控和告警
- NTP时间同步确保调度准确性
- 配置管理工具确保一致性
- 数据备份和恢复机制

---

## 14. 方案可行性结论（更新版）

### 14.1 技术可行性
✅ **高度可行** - 双OpenWRT架构，统一端口55201，SQLite数据同步，Web可视化

### 14.2 架构优势
✅ **架构清晰** - 服务器作客户端，OpenWRT作服务端，角色明确，通信简单

### 14.3 数据一致性
✅ **完全同步** - 两台OpenWRT数据实时同步，每小时都有完整数据，无数据丢失

### 14.4 用户体验
✅ **可视化友好** - Tailwind CSS响应式设计，实时显示12台机器TCP/UDP速度

### 14.5 运维简化
✅ **部署简单** - 统一端口配置，Web界面监控，故障隔离，维护方便

---

**重新设计的双OpenWRT方案完全可行且更加优化：**

🎯 **核心改进**：
- 服务器→客户端，OpenWRT→服务端（角色更合理）
- 统一端口55201，配置简化
- 数据完全同步，每小时都有数据
- Web可视化界面，用户体验佳

🚀 **技术栈**：
- Go + SQLite + Gin + WebSocket
- Tailwind CSS + Vue.js
- 双OpenWRT数据同步

📋 **实施计划**：
1. 核心功能开发（客户端管理+调度）
2. 数据同步机制（双OpenWRT同步）
3. Web前端开发（可视化界面）
4. 部署优化（生产环境）

**关键优势：架构清晰、数据一致、可视化佳、运维简单、扩展性强**
