# iPerf3 Dual OpenWRT Speed Testing System

A high-performance network speed testing system designed for dual OpenWRT architecture with automatic scheduling, data synchronization, and web visualization.

## 🎯 Features

- **Dual OpenWRT Architecture**: Odd/even hour scheduling for load balancing
- **12 Server Management**: Automatic client lifecycle management
- **Real-time Synchronization**: SQLite data sync between OpenWRT devices
- **Web Visualization**: Responsive interface showing TCP/UDP speeds
- **ARM64 Optimized**: Built for OpenWRT ARM64 platforms
- **Low Resource Usage**: Minimal memory and CPU footprint

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   OpenWRT A     │    │   OpenWRT B     │
│  (Odd Hours)    │◄──►│  (Even Hours)   │
│  Port: 55201    │    │  Port: 55201    │
│  Web: 6066      │    │  Web: 6066      │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───▼───┐    ┌───────▼──┐    ┌───────▼──┐
│Server1│    │ Server2  │    │ Server12 │
│Client │    │ Client   │    │ Client   │
└───────┘    └──────────┘    └──────────┘
```

## 🚀 Quick Start

### Prerequisites

- Go 1.21+ (for development)
- OpenWRT ARM64 device
- iperf3 installed on all servers

### Installation

1. **Download Release**
   ```bash
   wget https://github.com/your-repo/iperf3-controller/releases/latest/iperf3-controller-arm64
   chmod +x iperf3-controller-arm64
   ```

2. **Or Build from Source**
   ```bash
   git clone https://github.com/your-repo/iperf3-controller.git
   cd iperf3-controller
   make build-arm64
   ```

### Configuration

1. **Copy Configuration**
   ```bash
   cp configs/config-example.yaml configs/config.yaml
   ```

2. **Edit Configuration**
   ```yaml
   schedule:
     mode: "odd"  # "odd" for OpenWRT A, "even" for OpenWRT B
     timezone: "Asia/Shanghai"
   
   peer:
     ip: "************"     # Other OpenWRT IP
     port: 55201
   
   servers:
     - name: "server-01"
       host: "************"
       port: 55201
       enabled: true
   ```

### Running

1. **Server Mode**
   ```bash
   ./iperf3-controller-arm64 -s -aip ************ -ap 55201
   ```

2. **With Custom Config**
   ```bash
   ./iperf3-controller-arm64 -s -c /path/to/config.yaml
   ```

3. **Development Mode**
   ```bash
   make dev
   ```

## 📊 Web Interface

Access the web interface at `http://your-openwrt-ip:6066`

Features:
- Real-time speed monitoring for all 12 servers
- TCP and UDP speed display
- Historical data visualization
- Mobile-responsive design
- WebSocket real-time updates

## 🔧 Development

### Project Structure

```
iperf3-controller/
├── cmd/iperf3-controller/     # Application entry point
├── internal/                  # Private application code
│   ├── app/                  # Application layer
│   ├── config/               # Configuration management
│   ├── database/             # SQLite operations
│   ├── client/               # Client management
│   ├── coordinator/          # Test coordination
│   ├── scheduler/            # Scheduling system
│   ├── sync/                 # Data synchronization
│   └── web/                  # Web services
├── pkg/                      # Reusable library code
├── web/                      # Frontend assets
├── configs/                  # Configuration files
└── scripts/                  # Build and deploy scripts
```

### Building

```bash
# Build for current platform
make build

# Cross-compile for ARM64
make build-arm64

# Create distribution package
make dist

# Run tests
make test

# Format and lint
make fmt lint
```

### Testing

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific test
go test -v ./internal/database/
```

## 📋 API Reference

### REST API

- `GET /api/servers` - Get server status
- `GET /api/results` - Get test results
- `POST /api/test/trigger` - Trigger manual test
- `GET /api/sync/status` - Get sync status

### WebSocket

- `ws://your-ip:6066/ws` - Real-time data updates

## 🔄 Data Synchronization

The system automatically synchronizes data between two OpenWRT devices:

- **Sync Interval**: 5 minutes
- **Conflict Resolution**: Last-write-wins with timestamp
- **Retry Strategy**: Exponential backoff
- **Health Check**: Automatic peer discovery

## 📈 Performance

- **Memory Usage**: < 50MB
- **CPU Usage**: < 5% during testing
- **Database Size**: ~1MB per day
- **Network Overhead**: < 1Mbps for sync

## 🛠️ Troubleshooting

### Common Issues

1. **Database Lock Error**
   ```bash
   # Check database permissions
   ls -la /opt/iperf3-controller/data.db
   ```

2. **Client Connection Failed**
   ```bash
   # Test client connectivity
   telnet server-ip 55201
   ```

3. **Sync Not Working**
   ```bash
   # Check peer connectivity
   curl http://peer-ip:55201/api/sync/status
   ```

### Logs

```bash
# View logs
tail -f /var/log/iperf3-controller.log

# Debug mode
./iperf3-controller-arm64 --log-level debug
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- GitHub Issues: [Report bugs](https://github.com/your-repo/iperf3-controller/issues)
- Documentation: [Wiki](https://github.com/your-repo/iperf3-controller/wiki)
- Discussions: [GitHub Discussions](https://github.com/your-repo/iperf3-controller/discussions)
