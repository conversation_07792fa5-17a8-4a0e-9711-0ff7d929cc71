# iperf3-controller Windows 构建脚本

param(
    [string]$Target = "windows"
)

Write-Host "iperf3-controller Windows Build Script" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

# 项目信息
$ProjectName = "iperf3-controller"
$Version = "1.0.0"
$BuildTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"

try {
    $GitCommit = git rev-parse --short HEAD 2>$null
    if ($LASTEXITCODE -ne 0) { $GitCommit = "unknown" }
} catch {
    $GitCommit = "unknown"
}

Write-Host "Version: $Version" -ForegroundColor Cyan
Write-Host "Build Time: $BuildTime" -ForegroundColor Cyan
Write-Host "Git Commit: $GitCommit" -ForegroundColor Cyan
Write-Host ""

# 构建目录
$BuildDir = "build"
if (Test-Path $BuildDir) {
    Write-Host "Cleaning old build files..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $BuildDir
}
New-Item -ItemType Directory -Force -Path $BuildDir | Out-Null

# 检查Go环境
Write-Host "Checking Go environment..." -ForegroundColor Cyan
try {
    $GoVersion = go version
    Write-Host "Go Version: $GoVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Go not found in PATH" -ForegroundColor Red
    exit 1
}

# 检查依赖
Write-Host "Checking dependencies..." -ForegroundColor Cyan
go mod tidy
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to check dependencies" -ForegroundColor Red
    exit 1
}
Write-Host "Dependencies OK" -ForegroundColor Green

# 构建标志
$LdFlags = "-X main.Version=$Version -X main.BuildTime=$BuildTime -X main.GitCommit=$GitCommit -w -s"

Write-Host ""
Write-Host "Starting build..." -ForegroundColor Yellow

# 构建Windows版本
if ($Target -eq "windows" -or $Target -eq "all") {
    Write-Host "Building Windows amd64..." -ForegroundColor Cyan
    $env:GOOS = "windows"
    $env:GOARCH = "amd64"
    
    go build -ldflags $LdFlags -o "$BuildDir\iperf3-controller-windows-amd64.exe" .\cmd\iperf3-controller
    
    if ($LASTEXITCODE -eq 0) {
        $Size = [math]::Round((Get-Item "$BuildDir\iperf3-controller-windows-amd64.exe").Length / 1MB, 2)
        Write-Host "SUCCESS: Windows build completed ($Size MB)" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Windows build failed" -ForegroundColor Red
    }
}

# 构建Linux版本
if ($Target -eq "linux" -or $Target -eq "all") {
    Write-Host "Building Linux amd64..." -ForegroundColor Cyan
    $env:GOOS = "linux"
    $env:GOARCH = "amd64"
    
    go build -ldflags $LdFlags -o "$BuildDir\iperf3-controller-linux-amd64" .\cmd\iperf3-controller
    
    if ($LASTEXITCODE -eq 0) {
        $Size = [math]::Round((Get-Item "$BuildDir\iperf3-controller-linux-amd64").Length / 1MB, 2)
        Write-Host "SUCCESS: Linux build completed ($Size MB)" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Linux build failed" -ForegroundColor Red
    }
}

# 构建OpenWRT版本
if ($Target -eq "openwrt" -or $Target -eq "all") {
    Write-Host "Building OpenWRT MIPS..." -ForegroundColor Cyan
    $env:GOOS = "linux"
    $env:GOARCH = "mips"
    $env:CGO_ENABLED = "0"
    
    go build -tags=netgo -ldflags $LdFlags -o "$BuildDir\iperf3-controller-openwrt-mips" .\cmd\iperf3-controller
    
    if ($LASTEXITCODE -eq 0) {
        $Size = [math]::Round((Get-Item "$BuildDir\iperf3-controller-openwrt-mips").Length / 1MB, 2)
        Write-Host "SUCCESS: OpenWRT MIPS build completed ($Size MB)" -ForegroundColor Green
    } else {
        Write-Host "ERROR: OpenWRT MIPS build failed" -ForegroundColor Red
    }
    
    Write-Host "Building OpenWRT MIPSLE..." -ForegroundColor Cyan
    $env:GOARCH = "mipsle"
    
    go build -tags=netgo -ldflags $LdFlags -o "$BuildDir\iperf3-controller-openwrt-mipsle" .\cmd\iperf3-controller
    
    if ($LASTEXITCODE -eq 0) {
        $Size = [math]::Round((Get-Item "$BuildDir\iperf3-controller-openwrt-mipsle").Length / 1MB, 2)
        Write-Host "SUCCESS: OpenWRT MIPSLE build completed ($Size MB)" -ForegroundColor Green
    } else {
        Write-Host "ERROR: OpenWRT MIPSLE build failed" -ForegroundColor Red
    }
}

# 清理环境变量
Remove-Item Env:GOOS -ErrorAction SilentlyContinue
Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue

# 复制静态文件
Write-Host ""
Write-Host "Copying static files..." -ForegroundColor Cyan

if (Test-Path "web\static") {
    Copy-Item -Recurse -Force "web\static" "$BuildDir\"
    Write-Host "Static files copied" -ForegroundColor Green
}

if (Test-Path "config.example.yaml") {
    Copy-Item "config.example.yaml" "$BuildDir\"
    Write-Host "Config example copied" -ForegroundColor Green
}

if (Test-Path "README.md") {
    Copy-Item "README.md" "$BuildDir\"
    Write-Host "README copied" -ForegroundColor Green
}

# 显示结果
Write-Host ""
Write-Host "Build completed!" -ForegroundColor Green
Write-Host "Build directory: $BuildDir" -ForegroundColor Blue
Write-Host ""
Write-Host "Built files:" -ForegroundColor Yellow

Get-ChildItem $BuildDir -File -Filter "iperf3-controller-*" | ForEach-Object {
    $Size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  $($_.Name) - $Size MB" -ForegroundColor White
}

Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  Windows: .\build\iperf3-controller-windows-amd64.exe -config config.yaml" -ForegroundColor White
Write-Host "  Linux:   ./build/iperf3-controller-linux-amd64 -config config.yaml" -ForegroundColor White
Write-Host "  OpenWRT: ./build/iperf3-controller-openwrt-mips -config config.yaml" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Copy config.example.yaml to config.yaml and modify settings" -ForegroundColor White
Write-Host "2. Deploy appropriate binary to target systems" -ForegroundColor White
Write-Host "3. Configure clients list in config.yaml" -ForegroundColor White
Write-Host "4. Start the service and access web UI at http://ip:8080" -ForegroundColor White

Write-Host ""
Write-Host "Build script completed successfully!" -ForegroundColor Green
