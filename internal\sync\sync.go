package sync

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"iperf3-controller/internal/database"
)

// SyncManager 数据同步管理器接口
type SyncManager interface {
	// Start 启动同步服务
	Start(ctx context.Context) error
	
	// Stop 停止同步服务
	Stop() error
	
	// SyncNow 立即执行一次同步
	SyncNow(ctx context.Context) error
	
	// GetStatus 获取同步状态
	GetStatus() *SyncStatus
	
	// GetPeerStatus 获取对端状态
	GetPeerStatus() *PeerStatus
	
	// RegisterChangeListener 注册数据变更监听器
	RegisterChangeListener(listener ChangeListener)
}

// ChangeListener 数据变更监听器
type ChangeListener interface {
	// OnDataChanged 当数据发生变更时调用
	OnDataChanged(changeType ChangeType, tableName string, recordID string)
}

// ChangeType 变更类型
type ChangeType string

const (
	ChangeTypeInsert ChangeType = "insert"
	ChangeTypeUpdate ChangeType = "update"
	ChangeTypeDelete ChangeType = "delete"
)

// SyncStatus 同步状态
type SyncStatus struct {
	IsRunning        bool      `json:"is_running"`
	LastSyncTime     time.Time `json:"last_sync_time"`
	NextSyncTime     time.Time `json:"next_sync_time"`
	SyncInterval     time.Duration `json:"sync_interval"`
	TotalSyncs       int64     `json:"total_syncs"`
	SuccessfulSyncs  int64     `json:"successful_syncs"`
	FailedSyncs      int64     `json:"failed_syncs"`
	LastError        string    `json:"last_error,omitempty"`
	PeerConnected    bool      `json:"peer_connected"`
	DataInSync       bool      `json:"data_in_sync"`
}

// PeerStatus 对端状态
type PeerStatus struct {
	Host             string    `json:"host"`
	Port             int       `json:"port"`
	Connected        bool      `json:"connected"`
	LastHeartbeat    time.Time `json:"last_heartbeat"`
	Version          string    `json:"version"`
	DatabaseVersion  int64     `json:"database_version"`
	RecordCount      int64     `json:"record_count"`
	LastUpdateTime   time.Time `json:"last_update_time"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	// 对端OpenWRT地址
	PeerHost string `json:"peer_host"`
	PeerPort int    `json:"peer_port"`
	
	// 同步间隔
	SyncInterval time.Duration `json:"sync_interval"`
	
	// 心跳间隔
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	
	// 连接超时
	ConnectTimeout time.Duration `json:"connect_timeout"`
	
	// 同步超时
	SyncTimeout time.Duration `json:"sync_timeout"`
	
	// 重试次数
	RetryAttempts int `json:"retry_attempts"`
	
	// 重试延迟
	RetryDelay time.Duration `json:"retry_delay"`
	
	// 是否启用压缩
	EnableCompression bool `json:"enable_compression"`
	
	// 是否启用加密
	EnableEncryption bool `json:"enable_encryption"`
}

// DefaultSyncManager 默认同步管理器实现
type DefaultSyncManager struct {
	config     *SyncConfig
	repository *database.Repository
	logger     *logrus.Logger
	
	// 状态管理
	mu           sync.RWMutex
	isRunning    bool
	stopChan     chan struct{}
	syncTicker   *time.Ticker
	heartbeatTicker *time.Ticker
	
	// 统计信息
	totalSyncs      int64
	successfulSyncs int64
	failedSyncs     int64
	lastSyncTime    time.Time
	lastError       error
	
	// 对端状态
	peerStatus *PeerStatus
	
	// 变更监听器
	changeListeners []ChangeListener
}

// NewSyncManager 创建新的同步管理器
func NewSyncManager(
	config *SyncConfig,
	repository *database.Repository,
	logger *logrus.Logger,
) SyncManager {
	if logger == nil {
		logger = logrus.New()
	}
	
	return &DefaultSyncManager{
		config:     config,
		repository: repository,
		logger:     logger,
		stopChan:   make(chan struct{}),
		peerStatus: &PeerStatus{
			Host: config.PeerHost,
			Port: config.PeerPort,
		},
		changeListeners: make([]ChangeListener, 0),
	}
}

// Start 启动同步服务
func (sm *DefaultSyncManager) Start(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if sm.isRunning {
		return fmt.Errorf("同步服务已经在运行")
	}
	
	sm.logger.WithFields(logrus.Fields{
		"peer_host":          sm.config.PeerHost,
		"peer_port":          sm.config.PeerPort,
		"sync_interval":      sm.config.SyncInterval,
		"heartbeat_interval": sm.config.HeartbeatInterval,
	}).Info("启动数据同步服务")
	
	sm.isRunning = true
	
	// 启动同步定时器
	sm.syncTicker = time.NewTicker(sm.config.SyncInterval)
	
	// 启动心跳定时器
	sm.heartbeatTicker = time.NewTicker(sm.config.HeartbeatInterval)
	
	// 启动同步协程
	go sm.syncRoutine(ctx)
	
	// 启动心跳协程
	go sm.heartbeatRoutine(ctx)
	
	sm.logger.Info("数据同步服务启动成功")
	return nil
}

// Stop 停止同步服务
func (sm *DefaultSyncManager) Stop() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if !sm.isRunning {
		return fmt.Errorf("同步服务未运行")
	}
	
	sm.logger.Info("停止数据同步服务")
	
	sm.isRunning = false
	close(sm.stopChan)
	
	if sm.syncTicker != nil {
		sm.syncTicker.Stop()
	}
	
	if sm.heartbeatTicker != nil {
		sm.heartbeatTicker.Stop()
	}
	
	sm.logger.Info("数据同步服务已停止")
	return nil
}

// GetStatus 获取同步状态
func (sm *DefaultSyncManager) GetStatus() *SyncStatus {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	status := &SyncStatus{
		IsRunning:       sm.isRunning,
		LastSyncTime:    sm.lastSyncTime,
		SyncInterval:    sm.config.SyncInterval,
		TotalSyncs:      sm.totalSyncs,
		SuccessfulSyncs: sm.successfulSyncs,
		FailedSyncs:     sm.failedSyncs,
		PeerConnected:   sm.peerStatus.Connected,
	}
	
	if sm.isRunning {
		status.NextSyncTime = sm.lastSyncTime.Add(sm.config.SyncInterval)
	}
	
	if sm.lastError != nil {
		status.LastError = sm.lastError.Error()
	}
	
	// 简单的数据同步状态判断
	status.DataInSync = sm.peerStatus.Connected && sm.failedSyncs == 0
	
	return status
}

// GetPeerStatus 获取对端状态
func (sm *DefaultSyncManager) GetPeerStatus() *PeerStatus {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	// 返回副本避免竞态条件
	peerStatus := *sm.peerStatus
	return &peerStatus
}

// RegisterChangeListener 注册数据变更监听器
func (sm *DefaultSyncManager) RegisterChangeListener(listener ChangeListener) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	sm.changeListeners = append(sm.changeListeners, listener)
	sm.logger.WithField("listener_count", len(sm.changeListeners)).Debug("注册数据变更监听器")
}

// SyncNow 立即执行一次同步
func (sm *DefaultSyncManager) SyncNow(ctx context.Context) error {
	sm.logger.Info("手动触发数据同步")
	return sm.performSync(ctx)
}
