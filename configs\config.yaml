# iPerf3 双OpenWRT测速系统配置文件
# 详细配置说明请参考 config.example.yaml

# 服务器配置
server:
  # 服务监听端口 (默认: 55201)
  listen_port: 55201
  # Web界面端口 (默认: 6066)
  web_port: 6066

# 调度配置
schedule:
  # 调度模式: "odd"(奇数小时), "even"(偶数小时), "always"(每小时)
  mode: "odd"
  # 时区设置 (默认: Asia/Shanghai)
  timezone: "Asia/Shanghai"

# 对端OpenWRT配置
peer:
  # 对端IP地址 (必须配置)
  ip: ""
  # 对端端口 (默认: 55201)
  port: 55201
  # 同步间隔 (默认: 5m)
  sync_interval: "5m"

# 数据库配置
database:
  # 数据库文件路径
  path: "/opt/iperf3-controller/data.db"
  # 备份间隔
  backup_interval: "24h"
  # 最大数据库大小 (MB)
  max_size_mb: 1024
  # 数据保留天数
  retention_days: 30
  # 连接池配置
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "15m"
  busy_timeout: "30s"
  # SQLite优化配置
  journal_mode: "WAL"
  synchronous: "NORMAL"
  cache_size: -64000

# 测试服务器列表 (必须配置)
servers:
  - name: "server-01"
    host: "*************"
    port: 55201
    enabled: true
  - name: "server-02"
    host: "*************"
    port: 55201
    enabled: true
  # 添加更多服务器...

# 性能配置
performance:
  # TCP测试配置
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
    duration: "30s"
  # UDP测试配置
  udp:
    bandwidth: "500M"
    packet_size: 1472
    duration: "10s"
  # 并发控制
  concurrency:
    max_workers: 4
    batch_size: 3
    batch_delay: "2s"

# 客户端管理配置
client_management:
  prepare_timeout: "10s"
  test_timeout: "60s"
  stop_timeout: "5s"
  retry_attempts: 3
  retry_delay: "1s"

# 同步配置
sync:
  enabled: true
  interval: "5m"
  timeout: "30s"

# 日志配置
logging:
  level: "info"
  format: "text"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true
