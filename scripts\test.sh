#!/bin/bash

# iperf3-controller 测试脚本
# 执行完整的功能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
PROJECT_NAME="iperf3-controller"
TEST_HOST="localhost"
TEST_PORT="8080"
API_BASE="http://${TEST_HOST}:${TEST_PORT}/api/v1"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试结果记录
declare -a FAILED_TEST_NAMES=()

# 辅助函数
log_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_TESTS++))
    FAILED_TEST_NAMES+=("$1")
}

log_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# 执行测试
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    echo -e "${YELLOW}🧪 测试: $test_name${NC}"
    
    if eval "$test_command"; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# HTTP请求辅助函数
http_get() {
    local url="$1"
    local expected_status="${2:-200}"
    
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" 2>/dev/null)
    local body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    if [ "$status" -eq "$expected_status" ]; then
        echo "$body"
        return 0
    else
        echo "HTTP请求失败: 状态码 $status, 期望 $expected_status" >&2
        return 1
    fi
}

http_post() {
    local url="$1"
    local data="$2"
    local expected_status="${3:-200}"
    
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$url" 2>/dev/null)
    local body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    if [ "$status" -eq "$expected_status" ]; then
        echo "$body"
        return 0
    else
        echo "HTTP请求失败: 状态码 $status, 期望 $expected_status" >&2
        return 1
    fi
}

# 检查服务器是否运行
check_server_running() {
    log_info "检查服务器是否运行..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$TEST_HOST:$TEST_PORT/health" > /dev/null 2>&1; then
            log_success "服务器正在运行"
            return 0
        fi
        
        echo -n "."
        sleep 1
        ((attempt++))
    done
    
    log_error "服务器未运行或无法连接"
    return 1
}

# 基础API测试
test_health_check() {
    local response=$(http_get "$TEST_HOST:$TEST_PORT/health")
    echo "$response" | grep -q "healthy" && return 0 || return 1
}

test_system_status() {
    local response=$(http_get "$API_BASE/status")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_clients_list() {
    local response=$(http_get "$API_BASE/clients")
    echo "$response" | grep -q "clients" && return 0 || return 1
}

test_schedule_status() {
    local response=$(http_get "$API_BASE/schedule/status")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_sync_status() {
    local response=$(http_get "$API_BASE/sync/status")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_stats_overview() {
    local response=$(http_get "$API_BASE/stats/overview")
    echo "$response" | grep -q "summary" && return 0 || return 1
}

test_stats_performance() {
    local response=$(http_get "$API_BASE/stats/performance")
    echo "$response" | grep -q "performance" && return 0 || return 1
}

test_stats_clients() {
    local response=$(http_get "$API_BASE/stats/clients")
    echo "$response" | grep -q "clients" && return 0 || return 1
}

# 控制API测试
test_trigger_test() {
    local response=$(http_post "$API_BASE/tests/trigger" "{}")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_start_scheduler() {
    local response=$(http_post "$API_BASE/schedule/start" "{}")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_stop_scheduler() {
    local response=$(http_post "$API_BASE/schedule/stop" "{}")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_start_sync() {
    local response=$(http_post "$API_BASE/sync/start" "{}")
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_stop_sync() {
    local response=$(http_post "$API_BASE/sync/stop" "{}")
    echo "$response" | grep -q "success" && return 0 || return 1
}

# 客户端管理测试
test_add_client() {
    local client_data='{
        "id": "test-client-999",
        "name": "测试客户端",
        "host": "192.168.1.999",
        "port": 55999,
        "enabled": true
    }'
    local response=$(http_post "$API_BASE/clients" "$client_data" 201)
    echo "$response" | grep -q "success" && return 0 || return 1
}

test_get_client() {
    local response=$(http_get "$API_BASE/clients/test-client-999")
    echo "$response" | grep -q "test-client-999" && return 0 || return 1
}

test_delete_client() {
    local response=$(curl -s -X DELETE "$API_BASE/clients/test-client-999")
    echo "$response" | grep -q "success" && return 0 || return 1
}

# 错误处理测试
test_404_endpoint() {
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$API_BASE/nonexistent" 2>/dev/null)
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    [ "$status" -eq "404" ] && return 0 || return 1
}

test_invalid_client_id() {
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$API_BASE/clients/nonexistent" 2>/dev/null)
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    [ "$status" -eq "404" ] && return 0 || return 1
}

# 性能测试
test_concurrent_requests() {
    log_info "执行并发请求测试..."
    
    local pids=()
    local success_count=0
    
    # 启动10个并发请求
    for i in {1..10}; do
        (
            if http_get "$API_BASE/status" > /dev/null 2>&1; then
                echo "success"
            else
                echo "failed"
            fi
        ) &
        pids+=($!)
    done
    
    # 等待所有请求完成
    for pid in "${pids[@]}"; do
        wait $pid
        if [ $? -eq 0 ]; then
            ((success_count++))
        fi
    done
    
    # 检查成功率
    if [ $success_count -ge 8 ]; then
        return 0
    else
        echo "并发测试失败: 只有 $success_count/10 个请求成功" >&2
        return 1
    fi
}

# 主测试函数
run_all_tests() {
    echo -e "${BLUE}🚀 iperf3-controller 功能测试${NC}"
    echo -e "${BLUE}测试目标: $API_BASE${NC}"
    echo ""
    
    # 检查服务器状态
    if ! check_server_running; then
        echo -e "${RED}❌ 服务器未运行，无法执行测试${NC}"
        echo -e "${YELLOW}请先启动服务器: go run test_api.go${NC}"
        exit 1
    fi
    
    echo ""
    echo -e "${YELLOW}📋 开始API测试...${NC}"
    
    # 基础API测试
    run_test "健康检查" "test_health_check"
    run_test "系统状态" "test_system_status"
    run_test "客户端列表" "test_clients_list"
    run_test "调度状态" "test_schedule_status"
    run_test "同步状态" "test_sync_status"
    run_test "统计概览" "test_stats_overview"
    run_test "性能统计" "test_stats_performance"
    run_test "客户端统计" "test_stats_clients"
    
    echo ""
    echo -e "${YELLOW}🎮 控制API测试...${NC}"
    
    # 控制API测试
    run_test "触发测试" "test_trigger_test"
    run_test "启动调度器" "test_start_scheduler"
    run_test "停止调度器" "test_stop_scheduler"
    run_test "启动同步" "test_start_sync"
    run_test "停止同步" "test_stop_sync"
    
    echo ""
    echo -e "${YELLOW}👥 客户端管理测试...${NC}"
    
    # 客户端管理测试
    run_test "添加客户端" "test_add_client"
    run_test "获取客户端" "test_get_client"
    run_test "删除客户端" "test_delete_client"
    
    echo ""
    echo -e "${YELLOW}🚫 错误处理测试...${NC}"
    
    # 错误处理测试
    run_test "404端点" "test_404_endpoint"
    run_test "无效客户端ID" "test_invalid_client_id"
    
    echo ""
    echo -e "${YELLOW}⚡ 性能测试...${NC}"
    
    # 性能测试
    run_test "并发请求" "test_concurrent_requests"
}

# 显示测试结果
show_test_results() {
    echo ""
    echo -e "${BLUE}📊 测试结果统计${NC}"
    echo "=================================="
    echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -gt 0 ]; then
        echo ""
        echo -e "${RED}❌ 失败的测试:${NC}"
        for test_name in "${FAILED_TEST_NAMES[@]}"; do
            echo -e "  - ${RED}$test_name${NC}"
        done
    fi
    
    echo ""
    local success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
    echo -e "成功率: ${GREEN}$success_rate%${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo ""
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        exit 0
    else
        echo ""
        echo -e "${RED}💥 有测试失败，请检查日志${NC}"
        exit 1
    fi
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl 未安装，请先安装 curl${NC}"
        exit 1
    fi
    
    # 运行测试
    run_all_tests
    
    # 显示结果
    show_test_results
}

# 运行主函数
main "$@"
