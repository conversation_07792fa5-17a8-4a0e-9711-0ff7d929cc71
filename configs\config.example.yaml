# iPerf3 双OpenWRT测速系统 - 完整配置示例
# 本文件包含所有可配置项的详细说明和示例值

# ============================================================================
# 服务器配置
# ============================================================================
server:
  # 服务监听端口
  # 用于接收客户端连接和对端OpenWRT同步请求
  # 范围: 1-65535, 建议: 55201
  listen_port: 55201

  # Web界面端口
  # 用于提供Web管理界面和API服务
  # 范围: 1-65535, 建议: 6066
  web_port: 6066

# ============================================================================
# 调度配置
# ============================================================================
schedule:
  # 调度模式
  # - "odd": 奇数小时执行测试 (1,3,5,7,9,11,13,15,17,19,21,23)
  # - "even": 偶数小时执行测试 (0,2,4,6,8,10,12,14,16,18,20,22)
  # - "always": 每小时都执行测试
  mode: "odd"

  # 时区设置
  # 使用IANA时区数据库格式
  # 常用值: Asia/Shanghai, UTC, America/New_York
  timezone: "Asia/Shanghai"

# ============================================================================
# 对端OpenWRT配置
# ============================================================================
peer:
  # 对端OpenWRT设备IP地址
  # 必须配置，用于数据同步
  # 示例: "************"
  ip: "************"

  # 对端服务端口
  # 必须与对端设备的listen_port一致
  port: 55201

  # 数据同步间隔
  # 格式: 数字+单位 (s=秒, m=分钟, h=小时)
  # 建议: 5m (5分钟)
  sync_interval: "5m"

# ============================================================================
# 数据库配置
# ============================================================================
database:
  # SQLite数据库文件路径
  # 建议使用绝对路径，确保目录存在且有写权限
  path: "/opt/iperf3-controller/data.db"

  # 数据库备份间隔
  # 自动备份数据库文件的时间间隔
  backup_interval: "24h"

  # 数据库最大大小限制 (MB)
  # 超过此大小时会触发数据清理
  max_size_mb: 1024

  # 数据保留天数
  # 超过此天数的历史数据会被自动删除
  retention_days: 30

  # 数据库连接池配置
  max_open_conns: 10 # 最大打开连接数
  max_idle_conns: 5 # 最大空闲连接数
  conn_max_lifetime: "1h" # 连接最大生存时间
  conn_max_idle_time: "15m" # 连接最大空闲时间
  busy_timeout: "30s" # 数据库忙碌超时

  # SQLite性能优化配置
  journal_mode: "WAL" # 日志模式: DELETE, TRUNCATE, PERSIST, MEMORY, WAL, OFF
  synchronous: "NORMAL" # 同步模式: OFF, NORMAL, FULL, EXTRA
  cache_size: -64000 # 缓存大小 (负数表示KB)

# ============================================================================
# 测试服务器列表
# ============================================================================
servers:
  # 服务器配置示例 - 需要根据实际环境修改
  - name: "server-01" # 服务器名称 (唯一标识)
    host: "*************" # 服务器IP地址或主机名
    port: 55201 # 服务器监听端口
    enabled: true # 是否启用此服务器

  - name: "server-02"
    host: "*************"
    port: 55201
    enabled: true

  - name: "server-03"
    host: "*************"
    port: 55201
    enabled: true

  # 继续添加更多服务器...
  # 建议配置12台服务器以实现完整的测试覆盖

# ============================================================================
# 性能配置
# ============================================================================
performance:
  # TCP测试配置
  tcp:
    # 并行流数量
    # 建议: 4 (根据网络带宽调整)
    parallel_streams: 4

    # TCP窗口大小
    # 格式: 数字+单位 (K=KB, M=MB)
    # 建议: 512K
    window_size: "512K"

    # 最大段大小 (MSS)
    # 建议: 1460 (以太网MTU 1500 - TCP/IP头部 40)
    mss: 1460

    # 测试持续时间
    # 建议: 30s (平衡准确性和资源消耗)
    duration: "30s"

  # UDP测试配置
  udp:
    # 目标带宽
    # 格式: 数字+单位 (K=Kbps, M=Mbps, G=Gbps)
    # 建议: 500M (根据实际网络容量设置)
    bandwidth: "500M"

    # UDP包大小
    # 建议: 1472 (以太网MTU 1500 - UDP/IP头部 28)
    packet_size: 1472

    # 测试持续时间
    # UDP测试通常比TCP测试短
    duration: "10s"

  # 并发控制配置
  concurrency:
    # 最大工作线程数
    # 控制同时进行测试的服务器数量
    max_workers: 4

    # 批次大小
    # 每批次测试的服务器数量
    batch_size: 3

    # 批次间延迟
    # 避免网络拥塞和资源竞争
    batch_delay: "2s"

# ============================================================================
# 客户端管理配置
# ============================================================================
client_management:
  # 准备阶段超时
  # 客户端启动iperf3服务器的最大等待时间
  prepare_timeout: "10s"

  # 测试阶段超时
  # 单次测试的最大执行时间
  test_timeout: "60s"

  # 停止阶段超时
  # 客户端停止iperf3服务器的最大等待时间
  stop_timeout: "5s"

  # 重试次数
  # 操作失败时的最大重试次数
  retry_attempts: 3

  # 重试延迟
  # 重试之间的等待时间
  retry_delay: "1s"

# ============================================================================
# 数据同步配置
# ============================================================================
sync:
  # 是否启用数据同步
  # 双OpenWRT架构必须启用
  enabled: true

  # 同步检查间隔
  # 检查是否需要同步的时间间隔
  interval: "5m"

  # 同步操作超时
  # 单次同步操作的最大执行时间
  timeout: "30s"

# ============================================================================
# 日志配置
# ============================================================================
logging:
  # 日志级别
  # 可选: trace, debug, info, warn, error, fatal, panic
  # 生产环境建议: info, 调试时使用: debug
  level: "info"

  # 日志格式
  # - "text": 人类可读格式
  # - "json": 结构化JSON格式 (便于日志分析)
  format: "text"

  # 日志输出
  # - "stdout": 标准输出
  # - "stderr": 标准错误
  # - "file": 输出到文件 (需配置file_path)
  output: "stdout"

  # 日志文件路径 (当output为file时使用)
  file_path: "/var/log/iperf3-controller.log"

  # 日志文件轮转配置
  max_size: 100 # 单个日志文件最大大小 (MB)
  max_backups: 3 # 保留的备份文件数量
  max_age: 28 # 日志文件保留天数
  compress: true # 是否压缩备份文件
