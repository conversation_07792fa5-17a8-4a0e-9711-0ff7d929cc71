# iperf3-controller Windows PowerShell 构建脚本
# 支持多平台交叉编译

param(
    [string]$Platform = "all",
    [switch]$Clean = $false,
    [switch]$Test = $false,
    [switch]$Verbose = $false
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[OK] $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Cyan"
}

# 项目信息
$ProjectName = "iperf3-controller"
$Version = "1.0.0"
$BuildTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
$GitCommit = try { git rev-parse --short HEAD 2>$null } catch { "unknown" }

# 构建目录
$BuildDir = "build"
$DistDir = "dist"

Write-ColorOutput "iperf3-controller Windows Build Script" "Blue"
Write-ColorOutput "Version: $Version" "Blue"
Write-ColorOutput "Build Time: $BuildTime" "Blue"
Write-ColorOutput "Git Commit: $GitCommit" "Blue"
Write-Host ""

# 清理旧文件
if ($Clean -or (Test-Path $BuildDir)) {
    Write-Warning "Cleaning old build files..."
    if (Test-Path $BuildDir) { Remove-Item -Recurse -Force $BuildDir }
    if (Test-Path $DistDir) { Remove-Item -Recurse -Force $DistDir }
}

# 创建构建目录
New-Item -ItemType Directory -Force -Path $BuildDir | Out-Null
New-Item -ItemType Directory -Force -Path $DistDir | Out-Null

# 构建标志
$LdFlags = "-X main.Version=$Version -X main.BuildTime=$BuildTime -X main.GitCommit=$GitCommit -w -s"

# 支持的平台
$Platforms = @(
    @{OS = "linux"; Arch = "amd64"; Name = "Linux 64位" },
    @{OS = "linux"; Arch = "arm64"; Name = "Linux ARM64" },
    @{OS = "linux"; Arch = "arm"; Name = "Linux ARM" },
    @{OS = "windows"; Arch = "amd64"; Name = "Windows 64位" },
    @{OS = "darwin"; Arch = "amd64"; Name = "macOS Intel" },
    @{OS = "darwin"; Arch = "arm64"; Name = "macOS Apple Silicon" }
)

# OpenWRT特定平台
$OpenWRTPlatforms = @(
    @{OS = "linux"; Arch = "mips"; Name = "OpenWRT MIPS" },
    @{OS = "linux"; Arch = "mipsle"; Name = "OpenWRT MIPSLE" },
    @{OS = "linux"; Arch = "mips64"; Name = "OpenWRT MIPS64" },
    @{OS = "linux"; Arch = "mips64le"; Name = "OpenWRT MIPS64LE" }
)

# 检查Go环境
Write-Info "🔍 检查Go环境..."
try {
    $GoVersion = go version
    Write-Success "Go环境: $GoVersion"
}
catch {
    Write-Error "Go未安装或不在PATH中"
    exit 1
}

# 检查依赖
Write-Info "📦 检查依赖..."
try {
    go mod tidy
    Write-Success "依赖检查完成"
}
catch {
    Write-Error "依赖检查失败: $_"
    exit 1
}

# 运行测试
if ($Test) {
    Write-Info "🧪 运行测试..."
    try {
        go test ./...
        Write-Success "所有测试通过"
    }
    catch {
        Write-Error "测试失败: $_"
        exit 1
    }
}

# 构建函数
function Build-Platform {
    param(
        [string]$OS,
        [string]$Arch,
        [string]$Name,
        [bool]$IsOpenWRT = $false
    )
    
    $OutputName = if ($IsOpenWRT) {
        "$ProjectName-openwrt-$Arch"
    }
    else {
        "$ProjectName-$OS-$Arch"
    }
    
    if ($OS -eq "windows") {
        $OutputName += ".exe"
    }
    
    Write-Info "🔨 构建 $Name..."
    
    $env:GOOS = $OS
    $env:GOARCH = $Arch
    
    if ($IsOpenWRT) {
        $env:CGO_ENABLED = "0"
        $BuildTags = "-tags=netgo"
    }
    else {
        $BuildTags = ""
    }
    
    try {
        if ($Verbose) {
            Write-Host "   命令: go build $BuildTags -ldflags `"$LdFlags`" -o $BuildDir\$OutputName .\cmd\iperf3-controller"
        }
        
        if ($BuildTags) {
            go build $BuildTags -ldflags $LdFlags -o "$BuildDir\$OutputName" .\cmd\iperf3-controller
        }
        else {
            go build -ldflags $LdFlags -o "$BuildDir\$OutputName" .\cmd\iperf3-controller
        }
        
        if ($LASTEXITCODE -eq 0) {
            $FileSize = (Get-Item "$BuildDir\$OutputName").Length
            $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
            Write-Success "$Name 构建成功 ($FileSizeMB MB)"
        }
        else {
            Write-Error "$Name 构建失败"
            return $false
        }
    }
    catch {
        Write-Error "$Name 构建失败: $_"
        return $false
    }
    
    return $true
}

Write-Info "📦 开始构建..."

$SuccessCount = 0
$TotalCount = 0

# 构建主程序
if ($Platform -eq "all" -or $Platform -eq "main") {
    foreach ($p in $Platforms) {
        $TotalCount++
        if (Build-Platform -OS $p.OS -Arch $p.Arch -Name $p.Name) {
            $SuccessCount++
        }
    }
}

# 构建OpenWRT版本
if ($Platform -eq "all" -or $Platform -eq "openwrt") {
    Write-Warning "🔧 构建OpenWRT版本..."
    foreach ($p in $OpenWRTPlatforms) {
        $TotalCount++
        if (Build-Platform -OS $p.OS -Arch $p.Arch -Name $p.Name -IsOpenWRT $true) {
            $SuccessCount++
        }
    }
}

# 构建特定平台
if ($Platform -notin @("all", "main", "openwrt")) {
    $Found = $false
    foreach ($p in $Platforms + $OpenWRTPlatforms) {
        if ("$($p.OS)-$($p.Arch)" -eq $Platform) {
            $TotalCount++
            if (Build-Platform -OS $p.OS -Arch $p.Arch -Name $p.Name) {
                $SuccessCount++
            }
            $Found = $true
            break
        }
    }
    if (-not $Found) {
        Write-Error "未知平台: $Platform"
        Write-Info "支持的平台:"
        foreach ($p in $Platforms + $OpenWRTPlatforms) {
            Write-Host "  $($p.OS)-$($p.Arch) ($($p.Name))"
        }
        exit 1
    }
}

# 复制静态文件
Write-Info "📁 复制静态文件..."
if (Test-Path "web\static") {
    Copy-Item -Recurse -Force "web\static" "$BuildDir\"
    Write-Success "静态文件复制完成"
}

if (Test-Path "config.example.yaml") {
    Copy-Item "config.example.yaml" "$BuildDir\"
    Write-Success "配置文件复制完成"
}

if (Test-Path "README.md") {
    Copy-Item "README.md" "$BuildDir\"
}

# 创建发布包
Write-Info "📦 创建发布包..."

foreach ($p in $Platforms) {
    $OutputName = "$ProjectName-$($p.OS)-$($p.Arch)"
    if ($p.OS -eq "windows") {
        $OutputName += ".exe"
    }
    
    if (Test-Path "$BuildDir\$OutputName") {
        $PackageName = "$ProjectName-$Version-$($p.OS)-$($p.Arch)"
        $TempDir = "$BuildDir\temp_$($p.OS)_$($p.Arch)"
        
        # 创建临时目录
        New-Item -ItemType Directory -Force -Path $TempDir | Out-Null
        
        # 复制文件
        Copy-Item "$BuildDir\$OutputName" $TempDir
        if (Test-Path "$BuildDir\static") {
            Copy-Item -Recurse "$BuildDir\static" $TempDir
        }
        if (Test-Path "$BuildDir\config.example.yaml") {
            Copy-Item "$BuildDir\config.example.yaml" $TempDir
        }
        if (Test-Path "$BuildDir\README.md") {
            Copy-Item "$BuildDir\README.md" $TempDir
        }
        
        # 创建压缩包
        if ($p.OS -eq "windows") {
            Compress-Archive -Path "$TempDir\*" -DestinationPath "$DistDir\$PackageName.zip" -Force
        }
        else {
            # 使用tar创建.tar.gz (需要WSL或Git Bash)
            try {
                tar -czf "$DistDir\$PackageName.tar.gz" -C $TempDir .
            }
            catch {
                # 如果tar不可用，使用zip
                Compress-Archive -Path "$TempDir\*" -DestinationPath "$DistDir\$PackageName.zip" -Force
            }
        }
        
        # 清理临时目录
        Remove-Item -Recurse -Force $TempDir
        
        Write-Success "$PackageName 打包完成"
    }
}

# 创建OpenWRT发布包
Write-Info "📦 创建OpenWRT发布包..."
$OpenWRTDir = "$BuildDir\openwrt"
New-Item -ItemType Directory -Force -Path $OpenWRTDir | Out-Null

foreach ($p in $OpenWRTPlatforms) {
    $OutputName = "$ProjectName-openwrt-$($p.Arch)"
    if (Test-Path "$BuildDir\$OutputName") {
        Copy-Item "$BuildDir\$OutputName" $OpenWRTDir
    }
}

if (Test-Path "$BuildDir\static") {
    Copy-Item -Recurse "$BuildDir\static" $OpenWRTDir
}

Compress-Archive -Path "$OpenWRTDir\*" -DestinationPath "$DistDir\$ProjectName-$Version-openwrt.zip" -Force
Write-Success "OpenWRT包创建完成"

# 生成校验和
Write-Info "🔐 生成校验和..."
$HashFile = "$DistDir\checksums.sha256"
Get-ChildItem $DistDir -File | ForEach-Object {
    $Hash = Get-FileHash $_.FullName -Algorithm SHA256
    "$($Hash.Hash.ToLower())  $($_.Name)" | Out-File -Append -Encoding UTF8 $HashFile
}
Write-Success "校验和生成完成"

# 显示构建结果
Write-Host ""
Write-ColorOutput "🎉 构建完成！" "Green"
Write-ColorOutput "构建文件位置: $BuildDir" "Blue"
Write-ColorOutput "发布包位置: $DistDir" "Blue"
Write-Host ""
Write-ColorOutput "📊 构建统计:" "Yellow"
Write-Host "成功构建: $SuccessCount/$TotalCount"

Write-Host ""
Write-Host "二进制文件:"
Get-ChildItem $BuildDir -File -Filter "$ProjectName-*" | ForEach-Object {
    $Size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  $($_.Name) ($Size MB)"
}

Write-Host ""
Write-Host "发布包:"
Get-ChildItem $DistDir -File | ForEach-Object {
    $Size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  $($_.Name) ($Size MB)"
}

Write-Host ""
if ($SuccessCount -eq $TotalCount) {
    Write-Success "✅ 所有平台构建成功！"
    Write-ColorOutput "支持平台: Linux (amd64/arm64/arm), Windows (amd64), macOS (amd64/arm64), OpenWRT (mips/mipsle/mips64/mips64le)" "Blue"
}
else {
    Write-Warning "⚠️ 部分平台构建失败，请检查错误信息"
}

# 重置环境变量
Remove-Item Env:GOOS -ErrorAction SilentlyContinue
Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue
