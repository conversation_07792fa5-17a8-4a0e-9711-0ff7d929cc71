# iPerf3 双OpenWRT测速系统 - 开发TODO

## 🎯 项目概述
- **架构**: 双OpenWRT服务端 + 12台服务器客户端
- **测试方向**: 服务器到OpenWRT单向下行测速
- **调度**: 奇数/偶数小时分别测试
- **抢占模式**: 所有服务器同时抢占，谁先响应谁先测速 🚀
- **数据同步**: 双OpenWRT SQLite完全同步
- **可视化**: Web界面显示实时速度

---

## 🚀 抢占式测试特性 (已实现)

### 核心抢占机制
- **同时启动**: 每小时测试时，所有12台服务器同时开始抢占
- **先响应先测**: 谁先响应prepare请求，谁就优先获得测试机会
- **全覆盖测试**: 直到所有服务器都完成测速才结束本轮
- **优雅处理**: 失败的服务器不影响其他服务器继续测试

### 技术实现亮点
- **并发协程**: 每个客户端独立goroutine执行抢占逻辑
- **通道通信**: 使用Go channel优雅协调抢占结果
- **状态管理**: 线程安全的测试状态跟踪和统计
- **错误隔离**: 单个客户端失败不影响整体测试流程
- **实时监控**: 完整的抢占过程监控和统计信息

### 抢占优势
- **公平竞争**: 所有服务器机会均等，网络条件好的优先
- **效率最大**: 无需等待慢速服务器，整体测试时间最短
- **容错能力**: 部分服务器故障不影响其他服务器测试
- **负载均衡**: 自然实现基于响应速度的负载分配

---

## 📋 第一阶段：核心功能开发 (Week 1-2)

### 1.1 项目初始化 ✅ COMPLETED
- [x] 创建Go项目结构
- [x] 添加依赖包（cobra, viper, logrus, sqlite, gin）
- [ ] 设置交叉编译环境（ARM64）
- [x] 创建基础配置文件模板

### 1.2 数据库模块 ✅ COMPLETED
- [x] 实现SQLite数据库连接和初始化
- [x] 创建数据库表结构（servers, hourly_results, sync_status, system_config）
- [x] 实现数据库CRUD操作
- [x] 添加数据库迁移功能
- [x] 编写数据库单元测试

### 1.3 配置管理 ✅ COMPLETED
- [x] 实现配置文件解析（YAML）
- [x] 支持奇数/偶数小时调度配置
- [x] 服务器列表配置管理
- [x] 对端OpenWRT配置
- [x] 配置验证和默认值处理

### 1.4 客户端管理模块 ✅ COMPLETED
- [x] 设计客户端通信协议（JSON over HTTP/TCP）
- [x] 实现客户端连接管理
- [x] 客户端状态跟踪（准备/测试中/空闲）
- [x] 客户端超时处理
- [x] 客户端错误重试机制

---

## 📋 第二阶段：测试协调和调度 (Week 3-4)

### 2.1 测试协调器 ✅ COMPLETED (抢占式升级)
- [x] 实现抢占式测试流程控制
  - [x] 所有服务器同时开始抢占
  - [x] 谁先响应谁先测速
  - [x] 直到所有服务器都测速结束
  - [x] 通知客户端准备（启动iperf3）
  - [x] 执行TCP测试
  - [x] 执行UDP测试
  - [x] 通知客户端停止（关闭iperf3）
- [x] 测试结果解析和验证
- [x] 抢占式并发测试控制（优雅实现）
- [x] 测试超时和错误处理
- [x] 完整的抢占式统计和监控

### 2.2 调度模块 ✅ COMPLETED (抢占式升级)
- [x] 实现小时级定时调度
- [x] 奇数/偶数小时判断逻辑
- [x] 时区处理和时间同步
- [x] 调度状态持久化
- [x] 手动触发测试功能
- [x] 集成抢占式测试协调器
- [x] 支持抢占式测试调度

### 2.3 iperf3集成 ✅ COMPLETED
- [x] iperf3命令行封装
- [x] TCP测试参数优化
- [x] UDP测试参数优化
- [x] JSON结果输出解析
- [x] 错误码处理
- [x] 结果验证和质量评分
- [x] 人类可读的结果格式化

---

## 📋 第三阶段：数据同步功能 ✅ COMPLETED (Week 5-6)

### 3.1 同步机制设计 ✅ COMPLETED
- [x] 设计同步协议（RESTful API）
- [x] 实现数据变更检测
- [x] 增量同步vs全量同步策略
- [x] 同步冲突解决机制
- [x] 同步状态跟踪
- [x] 数据版本管理
- [x] 心跳检测机制

### 3.2 同步服务实现 ✅ COMPLETED
- [x] HTTP API服务器架构
- [x] 数据推送接口
- [x] 数据拉取接口
- [x] 同步状态查询接口
- [x] 健康检查接口
- [x] 冲突解决接口

### 3.3 同步客户端 ✅ COMPLETED
- [x] 定时同步任务
- [x] 网络异常处理
- [x] 重试和退避策略
- [x] 同步日志记录
- [x] 同步性能监控
- [x] 变更监听机制
- [x] 优雅启停控制

---

## 📋 第四阶段：Web前端开发 ✅ COMPLETED (Week 7-8)

### 4.1 后端API ✅ COMPLETED
- [x] RESTful API设计
- [x] 服务器状态查询接口
- [x] 历史数据查询接口
- [x] 系统配置接口
- [x] 客户端管理接口
- [x] 测试控制接口
- [x] 调度管理接口
- [x] 同步管理接口
- [x] 统计数据接口

### 4.2 前端页面 ✅ COMPLETED
- [x] HTML页面结构设计
- [x] 现代化CSS样式实现
- [x] 原生JavaScript开发
  - [x] 服务器状态卡片组件
  - [x] 实时数据更新组件
  - [x] 性能指标展示组件
  - [x] 客户端状态组件
  - [x] 控制面板组件
- [x] 响应式设计（移动端适配）

### 4.3 实时功能 ✅ COMPLETED
- [x] 自动刷新机制
- [x] 实时数据推送
- [x] 错误处理和重试
- [x] 用户友好的状态提示
- [x] 交互式控制面板

---

## 📋 第五阶段：部署和测试 (Week 9-10)

### 5.1 构建和打包
- [ ] 交叉编译脚本
- [ ] 静态资源打包
- [ ] 部署包制作
- [ ] 版本管理

### 5.2 部署配置
- [ ] OpenWRT部署脚本
- [ ] 系统服务配置
- [ ] 日志轮转配置
- [ ] 监控配置

### 5.3 测试验证
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] 性能测试
- [ ] 压力测试
- [ ] 故障恢复测试

---

## 🔧 开发环境准备

### 开发工具
- [ ] Go 1.21+ 开发环境
- [ ] SQLite3 命令行工具
- [ ] Node.js（前端构建）
- [ ] OpenWRT交叉编译工具链

### 测试环境
- [ ] 2台OpenWRT设备（或虚拟机）
- [ ] 12台测试服务器（或容器）
- [ ] 网络环境配置
- [ ] iperf3安装和配置

---

## 📁 项目文件结构

```
iperf3-controller/
├── cmd/
│   └── iperf3-controller/
│       └── main.go
├── internal/
│   ├── app/
│   ├── config/
│   ├── database/
│   ├── client/
│   ├── coordinator/
│   ├── scheduler/
│   ├── sync/
│   └── web/
├── pkg/
├── web/
│   ├── static/
│   ├── templates/
│   └── assets/
├── configs/
├── scripts/
├── docs/
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

---

## 🚀 快速开始

1. **克隆项目并初始化**
   ```bash
   mkdir iperf3-controller && cd iperf3-controller
   go mod init iperf3-controller
   ```

2. **安装依赖**
   ```bash
   go get github.com/spf13/cobra
   go get github.com/spf13/viper
   go get github.com/sirupsen/logrus
   go get modernc.org/sqlite
   go get github.com/gin-gonic/gin
   ```

3. **创建基础文件结构**
4. **开始第一阶段开发**

---

## ⚠️ 注意事项

- 确保时间同步（NTP）
- 网络防火墙配置
- SQLite文件权限
- 日志文件大小控制
- 内存使用监控
- 错误处理完整性

---

**开发优先级**: 数据库 → 客户端管理 → 测试协调 → 调度 → 同步 → Web界面
