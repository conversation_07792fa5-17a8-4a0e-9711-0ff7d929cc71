# iperf3-controller 网络部署脚本
# 部署两个OpenWRT服务端和两个客户端

param(
    [string]$Action = "deploy",
    [switch]$BuildFirst = $false
)

Write-Host "iperf3-controller 网络部署脚本" -ForegroundColor Blue
Write-Host "=================================" -ForegroundColor Blue

# 服务器信息
$Servers = @{
    "server1" = @{
        IP = "***********"
        Port = 6060
        Config = "config-server1.yaml"
        Binary = "iperf3-controller-openwrt-mips"
        Description = "OpenWRT服务端1"
    }
    "server2" = @{
        IP = "************"
        Port = 6066
        Config = "config-server2.yaml"
        Binary = "iperf3-controller-openwrt-mips"
        Description = "OpenWRT服务端2"
    }
}

$Clients = @{
    "client1" = @{
        IP = "*************"
        Port = 55201
        Config = "config-client1.yaml"
        Binary = "iperf3-controller-linux-amd64"
        Description = "外网客户端1"
    }
    "client2" = @{
        IP = "**************"
        Port = 55202
        Config = "config-client2.yaml"
        Binary = "iperf3-controller-linux-amd64"
        Description = "外网客户端2"
    }
}

# 构建程序
if ($BuildFirst) {
    Write-Host "构建程序..." -ForegroundColor Yellow
    .\build-win.ps1 -Target all
    if ($LASTEXITCODE -ne 0) {
        Write-Host "构建失败" -ForegroundColor Red
        exit 1
    }
}

# 部署函数
function Deploy-Server {
    param($ServerKey, $ServerInfo)
    
    Write-Host "部署 $($ServerInfo.Description) ($($ServerInfo.IP):$($ServerInfo.Port))..." -ForegroundColor Cyan
    
    # 这里应该是实际的部署命令
    # 由于无法直接访问远程服务器，这里提供部署指令
    
    Write-Host "请在 $($ServerInfo.IP) 上执行以下命令:" -ForegroundColor Yellow
    Write-Host "1. 创建目录:" -ForegroundColor White
    Write-Host "   mkdir -p /opt/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /etc/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/log/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/lib/iperf3-controller" -ForegroundColor Gray
    
    Write-Host "2. 上传文件:" -ForegroundColor White
    Write-Host "   scp build/$($ServerInfo.Binary) root@$($ServerInfo.IP):/opt/iperf3-controller/" -ForegroundColor Gray
    Write-Host "   scp $($ServerInfo.Config) root@$($ServerInfo.IP):/etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    Write-Host "   scp -r build/static root@$($ServerInfo.IP):/opt/iperf3-controller/" -ForegroundColor Gray
    
    Write-Host "3. 设置权限:" -ForegroundColor White
    Write-Host "   chmod +x /opt/iperf3-controller/$($ServerInfo.Binary)" -ForegroundColor Gray
    
    Write-Host "4. 启动服务:" -ForegroundColor White
    Write-Host "   /opt/iperf3-controller/$($ServerInfo.Binary) server -c /etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    
    Write-Host "5. 访问Web界面:" -ForegroundColor White
    Write-Host "   http://$($ServerInfo.IP):$($ServerInfo.Port)" -ForegroundColor Green
    Write-Host ""
}

function Deploy-Client {
    param($ClientKey, $ClientInfo)
    
    Write-Host "部署 $($ClientInfo.Description) ($($ClientInfo.IP):$($ClientInfo.Port))..." -ForegroundColor Cyan
    
    Write-Host "请在 $($ClientInfo.IP) 上执行以下命令:" -ForegroundColor Yellow
    Write-Host "1. 创建目录:" -ForegroundColor White
    Write-Host "   mkdir -p /opt/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /etc/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/log/iperf3-controller" -ForegroundColor Gray
    
    Write-Host "2. 上传文件:" -ForegroundColor White
    Write-Host "   scp build/$($ClientInfo.Binary) root@$($ClientInfo.IP):/opt/iperf3-controller/" -ForegroundColor Gray
    Write-Host "   scp $($ClientInfo.Config) root@$($ClientInfo.IP):/etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    
    Write-Host "3. 设置权限:" -ForegroundColor White
    Write-Host "   chmod +x /opt/iperf3-controller/$($ClientInfo.Binary)" -ForegroundColor Gray
    
    Write-Host "4. 启动服务:" -ForegroundColor White
    Write-Host "   /opt/iperf3-controller/$($ClientInfo.Binary) client -c /etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    Write-Host ""
}

# 执行部署
if ($Action -eq "deploy") {
    Write-Host "开始部署网络架构..." -ForegroundColor Green
    Write-Host ""
    
    # 部署服务端
    Write-Host "=== 部署OpenWRT服务端 ===" -ForegroundColor Magenta
    foreach ($key in $Servers.Keys) {
        Deploy-Server $key $Servers[$key]
    }
    
    # 部署客户端
    Write-Host "=== 部署客户端 ===" -ForegroundColor Magenta
    foreach ($key in $Clients.Keys) {
        Deploy-Client $key $Clients[$key]
    }
    
    # 网络架构说明
    Write-Host "=== 网络架构说明 ===" -ForegroundColor Magenta
    Write-Host "服务端1 (***********:6060)  - 奇数小时测试" -ForegroundColor Green
    Write-Host "服务端2 (************:6066) - 偶数小时测试" -ForegroundColor Green
    Write-Host "客户端1 (*************)     - 连接两个服务端" -ForegroundColor Cyan
    Write-Host "客户端2 (**************)    - 连接两个服务端" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "双服务端同步: 服务端1 ↔ 服务端2" -ForegroundColor Yellow
    Write-Host "抢占式测试: 客户端同时向两个服务端发起测试" -ForegroundColor Yellow
    Write-Host ""
    
} elseif ($Action -eq "status") {
    Write-Host "检查服务状态..." -ForegroundColor Green
    
    foreach ($key in $Servers.Keys) {
        $server = $Servers[$key]
        Write-Host "检查 $($server.Description)..." -ForegroundColor Cyan
        try {
            $response = Invoke-WebRequest -Uri "http://$($server.IP):$($server.Port)/health" -TimeoutSec 5
            Write-Host "  ✅ $($server.Description) 运行正常" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ $($server.Description) 无法访问" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "部署脚本完成!" -ForegroundColor Green
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "  .\deploy-network.ps1 -Action deploy    # 显示部署指令" -ForegroundColor White
Write-Host "  .\deploy-network.ps1 -Action status    # 检查服务状态" -ForegroundColor White
Write-Host "  .\deploy-network.ps1 -BuildFirst       # 先构建再部署" -ForegroundColor White
