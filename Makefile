# iPerf3 Controller Makefile
# Supports cross-compilation for OpenWRT ARM64

# Build information
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT ?= $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_DATE ?= $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Build flags
LDFLAGS = -ldflags "-X main.version=$(VERSION) -X main.commit=$(COMMIT) -X main.date=$(BUILD_DATE) -w -s"
BUILD_FLAGS = -trimpath

# Target binary
BINARY_NAME = iperf3-controller
BINARY_ARM64 = $(BINARY_NAME)-arm64

# Directories
BUILD_DIR = build
DIST_DIR = dist

# Go build environment
export CGO_ENABLED = 0
export GO111MODULE = on

.PHONY: all build build-arm64 clean test lint fmt vet deps tidy run dev help

# Default target
all: build

# Build for current platform
build:
	@echo "Building $(BINARY_NAME) for current platform..."
	@mkdir -p $(BUILD_DIR)
	go build $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/iperf3-controller

# Build for ARM64 (OpenWRT target)
build-arm64:
	@echo "Cross-compiling $(BINARY_ARM64) for linux/arm64..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=arm64 go build $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_ARM64) ./cmd/iperf3-controller

# Create distribution package
dist: build-arm64
	@echo "Creating distribution package..."
	@mkdir -p $(DIST_DIR)
	@cp $(BUILD_DIR)/$(BINARY_ARM64) $(DIST_DIR)/
	@cp -r configs $(DIST_DIR)/
	@cp -r web $(DIST_DIR)/
	@cp README.md $(DIST_DIR)/
	@echo "Distribution package created in $(DIST_DIR)/"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR) $(DIST_DIR)
	@go clean

# Run tests
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

# Run tests with coverage report
test-coverage: test
	@echo "Generating coverage report..."
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Lint code
lint:
	@echo "Running linter..."
	@which golangci-lint > /dev/null || (echo "golangci-lint not found, installing..." && go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest)
	golangci-lint run

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download

# Tidy dependencies
tidy:
	@echo "Tidying dependencies..."
	go mod tidy

# Run application in development mode
run: build
	@echo "Running $(BINARY_NAME) in development mode..."
	./$(BUILD_DIR)/$(BINARY_NAME) --config configs/config.yaml --log-level debug

# Run application in server mode
dev: build
	@echo "Running $(BINARY_NAME) in server mode..."
	./$(BUILD_DIR)/$(BINARY_NAME) --server --config configs/config.yaml --log-level debug

# Install binary to system
install: build-arm64
	@echo "Installing $(BINARY_ARM64) to /usr/local/bin/..."
	@sudo cp $(BUILD_DIR)/$(BINARY_ARM64) /usr/local/bin/$(BINARY_NAME)
	@sudo chmod +x /usr/local/bin/$(BINARY_NAME)

# Show help
help:
	@echo "Available targets:"
	@echo "  build        - Build for current platform"
	@echo "  build-arm64  - Cross-compile for linux/arm64 (OpenWRT)"
	@echo "  dist         - Create distribution package"
	@echo "  clean        - Clean build artifacts"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage report"
	@echo "  lint         - Run linter"
	@echo "  fmt          - Format code"
	@echo "  vet          - Vet code"
	@echo "  deps         - Install dependencies"
	@echo "  tidy         - Tidy dependencies"
	@echo "  run          - Run in development mode"
	@echo "  dev          - Run in server mode"
	@echo "  install      - Install binary to system"
	@echo "  help         - Show this help"
