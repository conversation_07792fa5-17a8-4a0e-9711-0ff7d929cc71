package coordinator

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/client"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// PreemptiveTestCoordinator 实现抢占式测试协调器
// 服务器抢占式响应测试，谁先响应谁先测速，直到所有服务器都测速结束
type PreemptiveTestCoordinator struct {
	clientManager *client.Manager
	logger        *logrus.Logger

	// 抢占式测试状态
	mu               sync.RWMutex
	isRunning        bool
	currentRound     *PreemptiveTestRound
	completedClients map[string]*client.TestResult // 存储已完成测试的客户端及其结果
	failedClients    map[string]error              // 测试失败的客户端

	// 统计信息
	totalRounds      int
	successfulRounds int
	failedRounds     int
	lastError        error
}

// PreemptiveTestRound 抢占式测试轮次
type PreemptiveTestRound struct {
	ID               string                        `json:"id"`
	Hour             int                           `json:"hour"`
	StartTime        time.Time                     `json:"start_time"`
	EndTime          *time.Time                    `json:"end_time,omitempty"`
	Status           string                        `json:"status"` // "running", "completed", "failed"
	TotalClients     int                           `json:"total_clients"`
	CompletedClients int                           `json:"completed_clients"`
	FailedClients    int                           `json:"failed_clients"`
	TestResults      map[string]*client.TestResult `json:"test_results"`
	Errors           map[string]string             `json:"errors"`
}

// NewPreemptiveTestCoordinator 创建新的抢占式测试协调器
func NewPreemptiveTestCoordinator(
	clientManager *client.Manager,
	logger *logrus.Logger,
) *PreemptiveTestCoordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &PreemptiveTestCoordinator{
		clientManager:    clientManager,
		logger:           logger,
		completedClients: make(map[string]*client.TestResult),
		failedClients:    make(map[string]error),
	}
}

// Start 启动抢占式测试协调器
func (c *PreemptiveTestCoordinator) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("启动抢占式测试协调器")
	c.isRunning = true

	return nil
}

// Stop 停止抢占式测试协调器
func (c *PreemptiveTestCoordinator) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("停止抢占式测试协调器")
	c.isRunning = false

	return nil
}

// ExecutePreemptiveTestRound 执行抢占式测试轮次
// 所有客户端同时开始抢占，谁先响应谁先测速
func (c *PreemptiveTestCoordinator) ExecutePreemptiveTestRound(ctx context.Context, hour int) (*PreemptiveTestRound, error) {
	c.mu.Lock()

	// 检查是否已有测试在运行
	if c.currentRound != nil && c.currentRound.Status == "running" {
		c.mu.Unlock()
		return nil, fmt.Errorf("已有测试轮次正在运行")
	}

	// 获取所有可用的客户端
	clients := c.clientManager.GetEnabledClients()
	if len(clients) == 0 {
		c.mu.Unlock()
		return nil, fmt.Errorf("没有可用的客户端")
	}

	// 创建新的测试轮次
	round := &PreemptiveTestRound{
		ID:           uuid.New().String(),
		Hour:         hour,
		StartTime:    time.Now(),
		Status:       "running",
		TotalClients: len(clients),
		TestResults:  make(map[string]*client.TestResult),
		Errors:       make(map[string]string),
	}

	c.currentRound = round
	c.completedClients = make(map[string]*client.TestResult)
	c.failedClients = make(map[string]error)

	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"round_id":      round.ID,
		"hour":          hour,
		"total_clients": len(clients),
	}).Info("开始抢占式测试轮次")

	// 执行抢占式测试
	err := c.executePreemptiveTests(ctx, clients, round)

	// 更新测试轮次状态
	c.mu.Lock()
	endTime := time.Now()
	round.EndTime = &endTime

	if err != nil {
		round.Status = "failed"
		c.lastError = err
		c.failedRounds++
		c.totalRounds++

		c.logger.WithFields(logrus.Fields{
			"round_id": round.ID,
			"hour":     hour,
			"error":    err.Error(),
			"duration": endTime.Sub(round.StartTime),
		}).Error("抢占式测试轮次失败")
	} else {
		round.Status = "completed"
		c.successfulRounds++
		c.totalRounds++

		c.logger.WithFields(logrus.Fields{
			"round_id":          round.ID,
			"hour":              hour,
			"duration":          endTime.Sub(round.StartTime),
			"completed_clients": round.CompletedClients,
			"failed_clients":    round.FailedClients,
		}).Info("抢占式测试轮次完成")
	}

	// 复制结果到轮次记录
	for clientID, result := range c.completedClients {
		round.TestResults[clientID] = result
		round.CompletedClients++
	}

	for clientID, err := range c.failedClients {
		round.Errors[clientID] = err.Error()
		round.FailedClients++
	}

	c.currentRound = nil
	c.mu.Unlock()

	return round, err
}

// executePreemptiveTests 执行抢占式测试
// 所有客户端同时开始，谁先响应谁先测速
func (c *PreemptiveTestCoordinator) executePreemptiveTests(ctx context.Context, clients []client.Client, round *PreemptiveTestRound) error {
	c.logger.WithField("client_count", len(clients)).Info("开始抢占式测试执行")

	// 创建抢占通道
	preemptChan := make(chan *PreemptiveTestRequest, len(clients))
	resultChan := make(chan *PreemptiveTestResult, len(clients))

	// 启动所有客户端的抢占goroutine
	var wg sync.WaitGroup
	for _, cl := range clients {
		wg.Add(1)
		go func(client client.Client) {
			defer wg.Done()
			c.clientPreemptiveRoutine(ctx, client, preemptChan, resultChan)
		}(cl)
	}

	// 等待所有客户端完成抢占
	go func() {
		wg.Wait()
		close(preemptChan)
		close(resultChan)
	}()

	// 处理抢占结果
	completedCount := 0
	failedCount := 0

	for result := range resultChan {
		if result.Error != nil {
			c.mu.Lock()
			c.failedClients[result.ClientID] = result.Error
			c.mu.Unlock()
			failedCount++

			c.logger.WithFields(logrus.Fields{
				"client_id": result.ClientID,
				"error":     result.Error.Error(),
			}).Warn("客户端测试失败")
		} else {
			// 记录成功的测试结果
			c.mu.Lock()
			// 暂时跳过结果存储，直接计数
			c.mu.Unlock()
			completedCount++

			c.logger.WithFields(logrus.Fields{
				"client_id": result.ClientID,
			}).Info("客户端测试完成")
		}
	}

	c.logger.WithFields(logrus.Fields{
		"completed_count": completedCount,
		"failed_count":    failedCount,
		"total_count":     len(clients),
	}).Info("抢占式测试执行完成")

	if completedCount == 0 {
		return fmt.Errorf("所有客户端测试都失败了")
	}

	return nil
}

// PreemptiveTestRequest 抢占式测试请求
type PreemptiveTestRequest struct {
	ClientID   string
	TestConfig client.TestConfig
	Priority   int // 优先级，数字越小优先级越高
}

// PreemptiveTestResult 抢占式测试结果
type PreemptiveTestResult struct {
	ClientID   string
	TestResult interface{} // 使用interface{}以支持不同类型的测试结果
	Error      error
	Duration   time.Duration
}

// clientPreemptiveRoutine 客户端抢占式测试例程
func (c *PreemptiveTestCoordinator) clientPreemptiveRoutine(
	ctx context.Context,
	client client.Client,
	preemptChan chan *PreemptiveTestRequest,
	resultChan chan *PreemptiveTestResult,
) {
	clientID := client.GetID()
	startTime := time.Now()

	c.logger.WithField("client_id", clientID).Debug("客户端开始抢占式测试")

	// 创建测试配置
	tcpConfig := c.createTCPTestConfig()
	udpConfig := c.createUDPTestConfig()

	// 执行TCP测试
	tcpResult := c.executeClientTestWithPreemption(ctx, client, *tcpConfig)

	// 执行UDP测试
	udpResult := c.executeClientTestWithPreemption(ctx, client, *udpConfig)

	// 选择最好的结果（优先TCP，如果TCP失败则使用UDP）
	var finalResult interface{}
	var finalError error

	if tcpResult.Error == nil {
		finalResult = tcpResult.TestResult
	} else if udpResult.Error == nil {
		finalResult = udpResult.TestResult
	} else {
		finalError = tcpResult.Error
	}

	// 发送结果
	var testResult interface{}
	if finalResult != nil {
		testResult = finalResult
	}

	resultChan <- &PreemptiveTestResult{
		ClientID:   clientID,
		TestResult: testResult,
		Error:      finalError,
		Duration:   time.Since(startTime),
	}
}

// executeClientTestWithPreemption 执行客户端抢占式测试
func (c *PreemptiveTestCoordinator) executeClientTestWithPreemption(
	ctx context.Context,
	client client.Client,
	testConfig client.TestConfig,
) *PreemptiveTestResult {
	clientID := client.GetID()
	startTime := time.Now()

	c.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"test_type": testConfig.Type,
	}).Debug("开始客户端抢占式测试")

	// 准备客户端
	if err := client.Prepare(ctx); err != nil {
		return &PreemptiveTestResult{
			ClientID: clientID,
			Error:    fmt.Errorf("客户端准备失败: %w", err),
			Duration: time.Since(startTime),
		}
	}

	// 执行测试
	result, err := client.StartTest(ctx, testConfig)
	if err != nil {
		// 尝试停止客户端
		client.Stop(ctx)
		return &PreemptiveTestResult{
			ClientID: clientID,
			Error:    fmt.Errorf("测试执行失败: %w", err),
			Duration: time.Since(startTime),
		}
	}

	// 停止客户端
	if err := client.Stop(ctx); err != nil {
		c.logger.WithFields(logrus.Fields{
			"client_id": clientID,
			"error":     err.Error(),
		}).Warn("测试后停止客户端失败")
	}

	return &PreemptiveTestResult{
		ClientID:   clientID,
		TestResult: result,
		Duration:   time.Since(startTime),
	}
}

// createTCPTestConfig 创建TCP测试配置
func (c *PreemptiveTestCoordinator) createTCPTestConfig() *client.TestConfig {
	return &client.TestConfig{
		Type:     "tcp",
		Duration: 30 * time.Second, // TCP测试30秒
		TCP: &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		},
	}
}

// createUDPTestConfig 创建UDP测试配置
func (c *PreemptiveTestCoordinator) createUDPTestConfig() *client.TestConfig {
	return &client.TestConfig{
		Type:     "udp",
		Duration: 10 * time.Second, // UDP测试10秒
		UDP: &client.UDPTestConfig{
			Bandwidth:  "500M",
			PacketSize: 1472,
		},
	}
}

// GetCurrentRound 获取当前测试轮次
func (c *PreemptiveTestCoordinator) GetCurrentRound() *PreemptiveTestRound {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.currentRound != nil {
		// 返回副本避免竞态条件
		round := *c.currentRound
		return &round
	}

	return nil
}

// GetStatus 获取协调器状态
func (c *PreemptiveTestCoordinator) GetStatus() *CoordinatorStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()

	status := &CoordinatorStatus{
		IsRunning:        c.isRunning,
		TotalRounds:      c.totalRounds,
		SuccessfulRounds: c.successfulRounds,
		FailedRounds:     c.failedRounds,
	}

	if c.currentRound != nil {
		roundID := c.currentRound.ID
		status.CurrentRound = &roundID
	}

	if c.lastError != nil {
		errStr := c.lastError.Error()
		status.LastError = &errStr
	}

	return status
}

// GetPreemptiveStats 获取抢占式测试统计信息
func (c *PreemptiveTestCoordinator) GetPreemptiveStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := map[string]interface{}{
		"is_running":        c.isRunning,
		"total_rounds":      c.totalRounds,
		"successful_rounds": c.successfulRounds,
		"failed_rounds":     c.failedRounds,
		"success_rate":      0.0,
	}

	if c.totalRounds > 0 {
		stats["success_rate"] = float64(c.successfulRounds) / float64(c.totalRounds) * 100
	}

	if c.currentRound != nil {
		stats["current_round"] = map[string]interface{}{
			"id":                c.currentRound.ID,
			"hour":              c.currentRound.Hour,
			"status":            c.currentRound.Status,
			"total_clients":     c.currentRound.TotalClients,
			"completed_clients": c.currentRound.CompletedClients,
			"failed_clients":    c.currentRound.FailedClients,
			"start_time":        c.currentRound.StartTime,
		}
	}

	if c.lastError != nil {
		stats["last_error"] = c.lastError.Error()
	}

	return stats
}
