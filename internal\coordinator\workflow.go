package coordinator

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"iperf3-controller/internal/client"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ExecuteTestRound executes a complete test round for the current hour
func (c *DefaultTestCoordinator) ExecuteTestRound(ctx context.Context, hour int) (*TestRoundResult, error) {
	c.mu.Lock()
	roundID := fmt.Sprintf("round-%d-%d", hour, time.Now().Unix())
	c.status.CurrentRound = &roundID
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"hour":     hour,
		"round_id": roundID,
	}).Info("Starting test round")

	startTime := time.Now()

	// Get all idle clients
	clients := c.clientManager.GetIdleClients()
	if len(clients) == 0 {
		err := fmt.Errorf("no idle clients available for testing")
		c.setLastError(err)
		return nil, err
	}

	c.logger.WithField("client_count", len(clients)).Info("Found idle clients for testing")

	// Create test configurations
	tcpConfig := c.createTCPTestConfig()
	udpConfig := c.createUDPTestConfig()

	// Execute tests in batches
	batchResults, err := c.executeTestsInBatches(ctx, clients, tcpConfig, udpConfig)
	if err != nil {
		c.setLastError(err)
		c.incrementFailedRounds()
		return nil, fmt.Errorf("failed to execute tests in batches: %w", err)
	}

	endTime := time.Now()

	// Calculate summary statistics
	summary := c.calculateRoundSummary(batchResults)

	// Create test round result
	result := &TestRoundResult{
		Hour:            hour,
		StartTime:       startTime,
		EndTime:         endTime,
		Duration:        endTime.Sub(startTime),
		TotalTests:      summary.TCPSummary.TestCount + summary.UDPSummary.TestCount,
		SuccessfulTests: summary.TCPSummary.SuccessfulTests + summary.UDPSummary.SuccessfulTests,
		FailedTests:     summary.TCPSummary.FailedTests + summary.UDPSummary.FailedTests,
		BatchResults:    batchResults,
		Summary:         summary,
	}

	c.incrementSuccessfulRounds()

	c.mu.Lock()
	c.status.CurrentRound = nil
	lastRoundTime := endTime
	c.status.LastRoundTime = &lastRoundTime
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"hour":             hour,
		"round_id":         roundID,
		"duration":         result.Duration,
		"total_tests":      result.TotalTests,
		"successful_tests": result.SuccessfulTests,
		"failed_tests":     result.FailedTests,
	}).Info("Test round completed")

	return result, nil
}

// ExecuteTestBatch executes tests for a batch of clients
func (c *DefaultTestCoordinator) ExecuteTestBatch(ctx context.Context, clients []client.Client, testConfig TestBatchConfig) (*TestBatchResult, error) {
	c.logger.WithFields(logrus.Fields{
		"batch_id":     testConfig.BatchID,
		"client_count": len(clients),
	}).Info("Starting test batch")

	startTime := time.Now()

	var tcpResults []client.TestResult
	var udpResults []client.TestResult
	var errors []TestError
	var wg sync.WaitGroup
	var mu sync.Mutex

	// Execute tests for each client
	for _, cl := range clients {
		wg.Add(1)
		go func(client client.Client) {
			defer wg.Done()

			// Acquire concurrency slot
			if err := c.concurrency.AcquireSlot(ctx); err != nil {
				mu.Lock()
				errors = append(errors, TestError{
					ClientID:  client.GetID(),
					TestType:  "concurrency",
					Error:     fmt.Sprintf("failed to acquire concurrency slot: %v", err),
					Timestamp: time.Now(),
				})
				mu.Unlock()
				return
			}
			defer c.concurrency.ReleaseSlot()

			// Execute TCP test if configured
			if testConfig.TCPConfig != nil {
				result, err := c.executeClientTest(ctx, client, *testConfig.TCPConfig)
				mu.Lock()
				if err != nil {
					errors = append(errors, TestError{
						ClientID:  client.GetID(),
						TestType:  "tcp",
						Error:     err.Error(),
						Timestamp: time.Now(),
					})
				} else {
					tcpResults = append(tcpResults, *result)
				}
				mu.Unlock()
			}

			// Execute UDP test if configured
			if testConfig.UDPConfig != nil {
				result, err := c.executeClientTest(ctx, client, *testConfig.UDPConfig)
				mu.Lock()
				if err != nil {
					errors = append(errors, TestError{
						ClientID:  client.GetID(),
						TestType:  "udp",
						Error:     err.Error(),
						Timestamp: time.Now(),
					})
				} else {
					udpResults = append(udpResults, *result)
				}
				mu.Unlock()
			}
		}(cl)
	}

	// Wait for all tests to complete
	wg.Wait()

	endTime := time.Now()

	result := &TestBatchResult{
		BatchID:     testConfig.BatchID,
		StartTime:   startTime,
		EndTime:     endTime,
		Duration:    endTime.Sub(startTime),
		ClientCount: len(clients),
		TCPResults:  tcpResults,
		UDPResults:  udpResults,
		Errors:      errors,
	}

	c.logger.WithFields(logrus.Fields{
		"batch_id":    testConfig.BatchID,
		"duration":    result.Duration,
		"tcp_results": len(tcpResults),
		"udp_results": len(udpResults),
		"errors":      len(errors),
	}).Info("Test batch completed")

	return result, nil
}

// executeTestsInBatches executes tests for all clients in batches
func (c *DefaultTestCoordinator) executeTestsInBatches(ctx context.Context, clients []client.Client, tcpConfig, udpConfig *client.TestConfig) ([]TestBatchResult, error) {
	// TODO: Get batch configuration from config
	batchSize := 3                // Default batch size
	batchDelay := 2 * time.Second // Default batch delay

	var results []TestBatchResult

	// Split clients into batches
	for i := 0; i < len(clients); i += batchSize {
		end := i + batchSize
		if end > len(clients) {
			end = len(clients)
		}

		batchClients := clients[i:end]
		batchID := fmt.Sprintf("batch-%d-%s", i/batchSize+1, uuid.New().String()[:8])

		testConfig := TestBatchConfig{
			BatchID:    batchID,
			TCPConfig:  tcpConfig,
			UDPConfig:  udpConfig,
			BatchDelay: batchDelay,
		}

		// Execute batch
		result, err := c.ExecuteTestBatch(ctx, batchClients, testConfig)
		if err != nil {
			c.logger.WithFields(logrus.Fields{
				"batch_id": batchID,
				"error":    err.Error(),
			}).Error("Batch execution failed")
			// Continue with other batches even if one fails
		} else {
			results = append(results, *result)
		}

		// Wait between batches (except for the last batch)
		if end < len(clients) {
			c.logger.WithField("delay", batchDelay).Debug("Waiting between batches")
			select {
			case <-ctx.Done():
				return results, ctx.Err()
			case <-time.After(batchDelay):
				// Continue to next batch
			}
		}
	}

	return results, nil
}

// executeClientTest executes a single test on a client
func (c *DefaultTestCoordinator) executeClientTest(ctx context.Context, client client.Client, testConfig client.TestConfig) (*client.TestResult, error) {
	c.logger.WithFields(logrus.Fields{
		"client_id": client.GetID(),
		"test_type": testConfig.Type,
	}).Debug("Executing client test")

	// Prepare client
	if err := client.Prepare(ctx); err != nil {
		return nil, fmt.Errorf("failed to prepare client: %w", err)
	}

	// Execute test
	result, err := client.StartTest(ctx, testConfig)
	if err != nil {
		// Try to stop client even if test failed
		if stopErr := client.Stop(ctx); stopErr != nil {
			c.logger.WithFields(logrus.Fields{
				"client_id": client.GetID(),
				"error":     stopErr.Error(),
			}).Warn("Failed to stop client after test failure")
		}
		return nil, fmt.Errorf("test execution failed: %w", err)
	}

	// Stop client
	if err := client.Stop(ctx); err != nil {
		c.logger.WithFields(logrus.Fields{
			"client_id": client.GetID(),
			"error":     err.Error(),
		}).Warn("Failed to stop client after successful test")
	}

	return result, nil
}

// createTCPTestConfig creates a TCP test configuration
func (c *DefaultTestCoordinator) createTCPTestConfig() *client.TestConfig {
	// TODO: Get configuration from config
	return &client.TestConfig{
		Type:     "tcp",
		Duration: 30 * time.Second,
		TCP: &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		},
	}
}

// createUDPTestConfig creates a UDP test configuration
func (c *DefaultTestCoordinator) createUDPTestConfig() *client.TestConfig {
	// TODO: Get configuration from config
	return &client.TestConfig{
		Type:     "udp",
		Duration: 10 * time.Second,
		UDP: &client.UDPTestConfig{
			Bandwidth:  "500M",
			PacketSize: 1472,
		},
	}
}

// calculateRoundSummary calculates summary statistics for a test round
func (c *DefaultTestCoordinator) calculateRoundSummary(batchResults []TestBatchResult) TestRoundSummary {
	var tcpResults []client.TestResult
	var udpResults []client.TestResult

	// Collect all results
	for _, batch := range batchResults {
		tcpResults = append(tcpResults, batch.TCPResults...)
		udpResults = append(udpResults, batch.UDPResults...)
	}

	return TestRoundSummary{
		TCPSummary: c.calculateTestTypeSummary(tcpResults),
		UDPSummary: c.calculateTestTypeSummary(udpResults),
	}
}

// calculateTestTypeSummary calculates summary statistics for a test type
func (c *DefaultTestCoordinator) calculateTestTypeSummary(results []client.TestResult) TestTypeSummary {
	if len(results) == 0 {
		return TestTypeSummary{}
	}

	var totalBytes int64
	var totalMbps float64
	var minMbps = math.MaxFloat64
	var maxMbps float64
	successfulTests := len(results)

	for _, result := range results {
		totalBytes += result.BytesTransferred
		totalMbps += result.MbitsPerSecond

		if result.MbitsPerSecond < minMbps {
			minMbps = result.MbitsPerSecond
		}
		if result.MbitsPerSecond > maxMbps {
			maxMbps = result.MbitsPerSecond
		}
	}

	avgMbps := totalMbps / float64(len(results))
	successRate := float64(successfulTests) / float64(len(results)) * 100

	if minMbps == math.MaxFloat64 {
		minMbps = 0
	}

	return TestTypeSummary{
		TestCount:       len(results),
		SuccessfulTests: successfulTests,
		FailedTests:     0, // Failed tests are not included in results
		SuccessRate:     successRate,
		AvgMbps:         avgMbps,
		MinMbps:         minMbps,
		MaxMbps:         maxMbps,
		TotalBytes:      totalBytes,
	}
}

// setLastError sets the last error (thread-safe)
func (c *DefaultTestCoordinator) setLastError(err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastError = err
}

// incrementSuccessfulRounds increments the successful rounds counter (thread-safe)
func (c *DefaultTestCoordinator) incrementSuccessfulRounds() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.totalRounds++
	c.successfulRounds++
}

// incrementFailedRounds increments the failed rounds counter (thread-safe)
func (c *DefaultTestCoordinator) incrementFailedRounds() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.totalRounds++
	c.failedRounds++
}
