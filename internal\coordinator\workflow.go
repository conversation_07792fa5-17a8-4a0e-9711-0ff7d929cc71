package coordinator

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"iperf3-controller/internal/client"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ExecuteTestRound 执行抢占式测试轮次
// 所有服务器同时开始抢占，谁先响应谁先测速，直到所有服务器都测速结束
func (c *DefaultTestCoordinator) ExecuteTestRound(ctx context.Context, hour int) (*TestRoundResult, error) {
	c.mu.Lock()
	roundID := fmt.Sprintf("round-%d-%d", hour, time.Now().Unix())
	c.status.CurrentRound = &roundID
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"hour":     hour,
		"round_id": roundID,
	}).Info("开始测试回合")

	startTime := time.Now()

	// 获取所有空闲客户端
	clients := c.clientManager.GetIdleClients()
	if len(clients) == 0 {
		err := fmt.Errorf("没有可用于测试的空闲客户端")
		c.setLastError(err)
		return nil, err
	}

	c.logger.WithField("client_count", len(clients)).Info("找到可用于测试的空闲客户端")

	// 创建测试配置
	tcpConfig := c.createTCPTestConfig()
	udpConfig := c.createUDPTestConfig()

	// 批量执行测试
	batchResults, err := c.executeTestsInBatches(ctx, clients, tcpConfig, udpConfig)
	if err != nil {
		c.setLastError(err)
		c.incrementFailedRounds()
		return nil, fmt.Errorf("批量执行测试失败: %w", err)
	}

	endTime := time.Now()

	// 计算汇总统计数据
	summary := c.calculateRoundSummary(batchResults)

	// 创建测试回合结果
	result := &TestRoundResult{
		Hour:            hour,
		StartTime:       startTime,
		EndTime:         endTime,
		Duration:        endTime.Sub(startTime),
		TotalTests:      summary.TCPSummary.TestCount + summary.UDPSummary.TestCount,
		SuccessfulTests: summary.TCPSummary.SuccessfulTests + summary.UDPSummary.SuccessfulTests,
		FailedTests:     summary.TCPSummary.FailedTests + summary.UDPSummary.FailedTests,
		BatchResults:    batchResults,
		Summary:         summary,
	}

	c.incrementSuccessfulRounds()

	c.mu.Lock()
	c.status.CurrentRound = nil
	lastRoundTime := endTime
	c.status.LastRoundTime = &lastRoundTime
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"hour":             hour,
		"round_id":         roundID,
		"duration":         result.Duration,
		"total_tests":      result.TotalTests,
		"successful_tests": result.SuccessfulTests,
		"failed_tests":     result.FailedTests,
	}).Info("测试回合完成")

	return result, nil
}

// ExecuteTestBatch 为一批客户端执行测试
func (c *DefaultTestCoordinator) ExecuteTestBatch(ctx context.Context, clients []client.Client, testConfig TestBatchConfig) (*TestBatchResult, error) {
	c.logger.WithFields(logrus.Fields{
		"batch_id":     testConfig.BatchID,
		"client_count": len(clients),
	}).Info("开始测试批次")

	startTime := time.Now()

	var tcpResults []client.TestResult
	var udpResults []client.TestResult
	var errors []TestError
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 为每个客户端执行测试
	for _, cl := range clients {
		wg.Add(1)
		go func(client client.Client) {
			defer wg.Done()

			// 获取并发槽
			if err := c.concurrency.AcquireSlot(ctx); err != nil {
				mu.Lock()
				errors = append(errors, TestError{
					ClientID:  client.GetID(),
					TestType:  "concurrency",
					Error:     fmt.Sprintf("获取并发槽失败: %v", err),
					Timestamp: time.Now(),
				})
				mu.Unlock()
				return
			}
			defer c.concurrency.ReleaseSlot()

			// 如果配置了 TCP 测试，则执行
			if testConfig.TCPConfig != nil {
				result, err := c.executeClientTest(ctx, client, *testConfig.TCPConfig)
				mu.Lock()
				if err != nil {
					errors = append(errors, TestError{
						ClientID:  client.GetID(),
						TestType:  "tcp",
						Error:     err.Error(),
						Timestamp: time.Now(),
					})
				} else {
					tcpResults = append(tcpResults, *result)
				}
				mu.Unlock()
			}

			// 如果配置了 UDP 测试，则执行
			if testConfig.UDPConfig != nil {
				result, err := c.executeClientTest(ctx, client, *testConfig.UDPConfig)
				mu.Lock()
				if err != nil {
					errors = append(errors, TestError{
						ClientID:  client.GetID(),
						TestType:  "udp",
						Error:     err.Error(),
						Timestamp: time.Now(),
					})
				} else {
					udpResults = append(udpResults, *result)
				}
				mu.Unlock()
			}
		}(cl)
	}

	// 等待所有测试完成
	wg.Wait()

	endTime := time.Now()

	result := &TestBatchResult{
		BatchID:     testConfig.BatchID,
		StartTime:   startTime,
		EndTime:     endTime,
		Duration:    endTime.Sub(startTime),
		ClientCount: len(clients),
		TCPResults:  tcpResults,
		UDPResults:  udpResults,
		Errors:      errors,
	}

	c.logger.WithFields(logrus.Fields{
		"batch_id":    testConfig.BatchID,
		"duration":    result.Duration,
		"tcp_results": len(tcpResults),
		"udp_results": len(udpResults),
		"errors":      len(errors),
	}).Info("测试批次完成")

	return result, nil
}

// executeTestsInBatches 执行抢占式测试
// 所有客户端同时开始抢占，谁先响应谁先测速，不再使用传统批次模式
func (c *DefaultTestCoordinator) executeTestsInBatches(ctx context.Context, clients []client.Client, tcpConfig, udpConfig *client.TestConfig) ([]TestBatchResult, error) {
	c.logger.WithField("client_count", len(clients)).Info("开始抢占式测试 - 所有客户端同时开始抢占")

	// 抢占式测试：所有客户端同时开始，不分批次
	var results []TestBatchResult

	// 创建单个大批次包含所有客户端
	batchID := fmt.Sprintf("preemptive-batch-%s", uuid.New().String()[:8])

	testConfig := TestBatchConfig{
		BatchID:    batchID,
		TCPConfig:  tcpConfig,
		UDPConfig:  udpConfig,
		BatchDelay: 0, // 抢占式测试无延迟
	}

	c.logger.WithField("batch_id", batchID).Info("执行抢占式测试批次 - 所有服务器同时抢占")

	// 执行抢占式测试批次
	result, err := c.ExecuteTestBatch(ctx, clients, testConfig)
	if err != nil {
		c.logger.WithFields(logrus.Fields{
			"batch_id": batchID,
			"error":    err.Error(),
		}).Error("抢占式测试批次执行失败")
		return results, err
	}

	results = append(results, *result)

	c.logger.WithFields(logrus.Fields{
		"batch_id":    batchID,
		"tcp_results": len(result.TCPResults),
		"udp_results": len(result.UDPResults),
		"errors":      len(result.Errors),
		"duration":    result.Duration,
	}).Info("抢占式测试批次完成")

	return results, nil
}

// executeClientTest 在客户端上执行单个测试
func (c *DefaultTestCoordinator) executeClientTest(ctx context.Context, client client.Client, testConfig client.TestConfig) (*client.TestResult, error) {
	c.logger.WithFields(logrus.Fields{
		"client_id": client.GetID(),
		"test_type": testConfig.Type,
	}).Debug("正在执行客户端测试")

	// 准备客户端
	if err := client.Prepare(ctx); err != nil {
		return nil, fmt.Errorf("准备客户端失败: %w", err)
	}

	// 执行测试
	result, err := client.StartTest(ctx, testConfig)
	if err != nil {
		// 即使测试失败也尝试停止客户端
		if stopErr := client.Stop(ctx); stopErr != nil {
			c.logger.WithFields(logrus.Fields{
				"client_id": client.GetID(),
				"error":     stopErr.Error(),
			}).Warn("测试失败后停止客户端失败")
		}
		return nil, fmt.Errorf("测试执行失败: %w", err)
	}

	// 停止客户端
	if err := client.Stop(ctx); err != nil {
		c.logger.WithFields(logrus.Fields{
			"client_id": client.GetID(),
			"error":     err.Error(),
		}).Warn("成功测试后停止客户端失败")
	}

	return result, nil
}

// createTCPTestConfig 创建 TCP 测试配置
func (c *DefaultTestCoordinator) createTCPTestConfig() *client.TestConfig {
	// 使用默认的TCP测试配置
	return &client.TestConfig{
		Type:     "tcp",
		Duration: 30 * time.Second,
		TCP: &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		},
	}
}

// createUDPTestConfig 创建 UDP 测试配置
func (c *DefaultTestCoordinator) createUDPTestConfig() *client.TestConfig {
	// 使用默认的UDP测试配置
	return &client.TestConfig{
		Type:     "udp",
		Duration: 10 * time.Second,
		UDP: &client.UDPTestConfig{
			Bandwidth:  "500M",
			PacketSize: 1472,
		},
	}
}

// calculateRoundSummary 计算测试回合的汇总统计数据
func (c *DefaultTestCoordinator) calculateRoundSummary(batchResults []TestBatchResult) TestRoundSummary {
	var tcpResults []client.TestResult
	var udpResults []client.TestResult

	// 收集所有结果
	for _, batch := range batchResults {
		tcpResults = append(tcpResults, batch.TCPResults...)
		udpResults = append(udpResults, batch.UDPResults...)
	}

	return TestRoundSummary{
		TCPSummary: c.calculateTestTypeSummary(tcpResults),
		UDPSummary: c.calculateTestTypeSummary(udpResults),
	}
}

// calculateTestTypeSummary 计算测试类型的汇总统计数据
func (c *DefaultTestCoordinator) calculateTestTypeSummary(results []client.TestResult) TestTypeSummary {
	if len(results) == 0 {
		return TestTypeSummary{}
	}

	var totalBytes int64
	var totalMbps float64
	var minMbps = math.MaxFloat64
	var maxMbps float64
	successfulTests := len(results)

	for _, result := range results {
		totalBytes += result.BytesTransferred
		totalMbps += result.MbitsPerSecond

		if result.MbitsPerSecond < minMbps {
			minMbps = result.MbitsPerSecond
		}
		if result.MbitsPerSecond > maxMbps {
			maxMbps = result.MbitsPerSecond
		}
	}

	avgMbps := totalMbps / float64(len(results))
	successRate := float64(successfulTests) / float64(len(results)) * 100

	if minMbps == math.MaxFloat64 {
		minMbps = 0
	}

	return TestTypeSummary{
		TestCount:       len(results),
		SuccessfulTests: successfulTests,
		FailedTests:     0, // 失败的测试不包含在结果中
		SuccessRate:     successRate,
		AvgMbps:         avgMbps,
		MinMbps:         minMbps,
		MaxMbps:         maxMbps,
		TotalBytes:      totalBytes,
	}
}

// setLastError 设置最后一个错误（线程安全）
func (c *DefaultTestCoordinator) setLastError(err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastError = err
}

// incrementSuccessfulRounds 增加成功回合计数器（线程安全）
func (c *DefaultTestCoordinator) incrementSuccessfulRounds() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.totalRounds++
	c.successfulRounds++
}

// incrementFailedRounds 增加失败回合计数器（线程安全）
func (c *DefaultTestCoordinator) incrementFailedRounds() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.totalRounds++
	c.failedRounds++
}
