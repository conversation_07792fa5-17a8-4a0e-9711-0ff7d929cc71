# iperf3-controller OpenWRT服务端1配置
# 服务器IP: ***********
# 监听端口: 6060

# 服务器配置
server:
  host: "0.0.0.0"          # 监听所有接口
  port: 6060               # Web管理界面端口
  mode: "server"           # 服务端模式
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

# 客户端管理配置
client_management:
  prepare_timeout: "10s"    # 客户端准备超时
  test_timeout: "60s"       # 测试执行超时
  stop_timeout: "5s"        # 停止超时
  retry_attempts: 3         # 重试次数
  retry_delay: "2s"         # 重试延迟

# 调度配置
schedule:
  mode: "odd"              # 奇数小时测试
  timezone: "Asia/Shanghai" # 时区设置

# 数据同步配置（与另一台OpenWRT同步）
sync:
  peer_host: "***********3"    # 对端OpenWRT IP
  peer_port: 6066              # 对端端口
  sync_interval: "30s"         # 同步间隔
  heartbeat_interval: "10s"    # 心跳间隔
  connect_timeout: "5s"        # 连接超时
  sync_timeout: "30s"          # 同步超时
  retry_attempts: 3            # 重试次数
  retry_delay: "2s"            # 重试延迟
  enable_compression: true     # 启用压缩
  enable_encryption: false     # 禁用加密

# 数据库配置
database:
  type: "sqlite"
  path: "/var/lib/iperf3-controller/iperf3-server1.db"
  max_connections: 10
  connection_timeout: "5s"

# 日志配置
logging:
  level: "info"
  file: "/var/log/iperf3-controller/server1.log"
  max_size: "50MB"
  max_backups: 3
  max_age: 7
  compress: true

# Web界面配置
web:
  static_dir: "/opt/iperf3-controller/web"
  enable_cors: true
  enable_auth: false
  api_key: "server1-secret-key"

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"
  server_port: 5201            # iperf3服务端口
  default_duration: 30         # 默认测试时长（秒）
  tcp:
    parallel_streams: 4        # 并行流数量
    window_size: "512K"        # TCP窗口大小
    mss: 1460                  # 最大段大小
  udp:
    bandwidth: "500M"          # 目标带宽
    packet_size: 1472          # UDP包大小

# 客户端列表 - 配置要测试的客户端
clients:
  - id: "client-138-2-114-254"
    name: "客户端 *************"
    host: "*************"
    port: 55201
    enabled: true
    description: "外网客户端1"
    
  - id: "client-193-123-80-169"
    name: "客户端 **************"
    host: "**************"
    port: 55202
    enabled: true
    description: "外网客户端2"

# 测试配置
testing:
  batch_size: 2                # 同时测试的客户端数量
  batch_delay: "3s"            # 批次间延迟
  enable_preemptive: true      # 启用抢占式测试
  result_retention_days: 30    # 测试结果保留天数

# 监控配置
monitoring:
  enable_performance_monitoring: true
  collection_interval: "1m"
  enable_health_check: true
  health_check_interval: "30s"

# 安全配置
security:
  enable_https: false
  allowed_ips:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
    - "***********/24"
    - "************/24"

# 系统配置
system:
  max_open_files: 65536
  tcp_keepalive: true
  tcp_nodelay: true
