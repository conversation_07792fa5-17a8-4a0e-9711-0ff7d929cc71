package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// Migration represents a database migration
type Migration struct {
	Version     int
	Description string
	Up          string
	Down        string
}

// migrations contains all database migrations in order
var migrations = []Migration{
	{
		Version:     1,
		Description: "Create initial tables",
		Up: `
-- Create servers table
CREATE TABLE IF NOT EXISTS servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    host TEXT NOT NULL,
    port INTEGER DEFAULT 55201,
    enabled BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create hourly_results table
CREATE TABLE IF NOT EXISTS hourly_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_name TEXT NOT NULL,
    test_hour TIMESTAMP NOT NULL,
    tcp_speed_mbps REAL,
    udp_speed_mbps REAL,
    tcp_status TEXT DEFAULT 'pending',
    udp_status TEXT DEFAULT 'pending',
    tcp_error TEXT,
    udp_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_name, test_hour)
);

-- Create sync_status table
CREATE TABLE IF NOT EXISTS sync_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    peer_ip TEXT NOT NULL,
    last_sync TIMESTAMP,
    sync_status TEXT DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create system_config table
CREATE TABLE IF NOT EXISTS system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create schema_migrations table for tracking migrations
CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
`,
		Down: `
DROP TABLE IF EXISTS schema_migrations;
DROP TABLE IF EXISTS system_config;
DROP TABLE IF EXISTS sync_status;
DROP TABLE IF EXISTS hourly_results;
DROP TABLE IF EXISTS servers;
`,
	},
	{
		Version:     2,
		Description: "Create indexes for performance optimization",
		Up: `
-- Indexes for hourly_results table
CREATE INDEX IF NOT EXISTS idx_hourly_results_server_hour ON hourly_results(server_name, test_hour);
CREATE INDEX IF NOT EXISTS idx_hourly_results_hour ON hourly_results(test_hour);
CREATE INDEX IF NOT EXISTS idx_hourly_results_status ON hourly_results(tcp_status, udp_status);
CREATE INDEX IF NOT EXISTS idx_hourly_results_created_at ON hourly_results(created_at);

-- Indexes for servers table
CREATE INDEX IF NOT EXISTS idx_servers_enabled ON servers(enabled);
CREATE INDEX IF NOT EXISTS idx_servers_name ON servers(name);

-- Indexes for sync_status table
CREATE INDEX IF NOT EXISTS idx_sync_status_peer_ip ON sync_status(peer_ip);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync);
`,
		Down: `
DROP INDEX IF EXISTS idx_sync_status_last_sync;
DROP INDEX IF EXISTS idx_sync_status_peer_ip;
DROP INDEX IF EXISTS idx_servers_name;
DROP INDEX IF EXISTS idx_servers_enabled;
DROP INDEX IF EXISTS idx_hourly_results_created_at;
DROP INDEX IF EXISTS idx_hourly_results_status;
DROP INDEX IF EXISTS idx_hourly_results_hour;
DROP INDEX IF EXISTS idx_hourly_results_server_hour;
`,
	},
	{
		Version:     3,
		Description: "Insert default system configuration",
		Up: `
-- Insert default system configuration
INSERT OR IGNORE INTO system_config (key, value, description) VALUES 
('schedule_mode', 'odd', 'Scheduling mode: odd/even/always'),
('peer_ip', '', 'Peer OpenWRT IP address'),
('peer_port', '55201', 'Peer OpenWRT port'),
('web_port', '6066', 'Web service port'),
('sync_interval', '300', 'Data sync interval in seconds'),
('test_timeout', '60', 'Test timeout in seconds'),
('max_concurrent_tests', '3', 'Maximum concurrent tests'),
('retention_days', '30', 'Data retention period in days');
`,
		Down: `
DELETE FROM system_config WHERE key IN (
    'schedule_mode', 'peer_ip', 'peer_port', 'web_port',
    'sync_interval', 'test_timeout', 'max_concurrent_tests', 'retention_days'
);
`,
	},
}

// Migrator handles database migrations
type Migrator struct {
	db     *sql.DB
	logger *logrus.Logger
}

// NewMigrator creates a new migrator instance
func NewMigrator(db *sql.DB, logger *logrus.Logger) *Migrator {
	return &Migrator{
		db:     db,
		logger: logger,
	}
}

// Migrate runs all pending migrations
func (m *Migrator) Migrate() error {
	m.logger.Info("Starting database migration")

	// Get current version
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	m.logger.WithField("current_version", currentVersion).Info("Current database version")

	// Apply pending migrations
	applied := 0
	for _, migration := range migrations {
		if migration.Version <= currentVersion {
			continue
		}

		m.logger.WithFields(logrus.Fields{
			"version":     migration.Version,
			"description": migration.Description,
		}).Info("Applying migration")

		if err := m.applyMigration(migration); err != nil {
			return fmt.Errorf("failed to apply migration %d: %w", migration.Version, err)
		}

		applied++
	}

	if applied > 0 {
		m.logger.WithField("applied_count", applied).Info("Database migration completed")
	} else {
		m.logger.Info("Database is up to date")
	}

	return nil
}

// Rollback rolls back to a specific version
func (m *Migrator) Rollback(targetVersion int) error {
	m.logger.WithField("target_version", targetVersion).Info("Starting database rollback")

	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	if targetVersion >= currentVersion {
		return fmt.Errorf("target version %d must be less than current version %d", targetVersion, currentVersion)
	}

	// Apply rollback migrations in reverse order
	for i := len(migrations) - 1; i >= 0; i-- {
		migration := migrations[i]
		if migration.Version <= targetVersion {
			break
		}

		if migration.Version > currentVersion {
			continue
		}

		m.logger.WithFields(logrus.Fields{
			"version":     migration.Version,
			"description": migration.Description,
		}).Info("Rolling back migration")

		if err := m.rollbackMigration(migration); err != nil {
			return fmt.Errorf("failed to rollback migration %d: %w", migration.Version, err)
		}
	}

	m.logger.Info("Database rollback completed")
	return nil
}

// getCurrentVersion returns the current database version
func (m *Migrator) getCurrentVersion() (int, error) {
	// First check if schema_migrations table exists
	var tableExists int
	err := m.db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='schema_migrations'").Scan(&tableExists)
	if err != nil {
		return 0, err
	}

	// If table doesn't exist, version is 0
	if tableExists == 0 {
		return 0, nil
	}

	var version int
	err = m.db.QueryRow("SELECT COALESCE(MAX(version), 0) FROM schema_migrations").Scan(&version)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil
		}
		return 0, err
	}
	return version, nil
}

// applyMigration applies a single migration
func (m *Migrator) applyMigration(migration Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Execute migration SQL
	if _, err := tx.Exec(migration.Up); err != nil {
		return fmt.Errorf("failed to execute migration SQL: %w", err)
	}

	// Record migration in schema_migrations table
	if _, err := tx.Exec("INSERT INTO schema_migrations (version, applied_at) VALUES (?, ?)",
		migration.Version, time.Now()); err != nil {
		return fmt.Errorf("failed to record migration: %w", err)
	}

	return tx.Commit()
}

// rollbackMigration rolls back a single migration
func (m *Migrator) rollbackMigration(migration Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Execute rollback SQL
	if _, err := tx.Exec(migration.Down); err != nil {
		return fmt.Errorf("failed to execute rollback SQL: %w", err)
	}

	// Remove migration record
	if _, err := tx.Exec("DELETE FROM schema_migrations WHERE version = ?", migration.Version); err != nil {
		return fmt.Errorf("failed to remove migration record: %w", err)
	}

	return tx.Commit()
}

// GetMigrationStatus returns the status of all migrations
func (m *Migrator) GetMigrationStatus() ([]MigrationStatus, error) {
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return nil, err
	}

	var status []MigrationStatus
	for _, migration := range migrations {
		applied := migration.Version <= currentVersion
		var appliedAt *time.Time

		if applied {
			var t time.Time
			err := m.db.QueryRow("SELECT applied_at FROM schema_migrations WHERE version = ?",
				migration.Version).Scan(&t)
			if err == nil {
				appliedAt = &t
			}
		}

		status = append(status, MigrationStatus{
			Version:     migration.Version,
			Description: migration.Description,
			Applied:     applied,
			AppliedAt:   appliedAt,
		})
	}

	return status, nil
}

// MigrationStatus represents the status of a migration
type MigrationStatus struct {
	Version     int        `json:"version"`
	Description string     `json:"description"`
	Applied     bool       `json:"applied"`
	AppliedAt   *time.Time `json:"applied_at"`
}
