package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// Migration 表示数据库迁移
type Migration struct {
	Version     int
	Description string
	Up          string
	Down        string
}

// migrations 包含所有按顺序的数据库迁移
var migrations = []Migration{
	{
		Version:     1,
		Description: "创建初始表",
		Up: `
-- 创建 servers 表
CREATE TABLE IF NOT EXISTS servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    host TEXT NOT NULL,
    port INTEGER DEFAULT 55201,
    enabled BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 hourly_results 表
CREATE TABLE IF NOT EXISTS hourly_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_name TEXT NOT NULL,
    test_hour TIMESTAMP NOT NULL,
    tcp_speed_mbps REAL,
    udp_speed_mbps REAL,
    tcp_status TEXT DEFAULT 'pending',
    udp_status TEXT DEFAULT 'pending',
    tcp_error TEXT,
    udp_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_name, test_hour)
);

-- 创建 sync_status 表
CREATE TABLE IF NOT EXISTS sync_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    peer_ip TEXT NOT NULL,
    last_sync TIMESTAMP,
    sync_status TEXT DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 system_config 表
CREATE TABLE IF NOT EXISTS system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 schema_migrations 表用于跟踪迁移
CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
`,
		Down: `
DROP TABLE IF EXISTS schema_migrations;
DROP TABLE IF EXISTS system_config;
DROP TABLE IF EXISTS sync_status;
DROP TABLE IF EXISTS hourly_results;
DROP TABLE IF EXISTS servers;
`,
	},
	{
		Version:     2,
		Description: "为性能优化创建索引",
		Up: `
-- hourly_results 表的索引
CREATE INDEX IF NOT EXISTS idx_hourly_results_server_hour ON hourly_results(server_name, test_hour);
CREATE INDEX IF NOT EXISTS idx_hourly_results_hour ON hourly_results(test_hour);
CREATE INDEX IF NOT EXISTS idx_hourly_results_status ON hourly_results(tcp_status, udp_status);
CREATE INDEX IF NOT EXISTS idx_hourly_results_created_at ON hourly_results(created_at);

-- servers 表的索引
CREATE INDEX IF NOT EXISTS idx_servers_enabled ON servers(enabled);
CREATE INDEX IF NOT EXISTS idx_servers_name ON servers(name);

-- sync_status 表的索引
CREATE INDEX IF NOT EXISTS idx_sync_status_peer_ip ON sync_status(peer_ip);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync);
`,
		Down: `
DROP INDEX IF EXISTS idx_sync_status_last_sync;
DROP INDEX IF EXISTS idx_sync_status_peer_ip;
DROP INDEX IF EXISTS idx_servers_name;
DROP INDEX IF EXISTS idx_servers_enabled;
DROP INDEX IF EXISTS idx_hourly_results_created_at;
DROP INDEX IF EXISTS idx_hourly_results_status;
DROP INDEX IF EXISTS idx_hourly_results_hour;
DROP INDEX IF EXISTS idx_hourly_results_server_hour;
`,
	},
	{
		Version:     3,
		Description: "插入默认系统配置",
		Up: `
-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (key, value, description) VALUES
('schedule_mode', 'odd', '调度模式: odd/even/always'),
('peer_ip', '', '对等 OpenWRT IP 地址'),
('peer_port', '55201', '对等 OpenWRT 端口'),
('web_port', '6066', 'Web 服务端口'),
('sync_interval', '300', '数据同步间隔（秒）'),
('test_timeout', '60', '测试超时（秒）'),
('max_concurrent_tests', '3', '最大并发测试数'),
('retention_days', '30', '数据保留天数');
`,
		Down: `
DELETE FROM system_config WHERE key IN (
    'schedule_mode', 'peer_ip', 'peer_port', 'web_port',
    'sync_interval', 'test_timeout', 'max_concurrent_tests', 'retention_days'
);
`,
	},
}

// Migrator 处理数据库迁移
type Migrator struct {
	db     *sql.DB
	logger *logrus.Logger
}

// NewMigrator 创建一个新的迁移器实例
func NewMigrator(db *sql.DB, logger *logrus.Logger) *Migrator {
	return &Migrator{
		db:     db,
		logger: logger,
	}
}

// Migrate 运行所有待处理的迁移
func (m *Migrator) Migrate() error {
	m.logger.Info("开始数据库迁移")

	// 获取当前版本
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("获取当前版本失败: %w", err)
	}

	m.logger.WithField("current_version", currentVersion).Info("当前数据库版本")

	// 应用待处理的迁移
	applied := 0
	for _, migration := range migrations {
		if migration.Version <= currentVersion {
			continue
		}

		m.logger.WithFields(logrus.Fields{
			"version":     migration.Version,
			"description": migration.Description,
		}).Info("正在应用迁移")

		if err := m.applyMigration(migration); err != nil {
			return fmt.Errorf("应用迁移 %d 失败: %w", migration.Version, err)
		}

		applied++
	}

	if applied > 0 {
		m.logger.WithField("applied_count", applied).Info("数据库迁移完成")
	} else {
		m.logger.Info("数据库已是最新版本")
	}

	return nil
}

// Rollback 回滚到特定版本
func (m *Migrator) Rollback(targetVersion int) error {
	m.logger.WithField("target_version", targetVersion).Info("开始数据库回滚")

	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("获取当前版本失败: %w", err)
	}

	if targetVersion >= currentVersion {
		return fmt.Errorf("目标版本 %d 必须小于当前版本 %d", targetVersion, currentVersion)
	}

	// 按逆序应用回滚迁移
	for i := len(migrations) - 1; i >= 0; i-- {
		migration := migrations[i]
		if migration.Version <= targetVersion {
			break
		}

		if migration.Version > currentVersion {
			continue
		}

		m.logger.WithFields(logrus.Fields{
			"version":     migration.Version,
			"description": migration.Description,
		}).Info("正在回滚迁移")

		if err := m.rollbackMigration(migration); err != nil {
			return fmt.Errorf("回滚迁移 %d 失败: %w", migration.Version, err)
		}
	}

	m.logger.Info("数据库回滚完成")
	return nil
}

// getCurrentVersion 返回当前数据库版本
func (m *Migrator) getCurrentVersion() (int, error) {
	// 首先检查 schema_migrations 表是否存在
	var tableExists int
	err := m.db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='schema_migrations'").Scan(&tableExists)
	if err != nil {
		return 0, err
	}

	// 如果表不存在，版本为 0
	if tableExists == 0 {
		return 0, nil
	}

	var version int
	err = m.db.QueryRow("SELECT COALESCE(MAX(version), 0) FROM schema_migrations").Scan(&version)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil
		}
		return 0, err
	}
	return version, nil
}

// applyMigration 应用单个迁移
func (m *Migrator) applyMigration(migration Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行迁移 SQL
	if _, err := tx.Exec(migration.Up); err != nil {
		return fmt.Errorf("执行迁移 SQL 失败: %w", err)
	}

	// 在 schema_migrations 表中记录迁移
	if _, err := tx.Exec("INSERT INTO schema_migrations (version, applied_at) VALUES (?, ?)",
		migration.Version, time.Now()); err != nil {
		return fmt.Errorf("记录迁移失败: %w", err)
	}

	return tx.Commit()
}

// rollbackMigration 回滚单个迁移
func (m *Migrator) rollbackMigration(migration Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行回滚 SQL
	if _, err := tx.Exec(migration.Down); err != nil {
		return fmt.Errorf("执行回滚 SQL 失败: %w", err)
	}

	// 移除迁移记录
	if _, err := tx.Exec("DELETE FROM schema_migrations WHERE version = ?", migration.Version); err != nil {
		return fmt.Errorf("移除迁移记录失败: %w", err)
	}

	return tx.Commit()
}

// GetMigrationStatus 返回所有迁移的状态
func (m *Migrator) GetMigrationStatus() ([]MigrationStatus, error) {
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return nil, err
	}

	var status []MigrationStatus
	for _, migration := range migrations {
		applied := migration.Version <= currentVersion
		var appliedAt *time.Time

		if applied {
			var t time.Time
			err := m.db.QueryRow("SELECT applied_at FROM schema_migrations WHERE version = ?",
				migration.Version).Scan(&t)
			if err == nil {
				appliedAt = &t
			}
		}

		status = append(status, MigrationStatus{
			Version:     migration.Version,
			Description: migration.Description,
			Applied:     applied,
			AppliedAt:   appliedAt,
		})
	}

	return status, nil
}

// MigrationStatus 表示迁移的状态
type MigrationStatus struct {
	Version     int        `json:"version"`
	Description string     `json:"description"`
	Applied     bool       `json:"applied"`
	AppliedAt   *time.Time `json:"applied_at"`
}
