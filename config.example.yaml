# iperf3-controller 配置文件示例
# 复制此文件为 config.yaml 并根据实际环境修改

# 服务器配置
server:
  # 监听地址
  host: "0.0.0.0"
  # 监听端口
  port: 8080
  # 运行模式: server (OpenWRT服务端) 或 client (测试客户端)
  mode: "server"
  # 读取超时
  read_timeout: "30s"
  # 写入超时
  write_timeout: "30s"
  # 空闲超时
  idle_timeout: "60s"

# 客户端管理配置
client_management:
  # 客户端准备超时时间
  prepare_timeout: "10s"
  # 测试执行超时时间
  test_timeout: "60s"
  # 客户端停止超时时间
  stop_timeout: "5s"
  # 重试次数
  retry_attempts: 3
  # 重试延迟
  retry_delay: "1s"

# 调度配置
schedule:
  # 调度模式: odd (奇数小时), even (偶数小时), always (每小时)
  mode: "odd"
  # 时区设置
  timezone: "Asia/Shanghai"

# 数据同步配置（双OpenWRT同步）
sync:
  # 对端OpenWRT地址
  peer_host: "*************"
  # 对端OpenWRT端口
  peer_port: 8080
  # 同步间隔
  sync_interval: "30s"
  # 心跳间隔
  heartbeat_interval: "10s"
  # 连接超时
  connect_timeout: "5s"
  # 同步超时
  sync_timeout: "30s"
  # 重试次数
  retry_attempts: 3
  # 重试延迟
  retry_delay: "2s"
  # 启用压缩
  enable_compression: true
  # 启用加密
  enable_encryption: false

# 数据库配置
database:
  # 数据库类型 (目前仅支持sqlite)
  type: "sqlite"
  # 数据库文件路径
  path: "/var/lib/iperf3-controller/iperf3.db"
  # 连接池大小
  max_connections: 10
  # 连接超时
  connection_timeout: "5s"

# 日志配置
logging:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志文件路径
  file: "/var/log/iperf3-controller/iperf3-controller.log"
  # 单个日志文件最大大小
  max_size: "100MB"
  # 保留的日志文件数量
  max_backups: 5
  # 日志文件保留天数
  max_age: 30
  # 是否压缩旧日志文件
  compress: true

# Web界面配置
web:
  # 静态文件目录
  static_dir: "/opt/iperf3-controller/web"
  # 启用CORS
  enable_cors: true
  # 启用API认证
  enable_auth: false
  # API密钥 (当enable_auth为true时使用)
  api_key: "iperf3-controller-api-key"

# iperf3配置
iperf3:
  # iperf3可执行文件路径 (留空使用系统PATH)
  binary_path: ""
  # 默认测试持续时间 (秒)
  default_duration: 30
  # TCP测试配置
  tcp:
    # 并行流数量
    parallel_streams: 4
    # TCP窗口大小
    window_size: "512K"
    # 最大段大小
    mss: 1460
  # UDP测试配置
  udp:
    # 目标带宽
    bandwidth: "500M"
    # UDP数据包大小
    packet_size: 1472

# 客户端列表配置
clients:
  # 客户端1 - 示例配置
  - id: "server-01"
    name: "服务器01"
    host: "*************"
    port: 55201
    enabled: true
    description: "主要测试服务器"
    
  # 客户端2 - 示例配置
  - id: "server-02"
    name: "服务器02"
    host: "*************"
    port: 55202
    enabled: true
    description: "备用测试服务器"
    
  # 客户端3 - 示例配置
  - id: "server-03"
    name: "服务器03"
    host: "*************"
    port: 55203
    enabled: true
    description: "边缘测试服务器"
    
  # 更多客户端...
  # 支持最多12台服务器客户端
  
# 测试配置
testing:
  # 批次大小 (同时测试的客户端数量)
  batch_size: 3
  # 批次间延迟
  batch_delay: "2s"
  # 启用抢占式测试
  enable_preemptive: true
  # 测试结果保留天数
  result_retention_days: 30

# 监控配置
monitoring:
  # 启用性能监控
  enable_performance_monitoring: true
  # 监控数据收集间隔
  collection_interval: "1m"
  # 启用健康检查
  enable_health_check: true
  # 健康检查间隔
  health_check_interval: "30s"

# 通知配置 (可选)
notifications:
  # 启用通知
  enabled: false
  # 通知方式: email, webhook, syslog
  methods:
    - "syslog"
  # 通知条件
  conditions:
    # 测试失败时通知
    on_test_failure: true
    # 同步失败时通知
    on_sync_failure: true
    # 客户端离线时通知
    on_client_offline: true

# 安全配置
security:
  # 启用HTTPS
  enable_https: false
  # SSL证书文件路径
  cert_file: ""
  # SSL私钥文件路径
  key_file: ""
  # 允许的IP地址范围
  allowed_ips:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"

# 高级配置
advanced:
  # 启用调试模式
  debug_mode: false
  # 性能分析端口 (调试用)
  pprof_port: 6060
  # 启用指标收集
  enable_metrics: true
  # 指标端口
  metrics_port: 9090
