# iperf3-controller OpenWRT2客户端配置
# OpenWRT设备作为客户端，主动连接12台外网iperf3服务端进行测试
# IP: ************, 端口: 6066, 调度: 偶数小时

# 服务器配置
server:
  # 监听地址
  host: "0.0.0.0"
  # Web管理界面端口
  port: 6066
  # 运行模式: client (OpenWRT客户端模式)
  mode: "client"
  # 读取超时
  read_timeout: "30s"
  # 写入超时
  write_timeout: "30s"
  # 空闲超时
  idle_timeout: "60s"

# 客户端管理配置
client_management:
  # 客户端准备超时时间
  prepare_timeout: "10s"
  # 测试执行超时时间
  test_timeout: "60s"
  # 客户端停止超时时间
  stop_timeout: "5s"
  # 重试次数
  retry_attempts: 3
  # 重试延迟
  retry_delay: "2s"

# 调度配置
schedule:
  # 调度模式: even (偶数小时测试)
  mode: "even"
  # 时区设置
  timezone: "Asia/Shanghai"

# 数据同步配置（与OpenWRT1同步）
sync:
  # 对端OpenWRT1地址
  peer_host: "***********"
  # 对端OpenWRT1端口
  peer_port: 6060
  # 同步间隔
  sync_interval: "30s"
  # 心跳间隔
  heartbeat_interval: "10s"
  # 连接超时
  connect_timeout: "5s"
  # 同步超时
  sync_timeout: "30s"
  # 重试次数
  retry_attempts: 3
  # 重试延迟
  retry_delay: "2s"
  # 启用压缩
  enable_compression: true
  # 启用加密
  enable_encryption: false

# 数据库配置
database:
  # 数据库类型 (目前仅支持sqlite)
  type: "sqlite"
  # 数据库文件路径
  path: "/var/lib/iperf3-controller/iperf3-openwrt2.db"
  # 连接池大小
  max_connections: 10
  # 连接超时
  connection_timeout: "5s"

# 日志配置
logging:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志文件路径
  file: "/var/log/iperf3-controller/openwrt2-client.log"
  # 单个日志文件最大大小
  max_size: "50MB"
  # 保留的日志文件数量
  max_backups: 3
  # 日志文件保留天数
  max_age: 7
  # 是否压缩旧日志文件
  compress: true

# Web界面配置
web:
  # 静态文件目录
  static_dir: "/opt/iperf3-controller/web"
  # 启用CORS
  enable_cors: true
  # 启用API认证
  enable_auth: false
  # API密钥
  api_key: "openwrt2-client-key"

# iperf3配置
iperf3:
  # iperf3可执行文件路径
  binary_path: "/usr/bin/iperf3"
  # 默认测试持续时间 (秒)
  default_duration: 30
  # TCP测试配置
  tcp:
    # 并行流数量
    parallel_streams: 4
    # TCP窗口大小
    window_size: "512K"
    # 最大段大小
    mss: 1460
  # UDP测试配置
  udp:
    # 目标带宽
    bandwidth: "500M"
    # UDP数据包大小
    packet_size: 1472

# 服务器列表配置 (要连接的iperf3服务端)
servers:
  # 服务器1 - 用户指定的外网服务器
  - id: "server-138-2-114-254"
    name: "外网服务器1"
    host: "*************"
    port: 5201
    enabled: true
    description: "用户指定的外网测试服务器1"

  # 服务器2 - 用户指定的外网服务器
  - id: "server-193-123-80-169"
    name: "外网服务器2"
    host: "**************"
    port: 5202
    enabled: true
    description: "用户指定的外网测试服务器2"

  # 服务器3-12 - 示例外网服务器
  - id: "server-203-0-113-1"
    name: "外网服务器3"
    host: "***********"
    port: 5203
    enabled: true
    description: "示例外网测试服务器3"

  - id: "server-203-0-113-2"
    name: "外网服务器4"
    host: "***********"
    port: 5204
    enabled: true
    description: "示例外网测试服务器4"

  - id: "server-203-0-113-3"
    name: "外网服务器5"
    host: "***********"
    port: 5205
    enabled: true
    description: "示例外网测试服务器5"

  - id: "server-203-0-113-4"
    name: "外网服务器6"
    host: "***********"
    port: 5206
    enabled: true
    description: "示例外网测试服务器6"

  - id: "server-203-0-113-5"
    name: "外网服务器7"
    host: "***********"
    port: 5207
    enabled: true
    description: "示例外网测试服务器7"

  - id: "server-203-0-113-6"
    name: "外网服务器8"
    host: "***********"
    port: 5208
    enabled: true
    description: "示例外网测试服务器8"

  - id: "server-203-0-113-7"
    name: "外网服务器9"
    host: "***********"
    port: 5209
    enabled: true
    description: "示例外网测试服务器9"

  - id: "server-203-0-113-8"
    name: "外网服务器10"
    host: "***********"
    port: 5210
    enabled: true
    description: "示例外网测试服务器10"

  - id: "server-203-0-113-9"
    name: "外网服务器11"
    host: "***********"
    port: 5211
    enabled: true
    description: "示例外网测试服务器11"

  - id: "server-203-0-113-10"
    name: "外网服务器12"
    host: "***********0"
    port: 5212
    enabled: true
    description: "示例外网测试服务器12"

# 测试配置
testing:
  # 批次大小 (同时测试的服务器数量)
  batch_size: 3
  # 批次间延迟
  batch_delay: "3s"
  # 启用抢占式测试
  enable_preemptive: true
  # 测试结果保留天数
  result_retention_days: 30

# 监控配置
monitoring:
  # 启用性能监控
  enable_performance_monitoring: true
  # 监控数据收集间隔
  collection_interval: "1m"
  # 启用健康检查
  enable_health_check: true
  # 健康检查间隔
  health_check_interval: "30s"

# 通知配置 (可选)
notifications:
  # 启用通知
  enabled: false
  # 通知方式: syslog
  methods:
    - "syslog"
  # 通知条件
  conditions:
    # 测试失败时通知
    on_test_failure: true
    # 同步失败时通知
    on_sync_failure: true
    # 服务器离线时通知
    on_server_offline: true

# 安全配置
security:
  # 启用HTTPS
  enable_https: false
  # SSL证书文件路径
  cert_file: ""
  # SSL私钥文件路径
  key_file: ""
  # 允许的IP地址范围
  allowed_ips:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
    - "***********/24"
    - "************/24"
    - "***********/24"

# 高级配置
advanced:
  # 启用调试模式
  debug_mode: false
  # 性能分析端口 (调试用)
  pprof_port: 6062
  # 启用指标收集
  enable_metrics: true
  # 指标端口
  metrics_port: 9092
