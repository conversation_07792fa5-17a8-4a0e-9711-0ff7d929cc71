package coordinator

import (
	"context"
	"sync"
)

// ConcurrencyController 定义了管理并发测试执行的接口。
type ConcurrencyController interface {
	// AcquireSlot 尝试获取一个新并发测试的槽位。
	// 它会阻塞直到有可用槽位或上下文被取消。
	AcquireSlot(ctx context.Context) error
	// ReleaseSlot 释放先前获取的槽位。
	ReleaseSlot()
	// ExecuteWithLock 执行一个带有全局锁的函数，防止对关键资源的并发访问。
	ExecuteWithLock(f func())
}

// NewConcurrencyController 创建 ConcurrencyController 的新实例。
// maxConcurrentTests 指定可以并发运行的最大测试数量。
func NewConcurrencyController(maxConcurrentTests int) ConcurrencyController {
	if maxConcurrentTests <= 0 {
		maxConcurrentTests = 1 // 如果提供了无效值，则默认为 1
	}
	return &defaultConcurrencyController{
		sem: make(chan struct{}, maxConcurrentTests),
		mu:  &sync.Mutex{},
	}
}

type defaultConcurrencyController struct {
	sem chan struct{} // 限制并发测试的信号量
	mu  *sync.Mutex   // 关键部分的互斥锁
}

// AcquireSlot 实现了 ConcurrencyController。
func (dcc *defaultConcurrencyController) AcquireSlot(ctx context.Context) error {
	select {
	case dcc.sem <- struct{}{}:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// ReleaseSlot 实现了 ConcurrencyController。
func (dcc *defaultConcurrencyController) ReleaseSlot() {
	<-dcc.sem
}

// ExecuteWithLock 实现了 ConcurrencyController。
func (dcc *defaultConcurrencyController) ExecuteWithLock(f func()) {
	dcc.mu.Lock()
	defer dcc.mu.Unlock()
	f()
}
