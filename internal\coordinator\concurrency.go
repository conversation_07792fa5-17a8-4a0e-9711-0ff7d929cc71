package coordinator

import (
	"context"
	"sync"
)

// ConcurrencyController defines the interface for managing concurrent test execution.
type ConcurrencyController interface {
	// AcquireSlot attempts to acquire a slot for a new concurrent test.
	// It blocks until a slot is available or the context is cancelled.
	AcquireSlot(ctx context.Context) error
	// ReleaseSlot releases a previously acquired slot.
	ReleaseSlot()
	// ExecuteWithLock executes a function with a global lock, preventing concurrent access to critical resources.
	ExecuteWithLock(f func())
}

// NewConcurrencyController creates a new instance of ConcurrencyController.
// maxConcurrentTests specifies the maximum number of tests that can run concurrently.
func NewConcurrencyController(maxConcurrentTests int) ConcurrencyController {
	if maxConcurrentTests <= 0 {
		maxConcurrentTests = 1 // Default to 1 if invalid value is provided
	}
	return &defaultConcurrencyController{
		sem: make(chan struct{}, maxConcurrentTests),
		mu:  &sync.Mutex{},
	}
}

type defaultConcurrencyController struct {
	sem chan struct{} // Semaphore to limit concurrent tests
	mu  *sync.Mutex   // Mutex for critical sections
}

// AcquireSlot implements ConcurrencyController.
func (dcc *defaultConcurrencyController) AcquireSlot(ctx context.Context) error {
	select {
	case dcc.sem <- struct{}{}:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// ReleaseSlot implements ConcurrencyController.
func (dcc *defaultConcurrencyController) ReleaseSlot() {
	<-dcc.sem
}

// ExecuteWithLock implements ConcurrencyController.
func (dcc *defaultConcurrencyController) ExecuteWithLock(f func()) {
	dcc.mu.Lock()
	defer dcc.mu.Unlock()
	f()
}
