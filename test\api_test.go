package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"iperf3-controller/internal/api"
	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/scheduler"
	"iperf3-controller/internal/sync"
)

// TestAPIServer 测试API服务器
func TestAPIServer(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 创建依赖组件
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
	if err != nil {
		t.Fatalf("创建调度器失败: %v", err)
	}

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	// 创建API服务器
	serverConfig := &api.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
		EnableCORS:   true,
		EnableAuth:   false,
		StaticDir:    "web/static",
	}

	apiServer := api.NewServer(
		serverConfig,
		clientManager,
		testCoordinator,
		testScheduler,
		syncManager,
		nil,
		logger,
	)

	if apiServer == nil {
		t.Fatal("API服务器创建失败")
	}

	router := apiServer.GetRouter()
	if router == nil {
		t.Fatal("获取路由器失败")
	}

	// 添加测试客户端
	testClients := []client.ClientInfo{
		{
			ID:      "api-test-client-1",
			Name:    "API测试客户端1",
			Host:    "*************",
			Port:    55200,
			Enabled: true,
		},
		{
			ID:      "api-test-client-2",
			Name:    "API测试客户端2",
			Host:    "*************",
			Port:    55201,
			Enabled: true,
		},
	}

	for _, clientInfo := range testClients {
		clientManager.AddClient(clientInfo)
	}

	// 启动核心服务
	ctx := context.Background()
	testCoordinator.Start(ctx)
	testScheduler.Start(ctx)
	syncManager.Start(ctx)

	t.Run("健康检查", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("健康检查失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["status"] != "healthy" {
			t.Fatalf("健康状态不正确: %v", response["status"])
		}

		t.Log("✅ 健康检查通过")
	})

	t.Run("系统状态", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/status", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("系统状态查询失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Fatal("响应数据格式不正确")
		}

		components, ok := data["components"].(map[string]interface{})
		if !ok {
			t.Fatal("组件数据格式不正确")
		}

		// 验证组件状态
		if _, ok := components["scheduler"]; !ok {
			t.Fatal("缺少调度器状态")
		}

		if _, ok := components["sync"]; !ok {
			t.Fatal("缺少同步状态")
		}

		if _, ok := components["clients"]; !ok {
			t.Fatal("缺少客户端状态")
		}

		t.Log("✅ 系统状态查询通过")
	})

	t.Run("客户端列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/clients", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("客户端列表查询失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Fatal("响应数据格式不正确")
		}

		clients, ok := data["clients"].([]interface{})
		if !ok {
			t.Fatal("客户端数据格式不正确")
		}

		if len(clients) != 2 {
			t.Fatalf("客户端数量不正确: 期望 2, 实际 %d", len(clients))
		}

		t.Log("✅ 客户端列表查询通过")
	})

	t.Run("添加客户端", func(t *testing.T) {
		newClient := map[string]interface{}{
			"id":      "api-test-client-3",
			"name":    "API测试客户端3",
			"host":    "*************",
			"port":    55202,
			"enabled": true,
		}

		jsonData, _ := json.Marshal(newClient)
		req, _ := http.NewRequest("POST", "/api/v1/clients", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusCreated {
			t.Fatalf("添加客户端失败: 状态码 %d, 响应: %s", w.Code, w.Body.String())
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		t.Log("✅ 添加客户端通过")
	})

	t.Run("获取单个客户端", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/clients/api-test-client-1", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("获取客户端失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Fatal("响应数据格式不正确")
		}

		if data["id"] != "api-test-client-1" {
			t.Fatalf("客户端ID不匹配: 期望 api-test-client-1, 实际 %v", data["id"])
		}

		t.Log("✅ 获取单个客户端通过")
	})

	t.Run("删除客户端", func(t *testing.T) {
		req, _ := http.NewRequest("DELETE", "/api/v1/clients/api-test-client-3", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("删除客户端失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		t.Log("✅ 删除客户端通过")
	})

	t.Run("触发测试", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/tests/trigger", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("触发测试失败: 状态码 %d", w.Code)
		}

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		if response["success"] != true {
			t.Fatalf("响应状态不正确: %v", response["success"])
		}

		t.Log("✅ 触发测试通过")
	})

	t.Run("调度器控制", func(t *testing.T) {
		// 停止调度器
		req, _ := http.NewRequest("POST", "/api/v1/schedule/stop", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("停止调度器失败: 状态码 %d", w.Code)
		}

		// 启动调度器
		req, _ = http.NewRequest("POST", "/api/v1/schedule/start", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("启动调度器失败: 状态码 %d", w.Code)
		}

		// 查询调度状态
		req, _ = http.NewRequest("GET", "/api/v1/schedule/status", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("查询调度状态失败: 状态码 %d", w.Code)
		}

		t.Log("✅ 调度器控制通过")
	})

	t.Run("同步管理", func(t *testing.T) {
		// 停止同步
		req, _ := http.NewRequest("POST", "/api/v1/sync/stop", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("停止同步失败: 状态码 %d", w.Code)
		}

		// 启动同步
		req, _ = http.NewRequest("POST", "/api/v1/sync/start", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("启动同步失败: 状态码 %d", w.Code)
		}

		// 查询同步状态
		req, _ = http.NewRequest("GET", "/api/v1/sync/status", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("查询同步状态失败: 状态码 %d", w.Code)
		}

		t.Log("✅ 同步管理通过")
	})

	t.Run("统计数据", func(t *testing.T) {
		endpoints := []string{
			"/api/v1/stats/overview",
			"/api/v1/stats/performance",
			"/api/v1/stats/clients",
			"/api/v1/stats/tests",
		}

		for _, endpoint := range endpoints {
			req, _ := http.NewRequest("GET", endpoint, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if w.Code != http.StatusOK {
				t.Fatalf("统计数据查询失败 %s: 状态码 %d", endpoint, w.Code)
			}

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			if err != nil {
				t.Fatalf("解析响应失败 %s: %v", endpoint, err)
			}

			if response["success"] != true {
				t.Fatalf("响应状态不正确 %s: %v", endpoint, response["success"])
			}
		}

		t.Log("✅ 统计数据查询通过")
	})

	t.Run("错误处理", func(t *testing.T) {
		// 404错误
		req, _ := http.NewRequest("GET", "/api/v1/nonexistent", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusNotFound {
			t.Fatalf("404错误处理失败: 状态码 %d", w.Code)
		}

		// 获取不存在的客户端
		req, _ = http.NewRequest("GET", "/api/v1/clients/nonexistent", nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusNotFound {
			t.Fatalf("不存在客户端错误处理失败: 状态码 %d", w.Code)
		}

		// 无效JSON
		req, _ = http.NewRequest("POST", "/api/v1/clients", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusBadRequest {
			t.Fatalf("无效JSON错误处理失败: 状态码 %d", w.Code)
		}

		t.Log("✅ 错误处理通过")
	})

	// 清理
	syncManager.Stop()
	testScheduler.Stop()
	testCoordinator.Stop()
}

// TestAPIServerConcurrency API服务器并发测试
func TestAPIServerConcurrency(t *testing.T) {
	gin.SetMode(gin.TestMode)

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 创建简化的API服务器
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 1 * time.Second,
		TestTimeout:    5 * time.Second,
		StopTimeout:    1 * time.Second,
		RetryAttempts:  1,
		RetryDelay:     100 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	testScheduler, _ := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	serverConfig := &api.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
		EnableCORS:   true,
		EnableAuth:   false,
		StaticDir:    "web/static",
	}

	apiServer := api.NewServer(
		serverConfig,
		clientManager,
		testCoordinator,
		testScheduler,
		syncManager,
		nil,
		logger,
	)

	router := apiServer.GetRouter()

	t.Run("并发API请求", func(t *testing.T) {
		done := make(chan bool, 50)

		// 并发发送50个请求
		for i := 0; i < 50; i++ {
			go func(index int) {
				req, _ := http.NewRequest("GET", "/health", nil)
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if w.Code != http.StatusOK {
					t.Errorf("并发请求 %d 失败: 状态码 %d", index, w.Code)
				}
				done <- true
			}(i)
		}

		// 等待所有请求完成
		for i := 0; i < 50; i++ {
			<-done
		}

		t.Log("✅ 并发API请求测试完成")
	})

	t.Run("混合并发请求", func(t *testing.T) {
		done := make(chan bool, 30)

		endpoints := []string{
			"/health",
			"/api/v1/status",
			"/api/v1/clients",
			"/api/v1/schedule/status",
			"/api/v1/sync/status",
		}

		// 并发发送不同类型的请求
		for i := 0; i < 30; i++ {
			go func(index int) {
				endpoint := endpoints[index%len(endpoints)]
				req, _ := http.NewRequest("GET", endpoint, nil)
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if w.Code != http.StatusOK {
					t.Errorf("混合并发请求 %d (%s) 失败: 状态码 %d", index, endpoint, w.Code)
				}
				done <- true
			}(i)
		}

		// 等待所有请求完成
		for i := 0; i < 30; i++ {
			<-done
		}

		t.Log("✅ 混合并发请求测试完成")
	})
}

// BenchmarkAPIServer API服务器性能测试
func BenchmarkAPIServer(b *testing.B) {
	gin.SetMode(gin.TestMode)

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 1 * time.Second,
		TestTimeout:    5 * time.Second,
		StopTimeout:    1 * time.Second,
		RetryAttempts:  1,
		RetryDelay:     100 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	testScheduler, _ := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	serverConfig := &api.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
		EnableCORS:   true,
		EnableAuth:   false,
		StaticDir:    "web/static",
	}

	apiServer := api.NewServer(
		serverConfig,
		clientManager,
		testCoordinator,
		testScheduler,
		syncManager,
		nil,
		logger,
	)

	router := apiServer.GetRouter()

	b.Run("HealthCheck", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequest("GET", "/health", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})

	b.Run("SystemStatus", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequest("GET", "/api/v1/status", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})
}
