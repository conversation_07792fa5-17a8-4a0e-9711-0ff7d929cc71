package coordinator

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// ResultParser 定义了用于解析和验证 iperf3 测试结果的接口。
type ResultParser interface {
	// Parse 解析 iperf3 的原始 JSON 输出并返回结构化结果。
	Parse(rawJSON string) (*Iperf3Result, error)
	// Validate 根据特定标准验证解析后的 iperf3 结果。
	Validate(result *Iperf3Result) error
}

// NewResultParser 创建 ResultParser 的新实例。
func NewResultParser() ResultParser {
	return &defaultResultParser{}
}

type defaultResultParser struct{}

// Parse 实现了 ResultParser 接口的 Parse 方法。
func (drp *defaultResultParser) Parse(rawJSON string) (*Iperf3Result, error) {
	var result Iperf3Result
	err := json.Unmarshal([]byte(rawJSON), &result)
	if err != nil {
		return nil, fmt.Errorf("iperf3 JSON 解析失败: %w", err)
	}
	return &result, nil
}

// Validate 实现了 ResultParser 接口的 Validate 方法。
func (drp *defaultResultParser) Validate(result *Iperf3Result) error {
	if result == nil {
		return errors.New("iperf3 结果为空")
	}
	// 实现结果验证逻辑
	// 检查必要的结构是否存在
	if result.End == nil || result.End.SumReceived == nil {
		return fmt.Errorf("缺少必要的iperf3结果数据")
	}

	// 检查速度值的合理性（转换为Mbps）
	mbitsPerSecond := result.End.SumReceived.BitsPerSecond / 1000000.0
	if mbitsPerSecond < 0 || mbitsPerSecond > 100000 {
		return fmt.Errorf("速度值异常: %.2f Mbps", mbitsPerSecond)
	}

	// 检查传输字节数
	if result.End.SumReceived.Bytes < 0 {
		return fmt.Errorf("传输字节数异常: %d", result.End.SumReceived.Bytes)
	}

	// 检查测试持续时间的合理性
	duration := time.Duration(result.End.SumReceived.Seconds) * time.Second
	if duration < 0 || duration > 24*time.Hour {
		return fmt.Errorf("测试持续时间异常: %v", duration)
	}
	if result.End == nil || result.End.SumReceived == nil {
		return errors.New("缺少必要的 iperf3 结果数据")
	}
	if result.End.SumReceived.BitsPerSecond <= 0 {
		return errors.New("接收带宽为零或负数")
	}
	return nil
}

// Iperf3Result 表示 iperf3 JSON 输出的结构。
// 这是一个简化的结构；您可能需要根据实际的 iperf3 输出进行扩展。
type Iperf3Result struct {
	Start *struct {
		Timestamp *struct {
			Time     string `json:"time"`
			Timesecs int64  `json:"timesecs"`
		} `json:"timestamp"`
		Version string `json:"version"`
		// ... 其他字段
	} `json:"start"`
	Intervals []struct {
		Streams []struct {
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			// ... 其他字段
		} `json:"streams"`
	} `json:"intervals"`
	End *struct {
		SumReceived *struct {
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			Retransmits   int64   `json:"retransmits"`
			Omitted       bool    `json:"omitted"`
			// ... 其他字段
		} `json:"sum_received"`
		// ... 其他字段
	} `json:"end"`
	// ... 其他顶级字段
}
