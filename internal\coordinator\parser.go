package coordinator

import (
	"encoding/json"
	"errors"
	"fmt"
)

// ResultParser defines the interface for parsing and validating iperf3 test results.
type ResultParser interface {
	// Parse parses the raw JSON output from iperf3 and returns a structured result.
	Parse(rawJSON string) (*Iperf3Result, error)
	// Validate validates the parsed iperf3 result against certain criteria.
	Validate(result *Iperf3Result) error
}

// NewResultParser creates a new instance of the ResultParser.
func NewResultParser() ResultParser {
	return &defaultResultParser{}
}

type defaultResultParser struct{}

// Parse implements ResultParser.
func (drp *defaultResultParser) Parse(rawJSON string) (*Iperf3Result, error) {
	var result Iperf3Result
	err := json.Unmarshal([]byte(rawJSON), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal iperf3 JSON: %w", err)
	}
	return &result, nil
}

// Validate implements ResultParser.
func (drp *defaultResultParser) Validate(result *Iperf3Result) error {
	if result == nil {
		return errors.New("iperf3 result is nil")
	}
	// TODO: Implement actual validation logic (e.g., check bandwidth, jitter, packet loss)
	if result.End == nil || result.End.SumReceived == nil {
		return errors.New("missing essential iperf3 result data")
	}
	if result.End.SumReceived.BitsPerSecond <= 0 {
		return errors.New("received bandwidth is zero or negative")
	}
	return nil
}

// Iperf3Result represents the structure of iperf3 JSON output.
// This is a simplified structure; you might need to expand it based on actual iperf3 output.
type Iperf3Result struct {
	Start *struct {
		Timestamp *struct {
			Time     string `json:"time"`
			Timesecs int64  `json:"timesecs"`
		} `json:"timestamp"`
		Version string `json:"version"`
		// ... other fields
	} `json:"start"`
	Intervals []struct {
		Streams []struct {
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			// ... other fields
		} `json:"streams"`
	} `json:"intervals"`
	End *struct {
		SumReceived *struct {
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			Retransmits   int64   `json:"retransmits"`
			Omitted       bool    `json:"omitted"`
			// ... other fields
		} `json:"sum_received"`
		// ... other fields
	} `json:"end"`
	// ... other top-level fields
}
