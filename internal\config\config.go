package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Config 表示应用程序配置
type Config struct {
	// 服务器配置
	Server ServerConfig `yaml:"server" mapstructure:"server"`

	// 调度配置
	Schedule ScheduleConfig `yaml:"schedule" mapstructure:"schedule"`

	// 对等 OpenWRT 配置
	Peer PeerConfig `yaml:"peer" mapstructure:"peer"`

	// 数据库配置
	Database DatabaseConfig `yaml:"database" mapstructure:"database"`

	// 服务器列表
	Servers []ServerConfig `yaml:"servers" mapstructure:"servers"`

	// 性能配置
	Performance PerformanceConfig `yaml:"performance" mapstructure:"performance"`

	// 客户端管理配置
	ClientManagement ClientManagementConfig `yaml:"client_management" mapstructure:"client_management"`

	// 同步配置
	Sync SyncConfig `yaml:"sync" mapstructure:"sync"`

	// 日志配置
	Logging LoggingConfig `yaml:"logging" mapstructure:"logging"`
}

// ClientConfig 表示客户端配置
type ClientConfig struct {
	Host      string        `yaml:"host" mapstructure:"host"`
	Port      int           `yaml:"port" mapstructure:"port"`
	Duration  time.Duration `yaml:"duration" mapstructure:"duration"`
	Bandwidth string        `yaml:"bandwidth" mapstructure:"bandwidth"`
}

// ServerConfig 表示服务器配置
type ServerConfig struct {
	Name       string `yaml:"name" mapstructure:"name"`
	Host       string `yaml:"host" mapstructure:"host"`
	Port       int    `yaml:"port" mapstructure:"port"`
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	ListenPort int    `yaml:"listen_port" mapstructure:"listen_port"`
	WebPort    int    `yaml:"web_port" mapstructure:"web_port"`
	PeerIP     string `yaml:"peer_ip" mapstructure:"peer_ip"`
	PeerPort   int    `yaml:"peer_port" mapstructure:"peer_port"`
}

// ScheduleConfig 表示调度配置
type ScheduleConfig struct {
	Mode     string `yaml:"mode" mapstructure:"mode"`         // "odd", "even", "always" （奇数、偶数、总是）
	Timezone string `yaml:"timezone" mapstructure:"timezone"` // "Asia/Shanghai" （亚洲/上海）
}

// PeerConfig 表示对等 OpenWRT 配置
type PeerConfig struct {
	IP           string        `yaml:"ip" mapstructure:"ip"`
	Port         int           `yaml:"port" mapstructure:"port"`
	SyncInterval time.Duration `yaml:"sync_interval" mapstructure:"sync_interval"`
}

// DatabaseConfig 表示数据库配置
type DatabaseConfig struct {
	Path            string        `yaml:"path" mapstructure:"path"`
	BackupInterval  time.Duration `yaml:"backup_interval" mapstructure:"backup_interval"`
	MaxSizeMB       int           `yaml:"max_size_mb" mapstructure:"max_size_mb"`
	RetentionDays   int           `yaml:"retention_days" mapstructure:"retention_days"`
	MaxOpenConns    int           `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time"`
	BusyTimeout     time.Duration `yaml:"busy_timeout" mapstructure:"busy_timeout"`
	JournalMode     string        `yaml:"journal_mode" mapstructure:"journal_mode"`
	Synchronous     string        `yaml:"synchronous" mapstructure:"synchronous"`
	CacheSize       int           `yaml:"cache_size" mapstructure:"cache_size"`
}

// PerformanceConfig 表示性能配置
type PerformanceConfig struct {
	TCP         TCPConfig         `yaml:"tcp" mapstructure:"tcp"`
	UDP         UDPConfig         `yaml:"udp" mapstructure:"udp"`
	Concurrency ConcurrencyConfig `yaml:"concurrency" mapstructure:"concurrency"`
}

// TCPConfig 表示 TCP 测试配置
type TCPConfig struct {
	ParallelStreams int           `yaml:"parallel_streams" mapstructure:"parallel_streams"`
	WindowSize      string        `yaml:"window_size" mapstructure:"window_size"`
	MSS             int           `yaml:"mss" mapstructure:"mss"`
	Duration        time.Duration `yaml:"duration" mapstructure:"duration"`
}

// UDPConfig 表示 UDP 测试配置
type UDPConfig struct {
	Bandwidth  string        `yaml:"bandwidth" mapstructure:"bandwidth"`
	PacketSize int           `yaml:"packet_size" mapstructure:"packet_size"`
	Duration   time.Duration `yaml:"duration" mapstructure:"duration"`
}

// ConcurrencyConfig 表示并发配置
type ConcurrencyConfig struct {
	MaxWorkers int           `yaml:"max_workers" mapstructure:"max_workers"`
	BatchSize  int           `yaml:"batch_size" mapstructure:"batch_size"`
	BatchDelay time.Duration `yaml:"batch_delay" mapstructure:"batch_delay"`
}

// ClientManagementConfig 表示客户端管理配置
type ClientManagementConfig struct {
	PrepareTimeout time.Duration `yaml:"prepare_timeout" mapstructure:"prepare_timeout"`
	TestTimeout    time.Duration `yaml:"test_timeout" mapstructure:"test_timeout"`
	StopTimeout    time.Duration `yaml:"stop_timeout" mapstructure:"stop_timeout"`
	RetryAttempts  int           `yaml:"retry_attempts" mapstructure:"retry_attempts"`
	RetryDelay     time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"`
}

// SyncConfig 表示同步配置
type SyncConfig struct {
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
	Interval time.Duration `yaml:"interval" mapstructure:"interval"`
	Timeout  time.Duration `yaml:"timeout" mapstructure:"timeout"`
}

// LoggingConfig 表示日志配置
type LoggingConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	Format     string `yaml:"format" mapstructure:"format"` // "text", "json" （文本、JSON）
	Output     string `yaml:"output" mapstructure:"output"` // "stdout", "stderr", "file" （标准输出、标准错误、文件）
	FilePath   string `yaml:"file_path" mapstructure:"file_path"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`       // MB （兆字节）
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"` // 备份文件数量
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`         // 天数
	Compress   bool   `yaml:"compress" mapstructure:"compress"`
}

// Manager 处理配置加载和管理
type Manager struct {
	config *Config
	viper  *viper.Viper
	logger *logrus.Logger
}

// NewManager 创建一个新的配置管理器
func NewManager(logger *logrus.Logger) *Manager {
	if logger == nil {
		logger = logrus.New()
	}

	return &Manager{
		viper:  viper.New(),
		logger: logger,
	}
}

// Load 从文件加载配置
func (m *Manager) Load(configPath string) error {
	// 设置默认值
	m.setDefaults()

	// 配置 Viper
	m.viper.SetConfigFile(configPath)
	m.viper.SetConfigType("yaml")

	// 启用环境变量支持
	m.viper.SetEnvPrefix("IPERF3")
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	m.viper.AutomaticEnv()

	// 读取配置文件
	if err := m.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			m.logger.WithField("config_path", configPath).Warn("Config file not found, using defaults")
		} else {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	} else {
		m.logger.WithField("config_path", configPath).Info("Configuration loaded successfully")
	}

	// 反序列化配置
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	m.config = config

	// 验证配置
	if err := m.Validate(); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	return nil
}

// setDefaults 设置默认配置值
func (m *Manager) setDefaults() {
	// 服务器默认值
	m.viper.SetDefault("server.listen_port", 55201)
	m.viper.SetDefault("server.web_port", 6066)

	// 调度默认值
	m.viper.SetDefault("schedule.mode", "odd")
	m.viper.SetDefault("schedule.timezone", "Asia/Shanghai")

	// 对等默认值
	m.viper.SetDefault("peer.port", 55201)
	m.viper.SetDefault("peer.sync_interval", "5m")

	// 数据库默认值
	m.viper.SetDefault("database.path", "/opt/iperf3-controller/data.db")
	m.viper.SetDefault("database.backup_interval", "24h")
	m.viper.SetDefault("database.max_size_mb", 1024)
	m.viper.SetDefault("database.retention_days", 30)
	m.viper.SetDefault("database.max_open_conns", 10)
	m.viper.SetDefault("database.max_idle_conns", 5)
	m.viper.SetDefault("database.conn_max_lifetime", "1h")
	m.viper.SetDefault("database.conn_max_idle_time", "15m")
	m.viper.SetDefault("database.busy_timeout", "30s")
	m.viper.SetDefault("database.journal_mode", "WAL")
	m.viper.SetDefault("database.synchronous", "NORMAL")
	m.viper.SetDefault("database.cache_size", -64000)

	// 性能默认值
	m.viper.SetDefault("performance.tcp.parallel_streams", 4)
	m.viper.SetDefault("performance.tcp.window_size", "512K")
	m.viper.SetDefault("performance.tcp.mss", 1460)
	m.viper.SetDefault("performance.tcp.duration", "30s")
	m.viper.SetDefault("performance.udp.bandwidth", "500M")
	m.viper.SetDefault("performance.udp.packet_size", 1472)
	m.viper.SetDefault("performance.udp.duration", "10s")
	m.viper.SetDefault("performance.concurrency.max_workers", 4)
	m.viper.SetDefault("performance.concurrency.batch_size", 3)
	m.viper.SetDefault("performance.concurrency.batch_delay", "2s")

	// 客户端管理默认值
	m.viper.SetDefault("client_management.prepare_timeout", "10s")
	m.viper.SetDefault("client_management.test_timeout", "60s")
	m.viper.SetDefault("client_management.stop_timeout", "5s")
	m.viper.SetDefault("client_management.retry_attempts", 3)
	m.viper.SetDefault("client_management.retry_delay", "1s")

	// 同步默认值
	m.viper.SetDefault("sync.enabled", true)
	m.viper.SetDefault("sync.interval", "5m")
	m.viper.SetDefault("sync.timeout", "30s")

	// 日志默认值
	m.viper.SetDefault("logging.level", "info")
	m.viper.SetDefault("logging.format", "text")
	m.viper.SetDefault("logging.output", "stdout")
	m.viper.SetDefault("logging.max_size", 100)
	m.viper.SetDefault("logging.max_backups", 3)
	m.viper.SetDefault("logging.max_age", 28)
	m.viper.SetDefault("logging.compress", true)
}

// GetConfig 返回加载的配置
func (m *Manager) GetConfig() *Config {
	return m.config
}
