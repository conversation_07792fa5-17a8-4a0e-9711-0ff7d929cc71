package sync

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// syncRoutine 同步协程
func (sm *DefaultSyncManager) syncRoutine(ctx context.Context) {
	sm.logger.Info("启动数据同步协程")
	
	for {
		select {
		case <-ctx.Done():
			sm.logger.Info("数据同步协程因上下文取消而停止")
			return
		case <-sm.stopChan:
			sm.logger.Info("数据同步协程停止")
			return
		case <-sm.syncTicker.C:
			// 执行定时同步
			if err := sm.performSync(ctx); err != nil {
				sm.logger.WithError(err).Error("定时同步失败")
			}
		}
	}
}

// heartbeatRoutine 心跳协程
func (sm *DefaultSyncManager) heartbeatRoutine(ctx context.Context) {
	sm.logger.Info("启动心跳协程")
	
	for {
		select {
		case <-ctx.Done():
			sm.logger.Info("心跳协程因上下文取消而停止")
			return
		case <-sm.stopChan:
			sm.logger.Info("心跳协程停止")
			return
		case <-sm.heartbeatTicker.C:
			// 执行心跳检测
			if err := sm.performHeartbeat(ctx); err != nil {
				sm.logger.WithError(err).Warn("心跳检测失败")
			}
		}
	}
}

// performSync 执行数据同步
func (sm *DefaultSyncManager) performSync(ctx context.Context) error {
	startTime := time.Now()
	
	sm.logger.WithFields(logrus.Fields{
		"peer_host": sm.config.PeerHost,
		"peer_port": sm.config.PeerPort,
	}).Info("开始数据同步")
	
	// 创建带超时的上下文
	syncCtx, cancel := context.WithTimeout(ctx, sm.config.SyncTimeout)
	defer cancel()
	
	// 更新统计信息
	sm.mu.Lock()
	sm.totalSyncs++
	sm.lastSyncTime = startTime
	sm.mu.Unlock()
	
	// 执行同步步骤
	err := sm.executeSyncSteps(syncCtx)
	
	duration := time.Since(startTime)
	
	sm.mu.Lock()
	if err != nil {
		sm.failedSyncs++
		sm.lastError = err
		sm.logger.WithFields(logrus.Fields{
			"error":    err.Error(),
			"duration": duration,
		}).Error("数据同步失败")
	} else {
		sm.successfulSyncs++
		sm.lastError = nil
		sm.logger.WithFields(logrus.Fields{
			"duration": duration,
		}).Info("数据同步成功")
	}
	sm.mu.Unlock()
	
	return err
}

// executeSyncSteps 执行同步步骤
func (sm *DefaultSyncManager) executeSyncSteps(ctx context.Context) error {
	// 步骤1：检查对端连接
	if err := sm.checkPeerConnection(ctx); err != nil {
		return fmt.Errorf("检查对端连接失败: %w", err)
	}
	
	// 步骤2：获取对端数据版本
	peerVersion, err := sm.getPeerDataVersion(ctx)
	if err != nil {
		return fmt.Errorf("获取对端数据版本失败: %w", err)
	}
	
	// 步骤3：获取本地数据版本
	localVersion, err := sm.getLocalDataVersion(ctx)
	if err != nil {
		return fmt.Errorf("获取本地数据版本失败: %w", err)
	}
	
	// 步骤4：比较版本并决定同步方向
	syncDirection := sm.determineSyncDirection(localVersion, peerVersion)
	
	sm.logger.WithFields(logrus.Fields{
		"local_version": localVersion,
		"peer_version":  peerVersion,
		"sync_direction": syncDirection,
	}).Info("确定同步方向")
	
	// 步骤5：执行数据同步
	switch syncDirection {
	case SyncDirectionToPeer:
		return sm.syncDataToPeer(ctx)
	case SyncDirectionFromPeer:
		return sm.syncDataFromPeer(ctx)
	case SyncDirectionBidirectional:
		// 双向同步：先推送本地变更，再拉取对端变更
		if err := sm.syncDataToPeer(ctx); err != nil {
			return fmt.Errorf("推送数据到对端失败: %w", err)
		}
		return sm.syncDataFromPeer(ctx)
	case SyncDirectionNone:
		sm.logger.Debug("数据已同步，无需同步")
		return nil
	default:
		return fmt.Errorf("未知的同步方向: %s", syncDirection)
	}
}

// SyncDirection 同步方向
type SyncDirection string

const (
	SyncDirectionNone          SyncDirection = "none"          // 无需同步
	SyncDirectionToPeer        SyncDirection = "to_peer"       // 同步到对端
	SyncDirectionFromPeer      SyncDirection = "from_peer"     // 从对端同步
	SyncDirectionBidirectional SyncDirection = "bidirectional" // 双向同步
)

// determineSyncDirection 确定同步方向
func (sm *DefaultSyncManager) determineSyncDirection(localVersion, peerVersion int64) SyncDirection {
	if localVersion == peerVersion {
		return SyncDirectionNone
	}
	
	if localVersion > peerVersion {
		return SyncDirectionToPeer
	}
	
	if peerVersion > localVersion {
		return SyncDirectionFromPeer
	}
	
	// 如果版本号相同但数据不同，执行双向同步
	return SyncDirectionBidirectional
}

// performHeartbeat 执行心跳检测
func (sm *DefaultSyncManager) performHeartbeat(ctx context.Context) error {
	sm.logger.Debug("执行心跳检测")
	
	// 创建带超时的上下文
	heartbeatCtx, cancel := context.WithTimeout(ctx, sm.config.ConnectTimeout)
	defer cancel()
	
	// 检查对端连接
	err := sm.checkPeerConnection(heartbeatCtx)
	
	sm.mu.Lock()
	if err != nil {
		sm.peerStatus.Connected = false
		sm.logger.WithError(err).Warn("心跳检测失败，对端连接断开")
	} else {
		sm.peerStatus.Connected = true
		sm.peerStatus.LastHeartbeat = time.Now()
		sm.logger.Debug("心跳检测成功")
	}
	sm.mu.Unlock()
	
	return err
}

// checkPeerConnection 检查对端连接
func (sm *DefaultSyncManager) checkPeerConnection(ctx context.Context) error {
	// 这里实现对端连接检查逻辑
	// 例如：发送HTTP请求到对端的健康检查端点
	sm.logger.WithFields(logrus.Fields{
		"peer_host": sm.config.PeerHost,
		"peer_port": sm.config.PeerPort,
	}).Debug("检查对端连接")
	
	// 模拟连接检查（实际实现时会发送真实的网络请求）
	// 这里简单返回成功，实际部署时需要实现真实的连接检查
	return nil
}

// getPeerDataVersion 获取对端数据版本
func (sm *DefaultSyncManager) getPeerDataVersion(ctx context.Context) (int64, error) {
	// 这里实现获取对端数据版本的逻辑
	// 例如：发送HTTP请求到对端的版本查询端点
	sm.logger.Debug("获取对端数据版本")
	
	// 模拟版本获取（实际实现时会发送真实的网络请求）
	// 这里返回一个模拟版本号
	return time.Now().Unix(), nil
}

// getLocalDataVersion 获取本地数据版本
func (sm *DefaultSyncManager) getLocalDataVersion(ctx context.Context) (int64, error) {
	// 这里实现获取本地数据版本的逻辑
	// 例如：查询数据库中的版本表
	sm.logger.Debug("获取本地数据版本")
	
	// 模拟版本获取（实际实现时会查询数据库）
	// 这里返回一个模拟版本号
	return time.Now().Unix(), nil
}

// syncDataToPeer 同步数据到对端
func (sm *DefaultSyncManager) syncDataToPeer(ctx context.Context) error {
	sm.logger.Info("开始同步数据到对端")
	
	// 这里实现数据推送逻辑
	// 例如：查询本地变更数据，打包发送到对端
	
	// 模拟数据同步过程
	time.Sleep(100 * time.Millisecond)
	
	sm.logger.Info("数据同步到对端完成")
	return nil
}

// syncDataFromPeer 从对端同步数据
func (sm *DefaultSyncManager) syncDataFromPeer(ctx context.Context) error {
	sm.logger.Info("开始从对端同步数据")
	
	// 这里实现数据拉取逻辑
	// 例如：请求对端变更数据，解析并应用到本地数据库
	
	// 模拟数据同步过程
	time.Sleep(100 * time.Millisecond)
	
	sm.logger.Info("从对端同步数据完成")
	return nil
}

// notifyChangeListeners 通知变更监听器
func (sm *DefaultSyncManager) notifyChangeListeners(changeType ChangeType, tableName string, recordID string) {
	sm.mu.RLock()
	listeners := make([]ChangeListener, len(sm.changeListeners))
	copy(listeners, sm.changeListeners)
	sm.mu.RUnlock()
	
	for _, listener := range listeners {
		go func(l ChangeListener) {
			defer func() {
				if r := recover(); r != nil {
					sm.logger.WithField("panic", r).Error("变更监听器发生panic")
				}
			}()
			l.OnDataChanged(changeType, tableName, recordID)
		}(listener)
	}
}
