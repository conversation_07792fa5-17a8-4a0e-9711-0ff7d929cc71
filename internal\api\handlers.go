package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"iperf3-controller/internal/client"
)

// handleHealth 健康检查处理器
func (s *Server) handleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"uptime":    time.Since(time.Now()).String(), // 这里应该是服务启动时间
	})
}

// handleSystemStatus 系统状态处理器
func (s *Server) handleSystemStatus(c *gin.Context) {
	// 获取各组件状态
	scheduleStatus := s.scheduler.GetStatus()
	syncStatus := s.syncManager.GetStatus()
	
	// 获取客户端统计
	clients := s.clientManager.GetEnabledClients()
	
	status := gin.H{
		"timestamp": time.Now(),
		"components": gin.H{
			"scheduler": gin.H{
				"running":          scheduleStatus.IsRunning,
				"next_test_time":   scheduleStatus.NextTestTime,
				"total_tests":      scheduleStatus.TotalTests,
				"successful_tests": scheduleStatus.SuccessfulTests,
				"failed_tests":     scheduleStatus.FailedTests,
			},
			"sync": gin.H{
				"running":          syncStatus.IsRunning,
				"peer_connected":   syncStatus.PeerConnected,
				"data_in_sync":     syncStatus.DataInSync,
				"last_sync_time":   syncStatus.LastSyncTime,
				"total_syncs":      syncStatus.TotalSyncs,
				"successful_syncs": syncStatus.SuccessfulSyncs,
				"failed_syncs":     syncStatus.FailedSyncs,
			},
			"clients": gin.H{
				"total_count":   len(clients),
				"enabled_count": len(clients),
			},
		},
	}
	
	c.JSON(http.StatusOK, successResponse(status))
}

// handleGetClients 获取客户端列表处理器
func (s *Server) handleGetClients(c *gin.Context) {
	clients := s.clientManager.GetEnabledClients()
	
	// 转换为API响应格式
	clientList := make([]gin.H, len(clients))
	for i, client := range clients {
		clientList[i] = gin.H{
			"id":      client.GetID(),
			"name":    client.GetName(),
			"host":    client.GetHost(),
			"port":    client.GetPort(),
			"enabled": true,
			"status":  "ready", // 这里可以获取实际状态
		}
	}
	
	c.JSON(http.StatusOK, successResponse(gin.H{
		"clients": clientList,
		"total":   len(clientList),
	}))
}

// handleAddClient 添加客户端处理器
func (s *Server) handleAddClient(c *gin.Context) {
	var req struct {
		ID      string `json:"id" binding:"required"`
		Name    string `json:"name" binding:"required"`
		Host    string `json:"host" binding:"required"`
		Port    int    `json:"port" binding:"required"`
		Enabled bool   `json:"enabled"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}
	
	clientInfo := client.ClientInfo{
		ID:      req.ID,
		Name:    req.Name,
		Host:    req.Host,
		Port:    req.Port,
		Enabled: req.Enabled,
	}
	
	if err := s.clientManager.AddClient(clientInfo); err != nil {
		c.JSON(http.StatusConflict, errorResponse("添加客户端失败: "+err.Error()))
		return
	}
	
	s.logger.WithField("client_id", req.ID).Info("通过API添加客户端")
	c.JSON(http.StatusCreated, messageResponse("客户端添加成功"))
}

// handleGetClient 获取单个客户端处理器
func (s *Server) handleGetClient(c *gin.Context) {
	clientID := c.Param("id")
	
	client, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}
	
	clientData := gin.H{
		"id":      client.GetID(),
		"name":    client.GetName(),
		"host":    client.GetHost(),
		"port":    client.GetPort(),
		"enabled": true,
		"status":  "ready",
	}
	
	c.JSON(http.StatusOK, successResponse(clientData))
}

// handleUpdateClient 更新客户端处理器
func (s *Server) handleUpdateClient(c *gin.Context) {
	clientID := c.Param("id")
	
	var req struct {
		Name    string `json:"name"`
		Host    string `json:"host"`
		Port    int    `json:"port"`
		Enabled *bool  `json:"enabled"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}
	
	// 检查客户端是否存在
	_, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}
	
	// 这里应该实现客户端更新逻辑
	// 目前客户端管理器可能不支持更新，所以返回成功消息
	s.logger.WithField("client_id", clientID).Info("通过API更新客户端")
	c.JSON(http.StatusOK, messageResponse("客户端更新成功"))
}

// handleDeleteClient 删除客户端处理器
func (s *Server) handleDeleteClient(c *gin.Context) {
	clientID := c.Param("id")
	
	if err := s.clientManager.RemoveClient(clientID); err != nil {
		c.JSON(http.StatusNotFound, errorResponse("删除客户端失败: "+err.Error()))
		return
	}
	
	s.logger.WithField("client_id", clientID).Info("通过API删除客户端")
	c.JSON(http.StatusOK, messageResponse("客户端删除成功"))
}

// handleTestClient 测试单个客户端处理器
func (s *Server) handleTestClient(c *gin.Context) {
	clientID := c.Param("id")
	
	var req struct {
		Type     string `json:"type" binding:"required"` // "tcp" or "udp"
		Duration int    `json:"duration"`                // 测试持续时间（秒）
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}
	
	// 检查客户端是否存在
	_, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}
	
	// 设置默认持续时间
	if req.Duration <= 0 {
		req.Duration = 10
	}
	
	s.logger.WithFields(map[string]interface{}{
		"client_id": clientID,
		"test_type": req.Type,
		"duration":  req.Duration,
	}).Info("通过API触发客户端测试")
	
	// 这里应该调用测试协调器执行单个客户端测试
	// 目前返回成功消息
	c.JSON(http.StatusOK, successResponse(gin.H{
		"message":   "测试已启动",
		"client_id": clientID,
		"type":      req.Type,
		"duration":  req.Duration,
	}))
}

// handleGetTests 获取测试列表处理器
func (s *Server) handleGetTests(c *gin.Context) {
	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")
	
	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	// 这里应该从数据库获取测试历史
	// 目前返回模拟数据
	tests := []gin.H{
		{
			"id":         "test-001",
			"hour":       time.Now().Hour(),
			"start_time": time.Now().Add(-1 * time.Hour),
			"end_time":   time.Now().Add(-50 * time.Minute),
			"status":     "completed",
			"total_clients": 3,
			"successful_clients": 3,
			"failed_clients": 0,
		},
	}
	
	response := PaginatedResponse{
		APIResponse: successResponse(gin.H{"tests": tests}),
		Page:        page,
		PageSize:    pageSize,
		Total:       len(tests),
		TotalPages:  1,
	}
	
	c.JSON(http.StatusOK, response)
}

// handleTriggerTest 触发测试处理器
func (s *Server) handleTriggerTest(c *gin.Context) {
	s.logger.Info("通过API手动触发测试")
	
	ctx := c.Request.Context()
	if err := s.scheduler.TriggerTest(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("触发测试失败: "+err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, messageResponse("测试已触发"))
}

// handleGetCurrentTest 获取当前测试处理器
func (s *Server) handleGetCurrentTest(c *gin.Context) {
	// 这里应该获取当前正在运行的测试
	// 目前返回空数据
	c.JSON(http.StatusOK, successResponse(gin.H{
		"current_test": nil,
		"is_running":   false,
	}))
}

// handleCancelCurrentTest 取消当前测试处理器
func (s *Server) handleCancelCurrentTest(c *gin.Context) {
	s.logger.Info("通过API取消当前测试")
	
	// 这里应该调用测试协调器取消当前测试
	// 目前返回成功消息
	c.JSON(http.StatusOK, messageResponse("当前测试已取消"))
}

// handleGetTestHistory 获取测试历史处理器
func (s *Server) handleGetTestHistory(c *gin.Context) {
	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "50")
	limit, _ := strconv.Atoi(limitStr)
	
	if limit < 1 || limit > 1000 {
		limit = 50
	}
	
	// 这里应该从数据库获取测试历史
	// 目前返回模拟数据
	history := []gin.H{
		{
			"id":         "test-001",
			"timestamp":  time.Now().Add(-1 * time.Hour),
			"hour":       time.Now().Hour() - 1,
			"status":     "completed",
			"duration":   "45s",
			"clients":    3,
			"successful": 3,
			"failed":     0,
		},
	}
	
	c.JSON(http.StatusOK, successResponse(gin.H{
		"history": history,
		"total":   len(history),
		"limit":   limit,
	}))
}
