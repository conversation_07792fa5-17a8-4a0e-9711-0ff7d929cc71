# 📚 iperf3-controller 详细使用教程

## 🎯 系统架构说明

### 服务端 vs 客户端关系

**重要说明**: iperf3-controller 是一个**统一的二进制文件**，但可以运行在不同的模式下：

```
┌─────────────────────────────────────────────────────────────┐
│                    iperf3-controller                        │
│                    (同一个二进制文件)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┴─────────────┐
        │                           │
    🖥️ 服务端模式                🔧 客户端模式
   (OpenWRT设备)              (测试服务器)
        │                           │
   ┌────▼────┐                 ┌────▼────┐
   │ mode:   │                 │ mode:   │
   │ server  │                 │ client  │
   └─────────┘                 └─────────┘
```

### 系统组件说明

#### 1. 控制器 (Controller) - OpenWRT设备
- **作用**: 管理和协调整个测试系统
- **运行模式**: `mode: server`
- **功能**: 
  - 管理12台测试服务器
  - 执行抢占式测试调度
  - 提供Web管理界面
  - 数据存储和同步

#### 2. 测试服务器 (Test Servers) - 12台服务器
- **作用**: 执行实际的iperf3测试
- **运行模式**: `mode: client` 
- **功能**:
  - 运行iperf3客户端
  - 向OpenWRT发起测试连接
  - 参与抢占式测试竞争

## 🚀 快速开始

### 第一步：下载和构建

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/iperf3-controller.git
cd iperf3-controller

# 2. 构建所有平台版本
./scripts/build.sh

# 构建完成后，你会得到：
# build/iperf3-controller-linux-amd64      # Linux服务器用
# build/iperf3-controller-openwrt-mips     # OpenWRT用
# build/iperf3-controller-windows-amd64.exe # Windows服务器用
```

### 第二步：部署到OpenWRT (控制器)

#### 方法1：自动部署
```bash
# 上传到OpenWRT并自动部署
scp scripts/deploy.sh root@***********:/tmp/
scp build/iperf3-controller-openwrt-mips root@***********:/tmp/
ssh root@*********** "cd /tmp && chmod +x deploy.sh && ./deploy.sh"
```

#### 方法2：手动部署
```bash
# 1. 上传文件到OpenWRT
scp build/iperf3-controller-openwrt-mips root@***********:/opt/
scp config.example.yaml root@***********:/etc/iperf3-controller/config.yaml
scp -r web/static root@***********:/opt/iperf3-controller/

# 2. SSH到OpenWRT
ssh root@***********

# 3. 设置权限
chmod +x /opt/iperf3-controller-openwrt-mips

# 4. 安装依赖
opkg update
opkg install iperf3 sqlite3-cli

# 5. 创建服务
cat > /etc/init.d/iperf3-controller << 'EOF'
#!/bin/sh /etc/rc.common
START=99
STOP=10
USE_PROCD=1
PROG="/opt/iperf3-controller-openwrt-mips"
CONF="/etc/iperf3-controller/config.yaml"

start_service() {
    procd_open_instance
    procd_set_param command $PROG -config $CONF
    procd_set_param respawn
    procd_set_param stdout 1
    procd_set_param stderr 1
    procd_close_instance
}
EOF

chmod +x /etc/init.d/iperf3-controller
/etc/init.d/iperf3-controller enable
```

### 第三步：配置OpenWRT控制器

编辑配置文件 `/etc/iperf3-controller/config.yaml`：

```yaml
# 服务器配置 - 控制器模式
server:
  host: "0.0.0.0"
  port: 8080
  mode: "server"  # 重要：设置为server模式

# 调度配置
schedule:
  mode: "odd"  # odd=奇数小时测试, even=偶数小时测试
  timezone: "Asia/Shanghai"

# 同步配置（如果有第二台OpenWRT）
sync:
  peer_host: "*************"  # 另一台OpenWRT的IP
  peer_port: 8080
  sync_interval: "30s"

# 客户端列表 - 配置你的12台测试服务器
clients:
  - id: "server-01"
    name: "测试服务器01"
    host: "***********01"  # 服务器1的IP
    port: 55201
    enabled: true
    
  - id: "server-02"
    name: "测试服务器02"
    host: "***********02"  # 服务器2的IP
    port: 55202
    enabled: true
    
  # ... 继续添加其他10台服务器
  - id: "server-12"
    name: "测试服务器12"
    host: "*************"
    port: 55212
    enabled: true
```

### 第四步：部署到测试服务器 (客户端)

在每台测试服务器上：

```bash
# 1. 上传对应平台的二进制文件
# Linux服务器：
scp build/iperf3-controller-linux-amd64 user@***********01:/opt/iperf3-controller

# Windows服务器：
# 复制 build/iperf3-controller-windows-amd64.exe 到服务器

# 2. 创建配置文件
cat > /etc/iperf3-controller/config.yaml << 'EOF'
# 服务器配置 - 客户端模式
server:
  host: "0.0.0.0"
  port: 55201  # 每台服务器使用不同端口
  mode: "client"  # 重要：设置为client模式

# 控制器地址
controller:
  host: "***********"  # OpenWRT控制器的IP
  port: 8080

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"  # iperf3可执行文件路径
  default_duration: 30
EOF

# 3. 安装iperf3
# Ubuntu/Debian:
sudo apt-get install iperf3

# CentOS/RHEL:
sudo yum install iperf3

# 4. 启动服务
sudo /opt/iperf3-controller -config /etc/iperf3-controller/config.yaml
```

### 第五步：启动系统

#### 1. 启动OpenWRT控制器
```bash
# 在OpenWRT上
/etc/init.d/iperf3-controller start

# 检查状态
/etc/init.d/iperf3-controller status
```

#### 2. 启动所有测试服务器
```bash
# 在每台测试服务器上
sudo systemctl start iperf3-controller
sudo systemctl enable iperf3-controller
```

#### 3. 验证连接
```bash
# 检查OpenWRT控制器日志
logread | grep iperf3-controller

# 检查Web界面
curl http://***********:8080/health
```

## 🎮 使用Web界面

### 访问管理界面

打开浏览器访问：`http://***********:8080`

### 主要功能

#### 1. 系统状态监控
- **调度器状态**: 显示是否正在运行
- **同步状态**: 显示双机同步状态
- **客户端状态**: 显示12台服务器连接状态
- **测试统计**: 显示成功率和测试次数

#### 2. 控制面板
- **启动调度器**: 开始自动测试
- **停止调度器**: 停止自动测试
- **手动测试**: 立即触发一次测试
- **启动同步**: 开始数据同步
- **停止同步**: 停止数据同步

#### 3. 实时数据
- **性能指标**: TCP/UDP速度、延迟、丢包率
- **客户端列表**: 12台服务器的详细状态
- **测试历史**: 历史测试结果

## ⚡ 抢占式测试原理

### 工作流程

```
1. 调度器触发测试 (奇数或偶数小时)
   ↓
2. 同时向12台服务器发送测试请求
   ↓
3. 所有服务器开始"抢占"OpenWRT的iperf3服务端
   ↓
4. 谁先连接成功，谁先开始测试
   ↓
5. 测试完成后，下一台服务器继续抢占
   ↓
6. 直到所有12台服务器都完成测试
```

### 优势
- **效率最大化**: 无需等待慢速服务器
- **公平竞争**: 所有服务器机会均等
- **自动容错**: 故障服务器不影响其他服务器
- **实时监控**: Web界面实时显示进度

## 🔧 常见问题解决

### Q1: 服务器连接失败
```bash
# 检查网络连通性
ping ***********01

# 检查端口是否开放
telnet ***********01 55201

# 检查iperf3是否安装
iperf3 --version
```

### Q2: Web界面无法访问
```bash
# 检查OpenWRT防火墙
iptables -L | grep 8080

# 添加防火墙规则
iptables -I INPUT -p tcp --dport 8080 -j ACCEPT
```

### Q3: 测试失败
```bash
# 检查iperf3服务
ps aux | grep iperf3

# 手动测试连接
iperf3 -c *********** -p 5201 -t 10
```

### Q4: 同步失败
```bash
# 检查对端连接
curl http://*************:8080/health

# 检查同步日志
logread | grep sync
```

## 📊 监控和维护

### 日志查看
```bash
# OpenWRT日志
logread | grep iperf3-controller

# Linux服务器日志
journalctl -u iperf3-controller -f

# 查看特定错误
grep ERROR /var/log/iperf3-controller.log
```

### 性能监控
```bash
# 检查资源使用
top | grep iperf3-controller

# 检查网络连接
netstat -an | grep 8080
```

### 数据备份
```bash
# 备份数据库
cp /var/lib/iperf3-controller/iperf3.db /backup/

# 备份配置
cp /etc/iperf3-controller/config.yaml /backup/
```

## 🎯 最佳实践

### 1. 网络配置
- 确保所有设备在同一网段
- 配置固定IP地址
- 开放必要的防火墙端口

### 2. 时间同步
- 所有设备使用NTP同步时间
- 确保时区设置正确

### 3. 监控告警
- 定期检查测试成功率
- 监控系统资源使用
- 设置故障告警

### 4. 定期维护
- 定期清理历史数据
- 更新iperf3版本
- 检查磁盘空间

---

## 🎉 开始使用

现在您已经了解了完整的部署和使用流程！

1. **部署控制器**: 在OpenWRT上运行 `mode: server`
2. **部署客户端**: 在12台服务器上运行 `mode: client`
3. **配置网络**: 确保所有设备互通
4. **启动系统**: 开始享受抢占式网络测试！

访问 `http://your-openwrt-ip:8080` 开始管理您的网络性能测试系统！🚀

## 📋 配置文件详解

### OpenWRT控制器完整配置示例

```yaml
# /etc/iperf3-controller/config.yaml (OpenWRT控制器)

# 服务器配置
server:
  host: "0.0.0.0"          # 监听所有接口
  port: 8080               # Web界面端口
  mode: "server"           # 控制器模式
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

# 客户端管理配置
client_management:
  prepare_timeout: "10s"    # 客户端准备超时
  test_timeout: "60s"       # 测试执行超时
  stop_timeout: "5s"        # 停止超时
  retry_attempts: 3         # 重试次数
  retry_delay: "1s"         # 重试延迟

# 调度配置
schedule:
  mode: "odd"              # odd=奇数小时, even=偶数小时, always=每小时
  timezone: "Asia/Shanghai" # 时区设置

# 数据同步配置（双OpenWRT）
sync:
  peer_host: "*************"    # 对端OpenWRT IP
  peer_port: 8080               # 对端端口
  sync_interval: "30s"          # 同步间隔
  heartbeat_interval: "10s"     # 心跳间隔
  connect_timeout: "5s"         # 连接超时
  sync_timeout: "30s"           # 同步超时
  retry_attempts: 3             # 重试次数
  retry_delay: "2s"             # 重试延迟
  enable_compression: true      # 启用压缩
  enable_encryption: false      # 启用加密

# 数据库配置
database:
  type: "sqlite"
  path: "/var/lib/iperf3-controller/iperf3.db"
  max_connections: 10
  connection_timeout: "5s"

# 日志配置
logging:
  level: "info"                 # debug, info, warn, error
  file: "/var/log/iperf3-controller/iperf3-controller.log"
  max_size: "100MB"
  max_backups: 5
  max_age: 30
  compress: true

# Web界面配置
web:
  static_dir: "/opt/iperf3-controller/web"
  enable_cors: true
  enable_auth: false            # 生产环境建议启用
  api_key: "your-secret-key"    # 启用认证时使用

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"
  default_duration: 30          # 默认测试时长（秒）
  tcp:
    parallel_streams: 4         # 并行流数量
    window_size: "512K"         # TCP窗口大小
    mss: 1460                   # 最大段大小
  udp:
    bandwidth: "500M"           # 目标带宽
    packet_size: 1472           # UDP包大小

# 客户端列表 - 12台测试服务器
clients:
  - id: "server-01"
    name: "主服务器01"
    host: "***********01"
    port: 55201
    enabled: true
    description: "主要测试服务器"

  - id: "server-02"
    name: "主服务器02"
    host: "***********02"
    port: 55202
    enabled: true
    description: "备用测试服务器"

  - id: "server-03"
    name: "边缘服务器01"
    host: "***********03"
    port: 55203
    enabled: true
    description: "边缘节点测试"

  - id: "server-04"
    name: "边缘服务器02"
    host: "***********04"
    port: 55204
    enabled: true

  - id: "server-05"
    name: "云服务器01"
    host: "***********05"
    port: 55205
    enabled: true

  - id: "server-06"
    name: "云服务器02"
    host: "***********06"
    port: 55206
    enabled: true

  - id: "server-07"
    name: "测试服务器07"
    host: "***********07"
    port: 55207
    enabled: true

  - id: "server-08"
    name: "测试服务器08"
    host: "***********08"
    port: 55208
    enabled: true

  - id: "server-09"
    name: "测试服务器09"
    host: "***********09"
    port: 55209
    enabled: true

  - id: "server-10"
    name: "测试服务器10"
    host: "***********10"
    port: 55210
    enabled: true

  - id: "server-11"
    name: "测试服务器11"
    host: "***********11"
    port: 55211
    enabled: true

  - id: "server-12"
    name: "测试服务器12"
    host: "*************"
    port: 55212
    enabled: true

# 测试配置
testing:
  batch_size: 3                 # 同时测试的客户端数量
  batch_delay: "2s"             # 批次间延迟
  enable_preemptive: true       # 启用抢占式测试
  result_retention_days: 30     # 测试结果保留天数

# 监控配置
monitoring:
  enable_performance_monitoring: true
  collection_interval: "1m"
  enable_health_check: true
  health_check_interval: "30s"

# 安全配置
security:
  enable_https: false           # 生产环境建议启用
  cert_file: ""
  key_file: ""
  allowed_ips:                  # 允许访问的IP范围
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
```

### 测试服务器配置示例

```yaml
# /etc/iperf3-controller/config.yaml (测试服务器)

# 服务器配置
server:
  host: "0.0.0.0"
  port: 55201              # 每台服务器使用不同端口
  mode: "client"           # 客户端模式
  read_timeout: "30s"
  write_timeout: "30s"

# 控制器连接配置
controller:
  host: "***********"      # OpenWRT控制器IP
  port: 8080               # 控制器端口
  connect_timeout: "10s"
  heartbeat_interval: "30s"
  retry_attempts: 5
  retry_delay: "5s"

# 客户端标识
client:
  id: "server-01"          # 与控制器配置中的ID对应
  name: "主服务器01"
  description: "主要测试服务器"

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"
  server_port: 5201        # iperf3服务端口（连接到OpenWRT）
  default_duration: 30
  tcp:
    parallel_streams: 4
    window_size: "512K"
  udp:
    bandwidth: "500M"
    packet_size: 1472

# 日志配置
logging:
  level: "info"
  file: "/var/log/iperf3-controller/client.log"
  max_size: "50MB"
  max_backups: 3
  max_age: 7

# 性能配置
performance:
  max_concurrent_tests: 1   # 同时进行的测试数量
  test_queue_size: 10       # 测试队列大小
  result_buffer_size: 100   # 结果缓冲区大小
```

## 🔧 系统服务配置

### OpenWRT系统服务

```bash
# /etc/init.d/iperf3-controller
#!/bin/sh /etc/rc.common

START=99
STOP=10
USE_PROCD=1

PROG="/opt/iperf3-controller"
CONF="/etc/iperf3-controller/config.yaml"
PIDFILE="/var/run/iperf3-controller.pid"

start_service() {
    procd_open_instance
    procd_set_param command $PROG -config $CONF
    procd_set_param pidfile $PIDFILE
    procd_set_param respawn ${respawn_threshold:-3600} ${respawn_timeout:-5} ${respawn_retry:-5}
    procd_set_param stdout 1
    procd_set_param stderr 1
    procd_set_param user root
    procd_close_instance
}

stop_service() {
    killall iperf3-controller
}

reload_service() {
    stop_service
    start_service
}

service_triggers() {
    procd_add_reload_trigger "iperf3-controller"
}
```

### Linux Systemd服务

```bash
# /etc/systemd/system/iperf3-controller.service
[Unit]
Description=iperf3 Controller Service
Documentation=https://github.com/your-repo/iperf3-controller
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/opt/iperf3-controller -config /etc/iperf3-controller/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=iperf3-controller

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/lib/iperf3-controller /var/log/iperf3-controller

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

## 🚀 快速部署脚本

### 一键部署到OpenWRT

```bash
#!/bin/bash
# deploy-openwrt.sh

OPENWRT_IP="***********"
OPENWRT_USER="root"

echo "🚀 开始部署到OpenWRT..."

# 1. 上传文件
echo "📁 上传文件..."
scp build/iperf3-controller-openwrt-mips ${OPENWRT_USER}@${OPENWRT_IP}:/opt/iperf3-controller
scp config.example.yaml ${OPENWRT_USER}@${OPENWRT_IP}:/etc/iperf3-controller/config.yaml
scp -r web/static ${OPENWRT_USER}@${OPENWRT_IP}:/opt/iperf3-controller/

# 2. 远程配置
echo "⚙️ 配置系统..."
ssh ${OPENWRT_USER}@${OPENWRT_IP} << 'EOF'
# 创建目录
mkdir -p /opt/iperf3-controller
mkdir -p /etc/iperf3-controller
mkdir -p /var/log/iperf3-controller
mkdir -p /var/lib/iperf3-controller

# 设置权限
chmod +x /opt/iperf3-controller
chown -R root:root /opt/iperf3-controller
chown -R root:root /etc/iperf3-controller

# 安装依赖
opkg update
opkg install iperf3 sqlite3-cli

# 创建服务
cat > /etc/init.d/iperf3-controller << 'EOFSERVICE'
#!/bin/sh /etc/rc.common
START=99
STOP=10
USE_PROCD=1
PROG="/opt/iperf3-controller"
CONF="/etc/iperf3-controller/config.yaml"

start_service() {
    procd_open_instance
    procd_set_param command $PROG -config $CONF
    procd_set_param respawn
    procd_set_param stdout 1
    procd_set_param stderr 1
    procd_close_instance
}
EOFSERVICE

chmod +x /etc/init.d/iperf3-controller
/etc/init.d/iperf3-controller enable

# 启动服务
/etc/init.d/iperf3-controller start

echo "✅ OpenWRT部署完成！"
echo "🌐 访问地址: http://${OPENWRT_IP}:8080"
EOF
```

### 批量部署到测试服务器

```bash
#!/bin/bash
# deploy-servers.sh

# 服务器列表
SERVERS=(
    "***********01:55201"
    "***********02:55202"
    "***********03:55203"
    "***********04:55204"
    "***********05:55205"
    "***********06:55206"
    "***********07:55207"
    "***********08:55208"
    "***********09:55209"
    "***********10:55210"
    "***********11:55211"
    "*************:55212"
)

CONTROLLER_IP="***********"
USER="root"

echo "🚀 开始批量部署到测试服务器..."

for server_config in "${SERVERS[@]}"; do
    IFS=':' read -r server_ip server_port <<< "$server_config"
    server_id="server-$(echo $server_ip | cut -d'.' -f4)"

    echo "📡 部署到服务器: $server_ip (端口: $server_port)"

    # 创建配置文件
    cat > temp_config.yaml << EOF
server:
  host: "0.0.0.0"
  port: $server_port
  mode: "client"

controller:
  host: "$CONTROLLER_IP"
  port: 8080

client:
  id: "$server_id"
  name: "测试服务器 $server_ip"

iperf3:
  binary_path: "/usr/bin/iperf3"
  default_duration: 30

logging:
  level: "info"
  file: "/var/log/iperf3-controller/client.log"
EOF

    # 部署到服务器
    scp build/iperf3-controller-linux-amd64 ${USER}@${server_ip}:/opt/iperf3-controller
    scp temp_config.yaml ${USER}@${server_ip}:/etc/iperf3-controller/config.yaml

    # 远程配置
    ssh ${USER}@${server_ip} << 'EOF'
mkdir -p /opt /etc/iperf3-controller /var/log/iperf3-controller
chmod +x /opt/iperf3-controller
apt-get update && apt-get install -y iperf3

# 创建systemd服务
cat > /etc/systemd/system/iperf3-controller.service << 'EOFSERVICE'
[Unit]
Description=iperf3 Controller Client
After=network.target

[Service]
Type=simple
ExecStart=/opt/iperf3-controller -config /etc/iperf3-controller/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOFSERVICE

systemctl daemon-reload
systemctl enable iperf3-controller
systemctl start iperf3-controller
EOF

    rm temp_config.yaml
    echo "✅ 服务器 $server_ip 部署完成"
done

echo "🎉 所有服务器部署完成！"
```

现在您有了完整的部署和使用指南！这个系统确实是同一个二进制文件，但通过配置文件中的 `mode` 参数来区分运行模式：
- `mode: server` = OpenWRT控制器
- `mode: client` = 测试服务器

这样设计的好处是维护简单，只需要一个程序文件就能满足所有需求！🚀
