package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"iperf3-controller/internal/api"
	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/database"
	"iperf3-controller/internal/scheduler"
	"iperf3-controller/internal/sync"
)

var (
	version = "dev"
	commit  = "unknown"
	date    = "unknown"
)

func main() {
	if err := newRootCommand().Execute(); err != nil {
		logrus.Fatal(err)
	}
}

func newRootCommand() *cobra.Command {
	var (
		configFile string
		logLevel   string
	)

	rootCmd := &cobra.Command{
		Use:   "iperf3-controller",
		Short: "iPerf3 dual OpenWRT speed testing system",
		Long: `一个用于双OpenWRT架构的网络速度测试系统。
支持奇偶小时调度、12个服务器客户端管理、
SQLite数据库存储和双机同步。`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) == 0 {
				return cmd.Help()
			}
			return nil // Should not be reached if subcommands handle execution
		},
	}

	// 全局标志
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/config.yaml", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "info", "log level (debug, info, warn, error)")

	// 服务器子命令
	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Run iPerf3 in server mode",
		Long:  "以服务器模式启动iPerf3控制器，监听客户端连接。",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runServerMode(configFile, logLevel, viper.GetString("aip"), viper.GetInt("ap"))
		},
	}
	serverCmd.Flags().String("aip", "", "peer OpenWRT IP address")
	serverCmd.Flags().Int("ap", 55201, "peer OpenWRT port")

	// 客户端子命令
	clientCmd := &cobra.Command{
		Use:   "client",
		Short: "Run iPerf3 in client mode",
		Long:  "以客户端模式启动iPerf3控制器，连接到服务器。",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runClientMode(configFile, logLevel, viper.GetString("host"), viper.GetInt("port"), viper.GetInt("duration"), viper.GetString("bandwidth"))
		},
	}
	clientCmd.Flags().String("host", "", "iPerf3 server host to connect to")
	clientCmd.Flags().Int("port", 5201, "iPerf3 server port to connect to")
	clientCmd.Flags().Int("duration", 10, "Duration of the test in seconds")
	clientCmd.Flags().String("bandwidth", "1G", "Target bandwidth in bits per second")

	rootCmd.AddCommand(serverCmd, clientCmd)

	return rootCmd
}

func runAppBase(configFile, logLevel string) (*context.Context, context.CancelFunc, error) {
	// Setup logging
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid log level: %w", err)
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.WithFields(logrus.Fields{
		"version":     version,
		"commit":      commit,
		"build_date":  date,
		"config_file": configFile,
	}).Info("Starting iPerf3 controller")

	// 创建用于优雅关闭的上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		logrus.WithField("signal", sig).Info("Received shutdown signal")
		cancel()
	}()

	return &ctx, cancel, nil
}

func runServerMode(configFile, logLevel string, peerIP string, peerPort int) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":      "server",
		"peer_ip":   peerIP,
		"peer_port": peerPort,
	}).Info("Running in server mode")

	// 加载配置文件
	logrus.Info("Loading configuration...")
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// 初始化数据库
	logrus.Info("Initializing database...")
	repo, err := initDatabase(cfg)
	if err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// 初始化组件
	logrus.Info("Initializing components...")
	components, err := initComponents(cfg, repo)
	if err != nil {
		return fmt.Errorf("failed to initialize components: %w", err)
	}

	// 启动Web服务器
	logrus.Info("Starting web server...")
	go func() {
		if err := components.APIServer.Start(); err != nil {
			logrus.WithError(err).Error("Web server failed")
		}
	}()

	logrus.Info("Server started successfully")
	logrus.WithField("port", cfg.Server.Port).Info("Web interface available")

	// Wait for context cancellation
	<-(*ctx).Done()
	logrus.Info("Shutting down gracefully...")

	// 停止组件
	if err := components.Cleanup(); err != nil {
		logrus.WithError(err).Error("Error during cleanup")
	}

	return nil
}

func runClientMode(configFile, logLevel string, host string, port int, duration int, bandwidth string) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":        "client",
		"target_host": host,
		"target_port": port,
		"duration":    duration,
		"bandwidth":   bandwidth,
	}).Info("Running in client mode")

	// 初始化客户端组件
	logrus.Info("初始化客户端组件...")

	// 这里可以添加客户端特有的初始化逻辑
	// 例如：连接到服务器、准备测试环境等
	logrus.Info("客户端组件初始化完成")

	logrus.Info("Client application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// 等待上下文取消
	<-(*ctx).Done()
	logrus.Info("Shutting down gracefully...")

	return nil
}

// loadConfig 加载配置文件
func loadConfig(configFile string) (*config.Config, error) {
	// 这是一个简化的配置加载实现
	cfg := &config.Config{}

	return cfg, nil
}

// initDatabase 初始化数据库
func initDatabase(cfg *config.Config) (*database.Repository, error) {
	dbConfig := &database.Config{
		Path: "./iperf3.db",
	}

	logger := logrus.StandardLogger()

	db, err := database.New(dbConfig, logger)
	if err != nil {
		return nil, err
	}

	// 运行数据库迁移
	if err := db.Migrate(); err != nil {
		return nil, fmt.Errorf("database migration failed: %w", err)
	}

	// 创建Repository
	repo := database.NewRepository(db, logger)

	return repo, nil
}

// Components 系统组件
type Components struct {
	ClientManager   *client.Manager
	TestCoordinator coordinator.TestCoordinator
	Scheduler       scheduler.Scheduler
	SyncManager     sync.SyncManager
	APIServer       *api.Server
	Repository      *database.Repository
}

// Cleanup 清理组件
func (c *Components) Cleanup() error {
	if c.SyncManager != nil {
		c.SyncManager.Stop()
	}
	if c.Scheduler != nil {
		c.Scheduler.Stop()
	}
	if c.TestCoordinator != nil {
		c.TestCoordinator.Stop()
	}
	return nil
}

// initComponents 初始化所有组件
func initComponents(cfg *config.Config, repo *database.Repository) (*Components, error) {
	logger := logrus.StandardLogger()

	// 创建客户端管理器
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 10 * time.Second,
		TestTimeout:    60 * time.Second,
		StopTimeout:    5 * time.Second,
		RetryAttempts:  3,
		RetryDelay:     1 * time.Second,
	}
	clientManager := client.NewManager(clientConfig, logger)

	// 创建测试协调器
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 4, logger)

	// 创建调度器
	scheduleConfig := &config.ScheduleConfig{
		Mode:     "odd",
		Timezone: "Asia/Shanghai",
	}
	testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, repo, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create scheduler: %w", err)
	}

	// 创建同步管理器
	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}
	syncManager := sync.NewSyncManager(syncConfig, repo, logger)

	// 创建API服务器
	serverConfig := &api.ServerConfig{
		Host:         "0.0.0.0",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
		EnableCORS:   true,
		EnableAuth:   false,
		StaticDir:    "./build/static",
	}

	apiServer := api.NewServer(
		serverConfig,
		clientManager,
		testCoordinator,
		testScheduler,
		syncManager,
		repo,
		logger,
	)

	return &Components{
		ClientManager:   clientManager,
		TestCoordinator: testCoordinator,
		Scheduler:       testScheduler,
		SyncManager:     syncManager,
		APIServer:       apiServer,
	}, nil
}
