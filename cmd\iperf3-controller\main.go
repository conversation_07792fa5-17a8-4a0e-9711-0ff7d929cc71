package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	version = "dev"
	commit  = "unknown"
	date    = "unknown"
)

func main() {
	if err := newRootCommand().Execute(); err != nil {
		logrus.Fatal(err)
	}
}

func newRootCommand() *cobra.Command {
	var (
		configFile string
		logLevel   string
	)

	rootCmd := &cobra.Command{
		Use:   "iperf3-controller",
		Short: "iPerf3 dual OpenWRT speed testing system",
		Long: `A network speed testing system for dual OpenWRT architecture.
Supports odd/even hour scheduling, 12 server client management,
SQLite database storage and dual-machine synchronization.`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) == 0 {
				return cmd.Help()
			}
			return nil // Should not be reached if subcommands handle execution
		},
	}

	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/config.yaml", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "info", "log level (debug, info, warn, error)")

	// Server subcommand
	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Run iPerf3 in server mode",
		Long:  "Starts the iPerf3 controller in server mode, listening for client connections.",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runServerMode(configFile, logLevel, viper.GetString("aip"), viper.GetInt("ap"))
		},
	}
	serverCmd.Flags().String("aip", "", "peer OpenWRT IP address")
	serverCmd.Flags().Int("ap", 55201, "peer OpenWRT port")

	// Client subcommand
	clientCmd := &cobra.Command{
		Use:   "client",
		Short: "Run iPerf3 in client mode",
		Long:  "Starts the iPerf3 controller in client mode, connecting to a server.",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runClientMode(configFile, logLevel, viper.GetString("host"), viper.GetInt("port"), viper.GetInt("duration"), viper.GetString("bandwidth"))
		},
	}
	clientCmd.Flags().String("host", "", "iPerf3 server host to connect to")
	clientCmd.Flags().Int("port", 5201, "iPerf3 server port to connect to")
	clientCmd.Flags().Int("duration", 10, "Duration of the test in seconds")
	clientCmd.Flags().String("bandwidth", "1G", "Target bandwidth in bits per second")

	rootCmd.AddCommand(serverCmd, clientCmd)

	return rootCmd
}

func runAppBase(configFile, logLevel string) (*context.Context, context.CancelFunc, error) {
	// Setup logging
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid log level: %w", err)
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.WithFields(logrus.Fields{
		"version":     version,
		"commit":      commit,
		"build_date":  date,
		"config_file": configFile,
	}).Info("Starting iPerf3 controller")

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())

	// Setup signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		logrus.WithField("signal", sig).Info("Received shutdown signal")
		cancel()
	}()

	return &ctx, cancel, nil
}

func runServerMode(configFile, logLevel string, peerIP string, peerPort int) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":      "server",
		"peer_ip":   peerIP,
		"peer_port": peerPort,
	}).Info("Running in server mode")

	// TODO: Initialize and start server components
	// This will be implemented in later tasks:
	// 1. Load configuration
	// 2. Initialize database
	// 3. Start client manager
	// 4. Start test coordinator
	// 5. Start scheduler
	// 6. Start sync service
	// 7. Start web server

	logrus.Info("Server application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// Wait for context cancellation
	<-(*ctx).Done()
	logrus.Info("Shutting down gracefully...")

	return nil
}

func runClientMode(configFile, logLevel string, host string, port int, duration int, bandwidth string) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":        "client",
		"target_host": host,
		"target_port": port,
		"duration":    duration,
		"bandwidth":   bandwidth,
	}).Info("Running in client mode")

	// TODO: Initialize and start client components
	// This will be implemented in later tasks.

	logrus.Info("Client application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// Wait for context cancellation
	<-(*ctx).Done()
	logrus.Info("Shutting down gracefully...")

	return nil
}
