package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	version = "dev"
	commit  = "unknown"
	date    = "unknown"
)

func main() {
	if err := newRootCommand().Execute(); err != nil {
		logrus.Fatal(err)
	}
}

func newRootCommand() *cobra.Command {
	var (
		configFile string
		logLevel   string
	)

	rootCmd := &cobra.Command{
		Use:   "iperf3-controller",
		Short: "iPerf3 dual OpenWRT speed testing system",
		Long: `一个用于双OpenWRT架构的网络速度测试系统。
支持奇偶小时调度、12个服务器客户端管理、
SQLite数据库存储和双机同步。`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) == 0 {
				return cmd.Help()
			}
			return nil // Should not be reached if subcommands handle execution
		},
	}

	// 全局标志
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/config.yaml", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "info", "log level (debug, info, warn, error)")

	// 服务器子命令
	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Run iPerf3 in server mode",
		Long:  "以服务器模式启动iPerf3控制器，监听客户端连接。",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runServerMode(configFile, logLevel, viper.GetString("aip"), viper.GetInt("ap"))
		},
	}
	serverCmd.Flags().String("aip", "", "peer OpenWRT IP address")
	serverCmd.Flags().Int("ap", 55201, "peer OpenWRT port")

	// 客户端子命令
	clientCmd := &cobra.Command{
		Use:   "client",
		Short: "Run iPerf3 in client mode",
		Long:  "以客户端模式启动iPerf3控制器，连接到服务器。",
		RunE: func(cmd *cobra.Command, args []string) error {
			viper.BindPFlags(cmd.Flags())
			return runClientMode(configFile, logLevel, viper.GetString("host"), viper.GetInt("port"), viper.GetInt("duration"), viper.GetString("bandwidth"))
		},
	}
	clientCmd.Flags().String("host", "", "iPerf3 server host to connect to")
	clientCmd.Flags().Int("port", 5201, "iPerf3 server port to connect to")
	clientCmd.Flags().Int("duration", 10, "Duration of the test in seconds")
	clientCmd.Flags().String("bandwidth", "1G", "Target bandwidth in bits per second")

	rootCmd.AddCommand(serverCmd, clientCmd)

	return rootCmd
}

func runAppBase(configFile, logLevel string) (*context.Context, context.CancelFunc, error) {
	// Setup logging
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid log level: %w", err)
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.WithFields(logrus.Fields{
		"version":     version,
		"commit":      commit,
		"build_date":  date,
		"config_file": configFile,
	}).Info("Starting iPerf3 controller")

	// 创建用于优雅关闭的上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		logrus.WithField("signal", sig).Info("Received shutdown signal")
		cancel()
	}()

	return &ctx, cancel, nil
}

func runServerMode(configFile, logLevel string, peerIP string, peerPort int) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":      "server",
		"peer_ip":   peerIP,
		"peer_port": peerPort,
	}).Info("Running in server mode")

	// 初始化服务器组件
	logrus.Info("初始化服务器组件...")

	// 这里可以添加服务器特有的初始化逻辑
	// 例如：启动iperf3服务器、配置防火墙规则等
	logrus.Info("服务器组件初始化完成")
	// 这将在后续任务中实现：
	// 1. 加载配置
	// 2. 初始化数据库
	// 3. 启动客户端管理器
	// 4. 启动测试协调器
	// 5. 启动调度器
	// 6. 启动同步服务
	// 7. 启动Web服务器

	logrus.Info("Server application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// Wait for context cancellation
	<-(*ctx).Done() // 等待上下文取消
	logrus.Info("正在优雅关闭...")

	return nil
}

func runClientMode(configFile, logLevel string, host string, port int, duration int, bandwidth string) error {
	ctx, cancel, err := runAppBase(configFile, logLevel)
	if err != nil {
		return err
	}
	defer cancel()

	logrus.WithFields(logrus.Fields{
		"mode":        "client",
		"target_host": host,
		"target_port": port,
		"duration":    duration,
		"bandwidth":   bandwidth,
	}).Info("Running in client mode")

	// 初始化客户端组件
	logrus.Info("初始化客户端组件...")

	// 这里可以添加客户端特有的初始化逻辑
	// 例如：连接到服务器、准备测试环境等
	logrus.Info("客户端组件初始化完成")

	logrus.Info("Client application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// 等待上下文取消
	<-(*ctx).Done()
	logrus.Info("Shutting down gracefully...")

	return nil
}
