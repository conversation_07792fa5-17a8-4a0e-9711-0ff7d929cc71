package iperf3

import (
	"context"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// Executor iperf3命令执行器接口
type Executor interface {
	// ExecuteTCPTest 执行TCP测试
	ExecuteTCPTest(ctx context.Context, config TCPTestConfig) (*TestResult, error)

	// ExecuteUDPTest 执行UDP测试
	ExecuteUDPTest(ctx context.Context, config UDPTestConfig) (*TestResult, error)

	// GetVersion 获取iperf3版本信息
	GetVersion() (string, error)

	// IsAvailable 检查iperf3是否可用
	IsAvailable() bool
}

// TCPTestConfig TCP测试配置
type TCPTestConfig struct {
	// 目标服务器地址
	ServerHost string `json:"server_host"`

	// 目标服务器端口
	ServerPort int `json:"server_port"`

	// 测试持续时间（秒）
	Duration int `json:"duration"`

	// 并行流数量
	ParallelStreams int `json:"parallel_streams"`

	// TCP窗口大小
	WindowSize string `json:"window_size"`

	// 最大段大小
	MSS int `json:"mss"`

	// 是否反向测试（服务器到客户端）
	Reverse bool `json:"reverse"`

	// 额外的iperf3参数
	ExtraArgs []string `json:"extra_args"`
}

// UDPTestConfig UDP测试配置
type UDPTestConfig struct {
	// 目标服务器地址
	ServerHost string `json:"server_host"`

	// 目标服务器端口
	ServerPort int `json:"server_port"`

	// 测试持续时间（秒）
	Duration int `json:"duration"`

	// 目标带宽
	Bandwidth string `json:"bandwidth"`

	// UDP数据包大小
	PacketSize int `json:"packet_size"`

	// 是否反向测试（服务器到客户端）
	Reverse bool `json:"reverse"`

	// 额外的iperf3参数
	ExtraArgs []string `json:"extra_args"`
}

// TestResult iperf3测试结果
type TestResult struct {
	// 测试类型（tcp/udp）
	Type string `json:"type"`

	// 测试开始时间
	StartTime time.Time `json:"start_time"`

	// 测试结束时间
	EndTime time.Time `json:"end_time"`

	// 测试持续时间
	Duration time.Duration `json:"duration"`

	// 传输速度（Mbps）
	MbitsPerSecond float64 `json:"mbits_per_second"`

	// 传输字节数
	BytesTransferred int64 `json:"bytes_transferred"`

	// 重传次数（仅TCP）
	Retransmits int `json:"retransmits,omitempty"`

	// 丢包率（仅UDP）
	PacketLoss float64 `json:"packet_loss,omitempty"`

	// 抖动（仅UDP）
	Jitter float64 `json:"jitter,omitempty"`

	// 原始JSON输出
	RawOutput string `json:"raw_output"`

	// 错误信息
	Error string `json:"error,omitempty"`
}

// DefaultExecutor 默认的iperf3执行器实现
type DefaultExecutor struct {
	// iperf3可执行文件路径
	BinaryPath string

	// 日志器
	logger *logrus.Logger

	// 默认超时时间
	DefaultTimeout time.Duration
}

// NewExecutor 创建新的iperf3执行器
func NewExecutor(binaryPath string, logger *logrus.Logger) *DefaultExecutor {
	if logger == nil {
		logger = logrus.New()
	}

	if binaryPath == "" {
		binaryPath = "iperf3" // 使用系统PATH中的iperf3
	}

	return &DefaultExecutor{
		BinaryPath:     binaryPath,
		logger:         logger,
		DefaultTimeout: 120 * time.Second, // 默认2分钟超时
	}
}

// IsAvailable 检查iperf3是否可用
func (e *DefaultExecutor) IsAvailable() bool {
	_, err := e.GetVersion()
	return err == nil
}

// GetVersion 获取iperf3版本信息
func (e *DefaultExecutor) GetVersion() (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, e.BinaryPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("获取iperf3版本失败: %w", err)
	}

	version := strings.TrimSpace(string(output))
	return version, nil
}

// ExecuteTCPTest 执行TCP测试
func (e *DefaultExecutor) ExecuteTCPTest(ctx context.Context, config TCPTestConfig) (*TestResult, error) {
	e.logger.WithFields(logrus.Fields{
		"server_host":      config.ServerHost,
		"server_port":      config.ServerPort,
		"duration":         config.Duration,
		"parallel_streams": config.ParallelStreams,
		"window_size":      config.WindowSize,
		"mss":              config.MSS,
		"reverse":          config.Reverse,
	}).Info("开始执行TCP测试")

	// 构建iperf3命令参数
	args := []string{
		"-c", config.ServerHost, // 客户端模式，连接到服务器
		"-p", strconv.Itoa(config.ServerPort), // 服务器端口
		"-t", strconv.Itoa(config.Duration), // 测试持续时间
		"-J", // JSON输出格式
	}

	// 添加并行流参数
	if config.ParallelStreams > 1 {
		args = append(args, "-P", strconv.Itoa(config.ParallelStreams))
	}

	// 添加窗口大小参数
	if config.WindowSize != "" {
		args = append(args, "-w", config.WindowSize)
	}

	// 添加MSS参数
	if config.MSS > 0 {
		args = append(args, "-M", strconv.Itoa(config.MSS))
	}

	// 添加反向测试参数
	if config.Reverse {
		args = append(args, "-R")
	}

	// 添加额外参数
	args = append(args, config.ExtraArgs...)

	return e.executeCommand(ctx, args, "tcp")
}

// ExecuteUDPTest 执行UDP测试
func (e *DefaultExecutor) ExecuteUDPTest(ctx context.Context, config UDPTestConfig) (*TestResult, error) {
	e.logger.WithFields(logrus.Fields{
		"server_host": config.ServerHost,
		"server_port": config.ServerPort,
		"duration":    config.Duration,
		"bandwidth":   config.Bandwidth,
		"packet_size": config.PacketSize,
		"reverse":     config.Reverse,
	}).Info("开始执行UDP测试")

	// 构建iperf3命令参数
	args := []string{
		"-c", config.ServerHost, // 客户端模式，连接到服务器
		"-p", strconv.Itoa(config.ServerPort), // 服务器端口
		"-t", strconv.Itoa(config.Duration), // 测试持续时间
		"-u", // UDP模式
		"-J", // JSON输出格式
	}

	// 添加带宽参数
	if config.Bandwidth != "" {
		args = append(args, "-b", config.Bandwidth)
	}

	// 添加数据包大小参数
	if config.PacketSize > 0 {
		args = append(args, "-l", strconv.Itoa(config.PacketSize))
	}

	// 添加反向测试参数
	if config.Reverse {
		args = append(args, "-R")
	}

	// 添加额外参数
	args = append(args, config.ExtraArgs...)

	return e.executeCommand(ctx, args, "udp")
}

// executeCommand 执行iperf3命令
func (e *DefaultExecutor) executeCommand(ctx context.Context, args []string, testType string) (*TestResult, error) {
	startTime := time.Now()

	// 创建带超时的上下文
	if _, hasDeadline := ctx.Deadline(); !hasDeadline {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, e.DefaultTimeout)
		defer cancel()
	}

	e.logger.WithFields(logrus.Fields{
		"command": e.BinaryPath,
		"args":    strings.Join(args, " "),
		"type":    testType,
	}).Debug("执行iperf3命令")

	// 执行命令
	cmd := exec.CommandContext(ctx, e.BinaryPath, args...)
	output, err := cmd.Output()
	endTime := time.Now()

	result := &TestResult{
		Type:      testType,
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  endTime.Sub(startTime),
		RawOutput: string(output),
	}

	if err != nil {
		result.Error = err.Error()
		e.logger.WithFields(logrus.Fields{
			"error":    err.Error(),
			"duration": result.Duration,
			"type":     testType,
		}).Error("iperf3命令执行失败")
		return result, fmt.Errorf("iperf3命令执行失败: %w", err)
	}

	// 解析JSON输出
	if err := e.parseResult(result); err != nil {
		result.Error = err.Error()
		e.logger.WithFields(logrus.Fields{
			"error":  err.Error(),
			"output": string(output),
			"type":   testType,
		}).Error("解析iperf3输出失败")
		return result, fmt.Errorf("解析iperf3输出失败: %w", err)
	}

	e.logger.WithFields(logrus.Fields{
		"type":              testType,
		"duration":          result.Duration,
		"mbits_per_second":  result.MbitsPerSecond,
		"bytes_transferred": result.BytesTransferred,
	}).Info("iperf3测试完成")

	return result, nil
}
