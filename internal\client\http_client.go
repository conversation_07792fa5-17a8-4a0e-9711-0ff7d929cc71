package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"iperf3-controller/internal/config"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// HTTPClient implements the Client interface using HTTP communication
type HTTPClient struct {
	id      string
	name    string
	host    string
	port    int
	enabled bool
	state   ClientState

	httpClient *http.Client
	logger     *logrus.Logger
	config     *config.ClientManagementConfig

	// Mutex for thread-safe state management
	mu sync.RWMutex

	// Last error
	lastError error

	// Current test configuration
	currentTest *TestConfig
}

// NewHTTPClient creates a new HTTP client
func NewHTTPClient(info ClientInfo, logger *logrus.Logger, cfg *config.ClientManagementConfig) *HTTPClient {
	if logger == nil {
		logger = logrus.New()
	}

	// Create HTTP client with timeouts
	httpClient := &http.Client{
		Timeout: cfg.TestTimeout,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			MaxIdleConnsPerHost: 2,
			IdleConnTimeout:     30 * time.Second,
		},
	}

	return &HTTPClient{
		id:         info.ID,
		name:       info.Name,
		host:       info.Host,
		port:       info.Port,
		enabled:    info.Enabled,
		state:      StateIdle,
		httpClient: httpClient,
		logger:     logger,
		config:     cfg,
	}
}

// GetID returns the unique identifier of the client
func (c *HTTPClient) GetID() string {
	return c.id
}

// GetName returns the human-readable name of the client
func (c *HTTPClient) GetName() string {
	return c.name
}

// GetHost returns the host address of the client
func (c *HTTPClient) GetHost() string {
	return c.host
}

// GetPort returns the port of the client
func (c *HTTPClient) GetPort() int {
	return c.port
}

// GetState returns the current state of the client
func (c *HTTPClient) GetState() ClientState {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state
}

// IsEnabled returns whether the client is enabled for testing
func (c *HTTPClient) IsEnabled() bool {
	return c.enabled
}

// setState sets the client state (thread-safe)
func (c *HTTPClient) setState(state ClientState) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.state = state
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"old_state": c.state,
		"new_state": state,
	}).Debug("Client state changed")
}

// setError sets the last error and changes state to error
func (c *HTTPClient) setError(err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastError = err
	c.state = StateError
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"error":     err.Error(),
	}).Error("Client error occurred")
}

// sendRequest sends an HTTP request to the client
func (c *HTTPClient) sendRequest(ctx context.Context, endpoint string, request interface{}) ([]byte, error) {
	// Create request body
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("http://%s:%d%s", c.host, c.port, endpoint)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// Prepare prepares the client for testing (starts iperf3 server)
func (c *HTTPClient) Prepare(ctx context.Context) error {
	c.setState(StatePreparing)

	// Create prepare request
	request := PrepareRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypePrepareRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
		Port: 5201, // Default iperf3 port
	}

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, c.config.PrepareTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/prepare", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("prepare request failed: %w", err)
	}

	// Parse response
	var response PrepareResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse prepare response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("prepare failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"port":      response.Port,
	}).Info("Client prepared successfully")

	return nil
}

// StartTest starts a test on the client
func (c *HTTPClient) StartTest(ctx context.Context, testConfig TestConfig) (*TestResult, error) {
	c.setState(StateTesting)
	c.mu.Lock()
	c.currentTest = &testConfig
	c.mu.Unlock()

	// Create start test request
	request := StartTestRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStartTestRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
		TestConfig: testConfig,
		ServerHost: "controller", // This should be the controller's host
		ServerPort: 5201,
	}

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, c.config.TestTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/start_test", request)
	if err != nil {
		c.setError(err)
		return nil, fmt.Errorf("start test request failed: %w", err)
	}

	// Parse response
	var response StartTestResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return nil, fmt.Errorf("failed to parse start test response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("test failed: %s", response.Message)
		c.setError(err)
		return nil, err
	}

	c.setState(StateIdle)
	c.mu.Lock()
	c.currentTest = nil
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"client_id":        c.id,
		"test_type":        testConfig.Type,
		"mbits_per_second": response.TestResult.MbitsPerSecond,
	}).Info("Test completed successfully")

	return response.TestResult, nil
}

// StopTest stops the current test on the client
func (c *HTTPClient) StopTest(ctx context.Context) error {
	// Create stop test request
	request := StopTestRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStopTestRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, c.config.StopTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/stop_test", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("stop test request failed: %w", err)
	}

	// Parse response
	var response StopTestResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse stop test response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("stop test failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.mu.Lock()
	c.currentTest = nil
	c.mu.Unlock()

	c.logger.WithField("client_id", c.id).Info("Test stopped successfully")
	return nil
}

// Stop stops the iperf3 server on the client
func (c *HTTPClient) Stop(ctx context.Context) error {
	// Create stop request
	request := StopRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStopRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, c.config.StopTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/stop", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("stop request failed: %w", err)
	}

	// Parse response
	var response StopResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse stop response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("stop failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.logger.WithField("client_id", c.id).Info("Client stopped successfully")
	return nil
}

// GetStatus gets the current status of the client
func (c *HTTPClient) GetStatus(ctx context.Context) (*ClientStatus, error) {
	// Create status request
	request := StatusRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStatusRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/status", request)
	if err != nil {
		c.setError(err)
		return nil, fmt.Errorf("status request failed: %w", err)
	}

	// Parse response
	var response StatusResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return nil, fmt.Errorf("failed to parse status response: %w", err)
	}

	// Update local state based on remote status
	c.setState(response.Status.State)

	return &response.Status, nil
}

// Close closes the client connection
func (c *HTTPClient) Close() error {
	c.setState(StateDisconnected)
	c.httpClient.CloseIdleConnections()
	c.logger.WithField("client_id", c.id).Info("Client connection closed")
	return nil
}
