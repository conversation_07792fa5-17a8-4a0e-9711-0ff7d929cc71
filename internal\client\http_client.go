package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"iperf3-controller/internal/config"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// HTTPClient 使用 HTTP 通信实现 Client 接口
type HTTPClient struct {
	id      string
	name    string
	host    string
	port    int
	enabled bool
	state   ClientState

	httpClient *http.Client
	logger     *logrus.Logger
	config     *config.ClientManagementConfig

	// 用于线程安全状态管理的互斥锁
	mu sync.RWMutex

	// 最后一次错误
	lastError error

	// 当前测试配置
	currentTest *TestConfig
}

// NewHTTPClient 创建一个新的 HTTP 客户端
func NewHTTPClient(info ClientInfo, logger *logrus.Logger, cfg *config.ClientManagementConfig) *HTTPClient {
	if logger == nil {
		logger = logrus.New()
	}

	// Create HTTP client with timeouts
	httpClient := &http.Client{
		Timeout: cfg.TestTimeout,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			MaxIdleConnsPerHost: 2,
			IdleConnTimeout:     30 * time.Second,
		},
	}

	return &HTTPClient{
		id:         info.ID,
		name:       info.Name,
		host:       info.Host,
		port:       info.Port,
		enabled:    info.Enabled,
		state:      StateIdle,
		httpClient: httpClient,
		logger:     logger,
		config:     cfg,
	}
}

// GetID 返回客户端的唯一标识符
func (c *HTTPClient) GetID() string {
	return c.id
}

// GetName 返回客户端的人类可读名称
func (c *HTTPClient) GetName() string {
	return c.name
}

// GetHost 返回客户端的主机地址
func (c *HTTPClient) GetHost() string {
	return c.host
}

// GetPort 返回客户端的端口
func (c *HTTPClient) GetPort() int {
	return c.port
}

// GetState 返回客户端的当前状态
func (c *HTTPClient) GetState() ClientState {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state
}

// IsEnabled 返回客户端是否启用测试
func (c *HTTPClient) IsEnabled() bool {
	return c.enabled
}

// setState 设置客户端状态（线程安全）
func (c *HTTPClient) setState(state ClientState) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.state = state
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"old_state": c.state,
		"new_state": state,
	}).Debug("Client state changed")
}

// setError 设置最后一次错误并更改状态为错误
func (c *HTTPClient) setError(err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastError = err
	c.state = StateError
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"error":     err.Error(),
	}).Error("Client error occurred")
}

// sendRequest 向客户端发送 HTTP 请求
func (c *HTTPClient) sendRequest(ctx context.Context, endpoint string, request interface{}) ([]byte, error) {
	// Create request body
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("http://%s:%d%s", c.host, c.port, endpoint)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// Prepare 准备客户端进行测试（启动 iperf3 服务器）
func (c *HTTPClient) Prepare(ctx context.Context) error {
	c.setState(StatePreparing)

	// Create prepare request
	request := PrepareRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypePrepareRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
		Port: 5201, // 默认 iperf3 端口
	}

	// 将超时添加到上下文
	ctx, cancel := context.WithTimeout(ctx, c.config.PrepareTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/prepare", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("prepare request failed: %w", err)
	}

	// Parse response
	var response PrepareResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse prepare response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("prepare failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.logger.WithFields(logrus.Fields{
		"client_id": c.id,
		"port":      response.Port,
	}).Info("Client prepared successfully")

	return nil
}

// StartTest 在客户端上启动测试
func (c *HTTPClient) StartTest(ctx context.Context, testConfig TestConfig) (*TestResult, error) {
	c.setState(StateTesting)
	c.mu.Lock()
	c.currentTest = &testConfig
	c.mu.Unlock()

	// Create start test request
	request := StartTestRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStartTestRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
		TestConfig: testConfig,
		ServerHost: "controller", // 这应该是控制器的 IP
		ServerPort: 5201,
	}

	// 将超时添加到上下文
	ctx, cancel := context.WithTimeout(ctx, c.config.TestTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/start_test", request)
	if err != nil {
		c.setError(err)
		return nil, fmt.Errorf("start test request failed: %w", err)
	}

	// Parse response
	var response StartTestResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return nil, fmt.Errorf("failed to parse start test response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("test failed: %s", response.Message)
		c.setError(err)
		return nil, err
	}

	c.setState(StateIdle)
	c.mu.Lock()
	c.currentTest = nil
	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"client_id":        c.id,
		"test_type":        testConfig.Type,
		"mbits_per_second": response.TestResult.MbitsPerSecond,
	}).Info("Test completed successfully")

	return response.TestResult, nil
}

// StopTest 停止客户端上的当前测试
func (c *HTTPClient) StopTest(ctx context.Context) error {
	// 创建停止测试请求
	request := StopTestRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStopTestRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// 将超时添加到上下文
	ctx, cancel := context.WithTimeout(ctx, c.config.StopTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/stop_test", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("stop test request failed: %w", err)
	}

	// Parse response
	var response StopTestResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse stop test response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("stop test failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.mu.Lock()
	c.currentTest = nil
	c.mu.Unlock()

	c.logger.WithField("client_id", c.id).Info("Test stopped successfully")
	return nil
}

// Stop 停止客户端上的 iperf3 服务器
func (c *HTTPClient) Stop(ctx context.Context) error {
	// 创建停止请求
	request := StopRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStopRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// 将超时添加到上下文
	ctx, cancel := context.WithTimeout(ctx, c.config.StopTimeout)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/stop", request)
	if err != nil {
		c.setError(err)
		return fmt.Errorf("stop request failed: %w", err)
	}

	// Parse response
	var response StopResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return fmt.Errorf("failed to parse stop response: %w", err)
	}

	if !response.Success {
		err := fmt.Errorf("stop failed: %s", response.Message)
		c.setError(err)
		return err
	}

	c.setState(StateIdle)
	c.logger.WithField("client_id", c.id).Info("Client stopped successfully")
	return nil
}

// GetStatus 获取客户端的当前状态
func (c *HTTPClient) GetStatus(ctx context.Context) (*ClientStatus, error) {
	// 创建状态请求
	request := StatusRequest{
		BaseMessage: BaseMessage{
			Type:      MessageTypeStatusRequest,
			RequestID: uuid.New().String(),
			Timestamp: time.Now(),
		},
	}

	// 将超时添加到上下文
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Send request
	responseBody, err := c.sendRequest(ctx, "/api/status", request)
	if err != nil {
		c.setError(err)
		return nil, fmt.Errorf("status request failed: %w", err)
	}

	// Parse response
	var response StatusResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		c.setError(err)
		return nil, fmt.Errorf("failed to parse status response: %w", err)
	}

	// 根据远程状态更新本地状态
	c.setState(response.Status.State)

	return &response.Status, nil
}

// Close 关闭客户端连接
func (c *HTTPClient) Close() error {
	c.setState(StateDisconnected)
	c.httpClient.CloseIdleConnections()
	c.logger.WithField("client_id", c.id).Info("Client connection closed")
	return nil
}
