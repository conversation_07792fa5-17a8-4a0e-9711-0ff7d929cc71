package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// Repository 提供了数据访问方法
type Repository struct {
	db     *DB
	logger *logrus.Logger
}

// NewRepository 创建一个新的仓库实例
func NewRepository(db *DB, logger *logrus.Logger) *Repository {
	return &Repository{
		db:     db,
		logger: logger,
	}
}

// Server operations 服务器操作

// GetServers 返回所有服务器
func (r *Repository) GetServers() ([]Server, error) {
	query := `SELECT id, name, host, port, enabled, created_at FROM servers ORDER BY name`
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询服务器失败: %w", err)
	}
	defer rows.Close()

	var servers []Server
	for rows.Next() {
		var server Server
		err := rows.Scan(&server.ID, &server.Name, &server.Host, &server.Port, &server.Enabled, &server.CreatedAt)
		if err != nil {
			return nil, fmt.Errorf("扫描服务器失败: %w", err)
		}
		servers = append(servers, server)
	}

	return servers, rows.Err()
}

// GetEnabledServers 只返回启用的服务器
func (r *Repository) GetEnabledServers() ([]Server, error) {
	query := `SELECT id, name, host, port, enabled, created_at FROM servers WHERE enabled = 1 ORDER BY name`
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询启用的服务器失败: %w", err)
	}
	defer rows.Close()

	var servers []Server
	for rows.Next() {
		var server Server
		err := rows.Scan(&server.ID, &server.Name, &server.Host, &server.Port, &server.Enabled, &server.CreatedAt)
		if err != nil {
			return nil, fmt.Errorf("扫描服务器失败: %w", err)
		}
		servers = append(servers, server)
	}

	return servers, rows.Err()
}

// GetServerByName 通过名称返回服务器
func (r *Repository) GetServerByName(name string) (*Server, error) {
	query := `SELECT id, name, host, port, enabled, created_at FROM servers WHERE name = ?`
	var server Server
	err := r.db.QueryRow(query, name).Scan(&server.ID, &server.Name, &server.Host, &server.Port, &server.Enabled, &server.CreatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrServerNotFound
		}
		return nil, fmt.Errorf("通过名称获取服务器失败: %w", err)
	}
	return &server, nil
}

// CreateServer 创建一个新的服务器
func (r *Repository) CreateServer(server *Server) error {
	if err := server.Validate(); err != nil {
		return err
	}

	query := `INSERT INTO servers (name, host, port, enabled) VALUES (?, ?, ?, ?)`
	result, err := r.db.Exec(query, server.Name, server.Host, server.Port, server.Enabled)
	if err != nil {
		return fmt.Errorf("创建服务器失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取服务器 ID 失败: %w", err)
	}

	server.ID = int(id)
	server.CreatedAt = time.Now()

	r.logger.WithField("server", server.Name).Info("服务器已创建")
	return nil
}

// UpdateServer 更新现有服务器
func (r *Repository) UpdateServer(server *Server) error {
	if err := server.Validate(); err != nil {
		return err
	}

	query := `UPDATE servers SET host = ?, port = ?, enabled = ? WHERE name = ?`
	result, err := r.db.Exec(query, server.Host, server.Port, server.Enabled, server.Name)
	if err != nil {
		return fmt.Errorf("更新服务器失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响的行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrServerNotFound
	}

	r.logger.WithField("server", server.Name).Info("服务器已更新")
	return nil
}

// DeleteServer 删除服务器
func (r *Repository) DeleteServer(name string) error {
	query := `DELETE FROM servers WHERE name = ?`
	result, err := r.db.Exec(query, name)
	if err != nil {
		return fmt.Errorf("删除服务器失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响的行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrServerNotFound
	}

	r.logger.WithField("server", name).Info("服务器已删除")
	return nil
}

// HourlyResult operations 每小时结果操作

// GetHourlyResult 返回特定的每小时结果
func (r *Repository) GetHourlyResult(serverName string, testHour time.Time) (*HourlyResult, error) {
	query := `SELECT id, server_name, test_hour, tcp_speed_mbps, udp_speed_mbps, tcp_status, udp_status,
			  tcp_error, udp_error, created_at, updated_at
			  FROM hourly_results WHERE server_name = ? AND test_hour = ?`

	var result HourlyResult
	var tcpSpeed, udpSpeed sql.NullFloat64
	var tcpError, udpError sql.NullString

	err := r.db.QueryRow(query, serverName, testHour).Scan(
		&result.ID, &result.ServerName, &result.TestHour,
		&tcpSpeed, &udpSpeed, &result.TCPStatus, &result.UDPStatus,
		&tcpError, &udpError, &result.CreatedAt, &result.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrResultNotFound
		}
		return nil, fmt.Errorf("获取每小时结果失败: %w", err)
	}

	if tcpSpeed.Valid {
		result.TCPSpeedMbps = &tcpSpeed.Float64
	}
	if udpSpeed.Valid {
		result.UDPSpeedMbps = &udpSpeed.Float64
	}
	if tcpError.Valid {
		result.TCPError = &tcpError.String
	}
	if udpError.Valid {
		result.UDPError = &udpError.String
	}

	return &result, nil
}

// CreateOrUpdateHourlyResult 创建或更新每小时结果
func (r *Repository) CreateOrUpdateHourlyResult(result *HourlyResult) error {
	if err := result.Validate(); err != nil {
		return err
	}

	// 使用 UPSERT (INSERT OR REPLACE)
	query := `INSERT OR REPLACE INTO hourly_results
			  (server_name, test_hour, tcp_speed_mbps, udp_speed_mbps, tcp_status, udp_status,
			   tcp_error, udp_error, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?,
			          COALESCE((SELECT created_at FROM hourly_results WHERE server_name = ? AND test_hour = ?), CURRENT_TIMESTAMP),
			          CURRENT_TIMESTAMP)`

	_, err := r.db.Exec(query,
		result.ServerName, result.TestHour, result.TCPSpeedMbps, result.UDPSpeedMbps,
		result.TCPStatus, result.UDPStatus, result.TCPError, result.UDPError,
		result.ServerName, result.TestHour,
	)

	if err != nil {
		return fmt.Errorf("创建或更新每小时结果失败: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"server": result.ServerName,
		"hour":   result.TestHour.Format("2006-01-02 15:04:05"),
	}).Debug("每小时结果已保存")

	return nil
}

// GetHourlyResults 返回带有可选过滤器的每小时结果
func (r *Repository) GetHourlyResults(serverName string, startTime, endTime time.Time, limit int) ([]HourlyResult, error) {
	query := `SELECT id, server_name, test_hour, tcp_speed_mbps, udp_speed_mbps, tcp_status, udp_status,
			  tcp_error, udp_error, created_at, updated_at
			  FROM hourly_results WHERE 1=1`
	args := []interface{}{}

	if serverName != "" {
		query += " AND server_name = ?"
		args = append(args, serverName)
	}

	if !startTime.IsZero() {
		query += " AND test_hour >= ?"
		args = append(args, startTime)
	}

	if !endTime.IsZero() {
		query += " AND test_hour <= ?"
		args = append(args, endTime)
	}

	query += " ORDER BY test_hour DESC"

	if limit > 0 {
		query += " LIMIT ?"
		args = append(args, limit)
	}

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询每小时结果失败: %w", err)
	}
	defer rows.Close()

	var results []HourlyResult
	for rows.Next() {
		var result HourlyResult
		var tcpSpeed, udpSpeed sql.NullFloat64
		var tcpError, udpError sql.NullString

		err := rows.Scan(
			&result.ID, &result.ServerName, &result.TestHour,
			&tcpSpeed, &udpSpeed, &result.TCPStatus, &result.UDPStatus,
			&tcpError, &udpError, &result.CreatedAt, &result.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描每小时结果失败: %w", err)
		}

		if tcpSpeed.Valid {
			result.TCPSpeedMbps = &tcpSpeed.Float64
		}
		if udpSpeed.Valid {
			result.UDPSpeedMbps = &udpSpeed.Float64
		}
		if tcpError.Valid {
			result.TCPError = &tcpError.String
		}
		if udpError.Valid {
			result.UDPError = &udpError.String
		}

		results = append(results, result)
	}

	return results, rows.Err()
}

// GetServerSummaries 返回所有服务器的汇总统计信息
func (r *Repository) GetServerSummaries() ([]ServerSummary, error) {
	query := `
	SELECT
		s.name as server_name,
		hr.last_test_time,
		hr.last_tcp_speed,
		hr.last_udp_speed,
		hr.tcp_status,
		hr.udp_status,
		COALESCE(stats.tests_today, 0) as tests_today,
		COALESCE(stats.successful_tests, 0) as successful_tests,
		stats.avg_tcp_speed_24h,
		stats.avg_udp_speed_24h
	FROM servers s
	LEFT JOIN (
		SELECT
			server_name,
			test_hour as last_test_time,
			tcp_speed_mbps as last_tcp_speed,
			udp_speed_mbps as last_udp_speed,
			tcp_status,
			udp_status,
			ROW_NUMBER() OVER (PARTITION BY server_name ORDER BY test_hour DESC) as rn
		FROM hourly_results
	) hr ON s.name = hr.server_name AND hr.rn = 1
	LEFT JOIN (
		SELECT
			server_name,
			COUNT(*) as tests_today,
			SUM(CASE WHEN tcp_status = 'success' AND udp_status = 'success' THEN 1 ELSE 0 END) as successful_tests,
			AVG(CASE WHEN tcp_status = 'success' THEN tcp_speed_mbps END) as avg_tcp_speed_24h,
			AVG(CASE WHEN udp_status = 'success' THEN udp_speed_mbps END) as avg_udp_speed_24h
		FROM hourly_results
		WHERE test_hour >= datetime('now', '-24 hours')
		GROUP BY server_name
	) stats ON s.name = stats.server_name
	WHERE s.enabled = 1
	ORDER BY s.name`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询服务器汇总失败: %w", err)
	}
	defer rows.Close()

	var summaries []ServerSummary
	for rows.Next() {
		var summary ServerSummary
		var lastTestTime sql.NullTime
		var lastTCPSpeed, lastUDPSpeed, avgTCPSpeed24h, avgUDPSpeed24h sql.NullFloat64
		var tcpStatus, udpStatus sql.NullString

		err := rows.Scan(
			&summary.ServerName, &lastTestTime, &lastTCPSpeed, &lastUDPSpeed,
			&tcpStatus, &udpStatus, &summary.TestsToday, &summary.SuccessfulTests,
			&avgTCPSpeed24h, &avgUDPSpeed24h,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描服务器汇总失败: %w", err)
		}

		if lastTestTime.Valid {
			summary.LastTestTime = &lastTestTime.Time
		}
		if lastTCPSpeed.Valid {
			summary.LastTCPSpeed = &lastTCPSpeed.Float64
		}
		if lastUDPSpeed.Valid {
			summary.LastUDPSpeed = &lastUDPSpeed.Float64
		}
		if tcpStatus.Valid {
			summary.TCPStatus = tcpStatus.String
		} else {
			summary.TCPStatus = StatusPending
		}
		if udpStatus.Valid {
			summary.UDPStatus = udpStatus.String
		} else {
			summary.UDPStatus = StatusPending
		}
		if avgTCPSpeed24h.Valid {
			summary.AvgTCPSpeed24h = &avgTCPSpeed24h.Float64
		}
		if avgUDPSpeed24h.Valid {
			summary.AvgUDPSpeed24h = &avgUDPSpeed24h.Float64
		}

		summaries = append(summaries, summary)
	}

	return summaries, rows.Err()
}

// SystemConfig operations 系统配置操作

// GetSystemConfig 返回系统配置值
func (r *Repository) GetSystemConfig(key string) (*SystemConfig, error) {
	query := `SELECT key, value, description, updated_at FROM system_config WHERE key = ?`
	var config SystemConfig
	var description sql.NullString

	err := r.db.QueryRow(query, key).Scan(&config.Key, &config.Value, &description, &config.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到配置键: %s", key)
		}
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	if description.Valid {
		config.Description = &description.String
	}

	return &config, nil
}

// GetAllSystemConfigs 返回所有系统配置
func (r *Repository) GetAllSystemConfigs() ([]SystemConfig, error) {
	query := `SELECT key, value, description, updated_at FROM system_config ORDER BY key`
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询系统配置失败: %w", err)
	}
	defer rows.Close()

	var configs []SystemConfig
	for rows.Next() {
		var config SystemConfig
		var description sql.NullString

		err := rows.Scan(&config.Key, &config.Value, &description, &config.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("扫描系统配置失败: %w", err)
		}

		if description.Valid {
			config.Description = &description.String
		}

		configs = append(configs, config)
	}

	return configs, rows.Err()
}

// SetSystemConfig 设置系统配置值
func (r *Repository) SetSystemConfig(key, value string, description *string) error {
	query := `INSERT OR REPLACE INTO system_config (key, value, description, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)`
	_, err := r.db.Exec(query, key, value, description)
	if err != nil {
		return fmt.Errorf("设置系统配置失败: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"key":   key,
		"value": value,
	}).Debug("系统配置已更新")

	return nil
}

// DeleteSystemConfig 删除系统配置
func (r *Repository) DeleteSystemConfig(key string) error {
	query := `DELETE FROM system_config WHERE key = ?`
	result, err := r.db.Exec(query, key)
	if err != nil {
		return fmt.Errorf("删除系统配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响的行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("未找到配置键: %s", key)
	}

	r.logger.WithField("key", key).Debug("系统配置已删除")
	return nil
}

// SyncStatus operations 同步状态操作

// CreateSyncStatus 创建新的同步状态记录
func (r *Repository) CreateSyncStatus(peerIP string, status string, errorMessage *string) error {
	query := `INSERT INTO sync_status (peer_ip, sync_status, error_message) VALUES (?, ?, ?)`
	_, err := r.db.Exec(query, peerIP, status, errorMessage)
	if err != nil {
		return fmt.Errorf("创建同步状态失败: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"peer_ip": peerIP,
		"status":  status,
	}).Debug("同步状态已创建")

	return nil
}

// UpdateSyncStatus 更新对等体的同步状态
func (r *Repository) UpdateSyncStatus(peerIP string, status string, errorMessage *string) error {
	query := `UPDATE sync_status SET last_sync = CURRENT_TIMESTAMP, sync_status = ?, error_message = ?
			  WHERE peer_ip = ? AND id = (SELECT MAX(id) FROM sync_status WHERE peer_ip = ?)`
	result, err := r.db.Exec(query, status, errorMessage, peerIP, peerIP)
	if err != nil {
		return fmt.Errorf("更新同步状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响的行数失败: %w", err)
	}

	if rowsAffected == 0 {
		// 如果不存在则创建新记录
		return r.CreateSyncStatus(peerIP, status, errorMessage)
	}

	r.logger.WithFields(logrus.Fields{
		"peer_ip": peerIP,
		"status":  status,
	}).Debug("同步状态已更新")

	return nil
}

// GetLatestSyncStatus 返回对等体的最新同步状态
func (r *Repository) GetLatestSyncStatus(peerIP string) (*SyncStatus, error) {
	query := `SELECT id, peer_ip, last_sync, sync_status, error_message, created_at
			  FROM sync_status WHERE peer_ip = ? ORDER BY id DESC LIMIT 1`

	var status SyncStatus
	var lastSync sql.NullTime
	var errorMessage sql.NullString

	err := r.db.QueryRow(query, peerIP).Scan(
		&status.ID, &status.PeerIP, &lastSync, &status.SyncStatus, &errorMessage, &status.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到对等体 %s 的同步状态", peerIP)
		}
		return nil, fmt.Errorf("获取同步状态失败: %w", err)
	}

	if lastSync.Valid {
		status.LastSync = &lastSync.Time
	}
	if errorMessage.Valid {
		status.ErrorMessage = &errorMessage.String
	}

	return &status, nil
}

// GetAllSyncStatuses 返回所有同步状态
func (r *Repository) GetAllSyncStatuses() ([]SyncStatus, error) {
	query := `SELECT id, peer_ip, last_sync, sync_status, error_message, created_at
			  FROM sync_status ORDER BY created_at DESC`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询同步状态失败: %w", err)
	}
	defer rows.Close()

	var statuses []SyncStatus
	for rows.Next() {
		var status SyncStatus
		var lastSync sql.NullTime
		var errorMessage sql.NullString

		err := rows.Scan(
			&status.ID, &status.PeerIP, &lastSync, &status.SyncStatus, &errorMessage, &status.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描同步状态失败: %w", err)
		}

		if lastSync.Valid {
			status.LastSync = &lastSync.Time
		}
		if errorMessage.Valid {
			status.ErrorMessage = &errorMessage.String
		}

		statuses = append(statuses, status)
	}

	return statuses, rows.Err()
}
