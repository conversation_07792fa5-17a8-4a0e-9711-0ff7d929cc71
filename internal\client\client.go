package client

import (
	"context"
	"time"
)

// ClientState 表示客户端的当前状态
type ClientState int

const (
	// StateIdle 客户端空闲并准备进行测试
	StateIdle ClientState = iota
	// StatePreparing 客户端正在准备进行测试（启动 iperf3 服务器）
	StatePreparing
	// StateTesting 客户端当前正在运行测试
	StateTesting
	// StateError 客户端遇到错误
	StateError
	// StateDisconnected 客户端已断开连接
	StateDisconnected
)

// String 返回 ClientState 的字符串表示
func (s ClientState) String() string {
	switch s {
	case StateIdle:
		return "idle"
	case StatePreparing:
		return "preparing"
	case StateTesting:
		return "testing"
	case StateError:
		return "error"
	case StateDisconnected:
		return "disconnected"
	default:
		return "unknown"
	}
}

// Client 表示可以运行 iperf3 测试的单个服务器客户端
type Client interface {
	// GetID 返回客户端的唯一标识符
	GetID() string
	
	// GetName 返回客户端的人类可读名称
	GetName() string
	
	// GetHost 返回客户端的主机地址
	GetHost() string
	
	// GetPort 返回客户端的端口
	GetPort() int
	
	// GetState 返回客户端的当前状态
	GetState() ClientState
	
	// IsEnabled 返回客户端是否启用测试
	IsEnabled() bool
	
	// Prepare 准备客户端进行测试（启动 iperf3 服务器）
	Prepare(ctx context.Context) error
	
	// StartTest 在客户端上启动测试
	StartTest(ctx context.Context, testConfig TestConfig) (*TestResult, error)
	
	// StopTest 停止客户端上的当前测试
	StopTest(ctx context.Context) error
	
	// Stop 停止客户端上的 iperf3 服务器
	Stop(ctx context.Context) error
	
	// GetStatus 获取客户端的当前状态
	GetStatus(ctx context.Context) (*ClientStatus, error)
	
	// Close 关闭客户端连接
	Close() error
}

// TestConfig 表示测试的配置
type TestConfig struct {
	// 测试类型："tcp" 或 "udp"
	Type string `json:"type"`
	
	// 测试持续时间
	Duration time.Duration `json:"duration"`
	
	// TCP 特定配置
	TCP *TCPTestConfig `json:"tcp,omitempty"`
	
	// UDP 特定配置
	UDP *UDPTestConfig `json:"udp,omitempty"`
}

// TCPTestConfig 表示 TCP 测试配置
type TCPTestConfig struct {
	// 并行流数量
	ParallelStreams int `json:"parallel_streams"`
	
	// TCP 窗口大小
	WindowSize string `json:"window_size"`
	
	// 最大分段大小
	MSS int `json:"mss"`
}

// UDPTestConfig 表示 UDP 测试配置
type UDPTestConfig struct {
	// 目标带宽
	Bandwidth string `json:"bandwidth"`
	
	// UDP 数据包大小
	PacketSize int `json:"packet_size"`
}

// TestResult 表示测试结果
type TestResult struct {
	// 客户端 ID
	ClientID string `json:"client_id"`
	
	// 测试类型
	Type string `json:"type"`
	
	// 测试持续时间
	Duration time.Duration `json:"duration"`
	
	// 传输的字节数
	BytesTransferred int64 `json:"bytes_transferred"`
	
	// 每秒位数
	BitsPerSecond float64 `json:"bits_per_second"`
	
	// 每秒兆位
	MbitsPerSecond float64 `json:"mbits_per_second"`
	
	// 测试开始时间
	StartTime time.Time `json:"start_time"`
	
	// 测试结束时间
	EndTime time.Time `json:"end_time"`
	
	// 如果测试失败，则为错误消息
	Error string `json:"error,omitempty"`
	
	// 原始 iperf3 输出
	RawOutput string `json:"raw_output,omitempty"`
}

// ClientStatus 表示客户端的当前状态
type ClientStatus struct {
	// 客户端 ID
	ClientID string `json:"client_id"`
	
	// 当前状态
	State ClientState `json:"state"`
	
	// iperf3 服务器是否正在运行
	ServerRunning bool `json:"server_running"`
	
	// 当前测试信息（如果正在测试）
	CurrentTest *TestConfig `json:"current_test,omitempty"`
	
	// 最后错误消息
	LastError string `json:"last_error,omitempty"`
	
	// 最后更新时间
	LastUpdate time.Time `json:"last_update"`
}

// ClientInfo 表示客户端的基本信息
type ClientInfo struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Host    string `json:"host"`
	Port    int    `json:"port"`
	Enabled bool   `json:"enabled"`
}
