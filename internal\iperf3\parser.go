package iperf3

import (
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"
)

// Iperf3Output iperf3 JSON输出结构
type Iperf3Output struct {
	Start struct {
		Connected []struct {
			Socket     int    `json:"socket"`
			LocalHost  string `json:"local_host"`
			LocalPort  int    `json:"local_port"`
			RemoteHost string `json:"remote_host"`
			RemotePort int    `json:"remote_port"`
		} `json:"connected"`
		Version    string `json:"version"`
		SystemInfo string `json:"system_info"`
		Timestamp  struct {
			Time     string `json:"time"`
			Timesecs int64  `json:"timesecs"`
		} `json:"timestamp"`
		ConnectingTo struct {
			Host string `json:"host"`
			Port int    `json:"port"`
		} `json:"connecting_to"`
		Cookie        string `json:"cookie"`
		TCPMSSDefault int    `json:"tcp_mss_default"`
		TestStart     struct {
			Protocol   string `json:"protocol"`
			NumStreams int    `json:"num_streams"`
			Blksize    int    `json:"blksize"`
			Omit       int    `json:"omit"`
			Duration   int    `json:"duration"`
			Bytes      int64  `json:"bytes"`
			Blocks     int    `json:"blocks"`
			Reverse    int    `json:"reverse"`
		} `json:"test_start"`
	} `json:"start"`

	Intervals []struct {
		Streams []struct {
			Socket        int     `json:"socket"`
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			Retransmits   int     `json:"retransmits,omitempty"`
			SndCwnd       int     `json:"snd_cwnd,omitempty"`
			RTT           int     `json:"rtt,omitempty"`
			RTTVar        int     `json:"rttvar,omitempty"`
			PMTU          int     `json:"pmtu,omitempty"`
			Omitted       bool    `json:"omitted,omitempty"`
			// UDP特有字段
			Packets     int     `json:"packets,omitempty"`
			LostPackets int     `json:"lost_packets,omitempty"`
			OutOfOrder  int     `json:"out_of_order,omitempty"`
			Jitter      float64 `json:"jitter_ms,omitempty"`
		} `json:"streams"`
		Sum struct {
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			Retransmits   int     `json:"retransmits,omitempty"`
			Omitted       bool    `json:"omitted,omitempty"`
			// UDP特有字段
			Packets     int     `json:"packets,omitempty"`
			LostPackets int     `json:"lost_packets,omitempty"`
			OutOfOrder  int     `json:"out_of_order,omitempty"`
			Jitter      float64 `json:"jitter_ms,omitempty"`
		} `json:"sum"`
	} `json:"intervals"`

	End struct {
		Streams []struct {
			Sender struct {
				Socket        int     `json:"socket"`
				Start         float64 `json:"start"`
				End           float64 `json:"end"`
				Seconds       float64 `json:"seconds"`
				Bytes         int64   `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
				Retransmits   int     `json:"retransmits,omitempty"`
				MaxSndCwnd    int     `json:"max_snd_cwnd,omitempty"`
				MaxRTT        int     `json:"max_rtt,omitempty"`
				MinRTT        int     `json:"min_rtt,omitempty"`
				MeanRTT       int     `json:"mean_rtt,omitempty"`
				// UDP特有字段
				Packets     int     `json:"packets,omitempty"`
				LostPackets int     `json:"lost_packets,omitempty"`
				OutOfOrder  int     `json:"out_of_order,omitempty"`
				Jitter      float64 `json:"jitter_ms,omitempty"`
			} `json:"sender"`
			Receiver struct {
				Socket        int     `json:"socket"`
				Start         float64 `json:"start"`
				End           float64 `json:"end"`
				Seconds       float64 `json:"seconds"`
				Bytes         int64   `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
				// UDP特有字段
				Packets     int     `json:"packets,omitempty"`
				LostPackets int     `json:"lost_packets,omitempty"`
				OutOfOrder  int     `json:"out_of_order,omitempty"`
				Jitter      float64 `json:"jitter_ms,omitempty"`
			} `json:"receiver"`
		} `json:"streams"`
		SumSent struct {
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			Retransmits   int     `json:"retransmits,omitempty"`
			// UDP特有字段
			Packets     int     `json:"packets,omitempty"`
			LostPackets int     `json:"lost_packets,omitempty"`
			OutOfOrder  int     `json:"out_of_order,omitempty"`
			Jitter      float64 `json:"jitter_ms,omitempty"`
		} `json:"sum_sent"`
		SumReceived struct {
			Start         float64 `json:"start"`
			End           float64 `json:"end"`
			Seconds       float64 `json:"seconds"`
			Bytes         int64   `json:"bytes"`
			BitsPerSecond float64 `json:"bits_per_second"`
			// UDP特有字段
			Packets     int     `json:"packets,omitempty"`
			LostPackets int     `json:"lost_packets,omitempty"`
			OutOfOrder  int     `json:"out_of_order,omitempty"`
			Jitter      float64 `json:"jitter_ms,omitempty"`
		} `json:"sum_received"`
		CPUUtilizationPercent struct {
			HostTotal    float64 `json:"host_total"`
			HostUser     float64 `json:"host_user"`
			HostSystem   float64 `json:"host_system"`
			RemoteTotal  float64 `json:"remote_total"`
			RemoteUser   float64 `json:"remote_user"`
			RemoteSystem float64 `json:"remote_system"`
		} `json:"cpu_utilization_percent"`
	} `json:"end"`

	Error string `json:"error,omitempty"`
}

// parseResult 解析iperf3的JSON输出
func (e *DefaultExecutor) parseResult(result *TestResult) error {
	if result.RawOutput == "" {
		return fmt.Errorf("iperf3输出为空")
	}

	var output Iperf3Output
	if err := json.Unmarshal([]byte(result.RawOutput), &output); err != nil {
		return fmt.Errorf("解析JSON输出失败: %w", err)
	}

	// 检查是否有错误
	if output.Error != "" {
		return fmt.Errorf("iperf3报告错误: %s", output.Error)
	}

	// 检查是否有结束数据
	if len(output.End.Streams) == 0 {
		return fmt.Errorf("iperf3输出中没有找到结束数据")
	}

	// 根据测试类型解析结果
	if result.Type == "tcp" {
		return e.parseTCPResult(result, &output)
	} else if result.Type == "udp" {
		return e.parseUDPResult(result, &output)
	}

	return fmt.Errorf("未知的测试类型: %s", result.Type)
}

// parseTCPResult 解析TCP测试结果
func (e *DefaultExecutor) parseTCPResult(result *TestResult, output *Iperf3Output) error {
	// 使用发送端的数据（通常更准确）
	sender := output.End.SumSent

	// 转换速度从bps到Mbps
	result.MbitsPerSecond = sender.BitsPerSecond / 1000000.0
	result.BytesTransferred = sender.Bytes
	result.Retransmits = sender.Retransmits

	// TCP测试结果解析完成

	return nil
}

// parseUDPResult 解析UDP测试结果
func (e *DefaultExecutor) parseUDPResult(result *TestResult, output *Iperf3Output) error {
	// 使用发送端的数据
	sender := output.End.SumSent
	receiver := output.End.SumReceived

	// 转换速度从bps到Mbps
	result.MbitsPerSecond = sender.BitsPerSecond / 1000000.0
	result.BytesTransferred = sender.Bytes

	// 计算丢包率
	if sender.Packets > 0 {
		lostPackets := sender.Packets - receiver.Packets
		if lostPackets < 0 {
			lostPackets = 0
		}
		result.PacketLoss = float64(lostPackets) / float64(sender.Packets) * 100.0
	}

	// 获取抖动信息
	result.Jitter = receiver.Jitter

	// UDP测试结果解析完成

	return nil
}

// ValidateResult 验证测试结果的合理性
func ValidateResult(result *TestResult) error {
	if result == nil {
		return fmt.Errorf("测试结果为空")
	}

	// 检查基本字段
	if result.Type == "" {
		return fmt.Errorf("测试类型为空")
	}

	if result.Type != "tcp" && result.Type != "udp" {
		return fmt.Errorf("无效的测试类型: %s", result.Type)
	}

	// 检查速度是否合理（0-100Gbps）
	if result.MbitsPerSecond < 0 || result.MbitsPerSecond > 100000 {
		return fmt.Errorf("速度值不合理: %.2f Mbps", result.MbitsPerSecond)
	}

	// 检查传输字节数
	if result.BytesTransferred < 0 {
		return fmt.Errorf("传输字节数不能为负数: %d", result.BytesTransferred)
	}

	// 检查UDP特有字段
	if result.Type == "udp" {
		if result.PacketLoss < 0 || result.PacketLoss > 100 {
			return fmt.Errorf("丢包率不合理: %.2f%%", result.PacketLoss)
		}

		if result.Jitter < 0 {
			return fmt.Errorf("抖动值不能为负数: %.2f ms", result.Jitter)
		}
	}

	// 检查TCP特有字段
	if result.Type == "tcp" {
		if result.Retransmits < 0 {
			return fmt.Errorf("重传次数不能为负数: %d", result.Retransmits)
		}
	}

	return nil
}

// FormatResult 格式化测试结果为人类可读的字符串
func FormatResult(result *TestResult) string {
	if result == nil {
		return "测试结果为空"
	}

	baseInfo := fmt.Sprintf("类型: %s, 速度: %.2f Mbps, 传输: %.2f MB, 持续: %v",
		strings.ToUpper(result.Type),
		result.MbitsPerSecond,
		float64(result.BytesTransferred)/1024/1024,
		result.Duration.Round(time.Millisecond))

	if result.Type == "tcp" && result.Retransmits > 0 {
		baseInfo += fmt.Sprintf(", 重传: %d", result.Retransmits)
	}

	if result.Type == "udp" {
		if result.PacketLoss > 0 {
			baseInfo += fmt.Sprintf(", 丢包: %.2f%%", result.PacketLoss)
		}
		if result.Jitter > 0 {
			baseInfo += fmt.Sprintf(", 抖动: %.2f ms", result.Jitter)
		}
	}

	if result.Error != "" {
		baseInfo += fmt.Sprintf(", 错误: %s", result.Error)
	}

	return baseInfo
}

// GetQualityScore 根据测试结果计算质量评分（0-100）
func GetQualityScore(result *TestResult) float64 {
	if result == nil || result.Error != "" {
		return 0.0
	}

	score := 100.0

	if result.Type == "tcp" {
		// TCP质量评分主要基于速度和重传
		// 速度评分（假设1Gbps为满分）
		speedScore := math.Min(result.MbitsPerSecond/1000.0*100, 100.0)

		// 重传惩罚（每1%重传率扣10分）
		retransmitPenalty := 0.0
		if result.BytesTransferred > 0 {
			retransmitRate := float64(result.Retransmits) / float64(result.BytesTransferred) * 100
			retransmitPenalty = retransmitRate * 10
		}

		score = speedScore - retransmitPenalty

	} else if result.Type == "udp" {
		// UDP质量评分基于速度、丢包率和抖动
		// 速度评分
		speedScore := math.Min(result.MbitsPerSecond/1000.0*100, 100.0)

		// 丢包惩罚（每1%丢包扣5分）
		packetLossPenalty := result.PacketLoss * 5

		// 抖动惩罚（每10ms抖动扣1分）
		jitterPenalty := result.Jitter / 10.0

		score = speedScore - packetLossPenalty - jitterPenalty
	}

	// 确保评分在0-100范围内
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}
