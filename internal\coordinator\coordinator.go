package coordinator

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/client"

	"github.com/sirupsen/logrus"
)

// TestCoordinator coordinates the execution of iperf3 tests across multiple clients
type TestCoordinator interface {
	// Start starts the test coordinator
	Start(ctx context.Context) error

	// Stop stops the test coordinator
	Stop() error

	// ExecuteTestRound executes a complete test round for the current hour
	ExecuteTestRound(ctx context.Context, hour int) (*TestRoundResult, error)

	// ExecuteTestBatch executes tests for a batch of clients
	ExecuteTestBatch(ctx context.Context, clients []client.Client, testConfig TestBatchConfig) (*TestBatchResult, error)

	// GetStatus returns the current status of the coordinator
	GetStatus() *CoordinatorStatus
}

// Legacy Coordinator interface for backward compatibility
type Coordinator interface {
	// PrepareClient notifies a client to prepare for an iperf3 test (e.g., start iperf3 server).
	PrepareClient(ctx context.Context, clientID string) error

	// ExecuteTCPTest initiates a TCP iperf3 test between the client and a target.
	ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error)

	// ExecuteUDPTest initiates a UDP iperf3 test between the client and a target.
	ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error)

	// StopClient notifies a client to stop its iperf3 process.
	StopClient(ctx context.Context, clientID string) error
}

// TestRoundResult represents the result of a complete test round
type TestRoundResult struct {
	Hour            int               `json:"hour"`
	StartTime       time.Time         `json:"start_time"`
	EndTime         time.Time         `json:"end_time"`
	Duration        time.Duration     `json:"duration"`
	TotalTests      int               `json:"total_tests"`
	SuccessfulTests int               `json:"successful_tests"`
	FailedTests     int               `json:"failed_tests"`
	BatchResults    []TestBatchResult `json:"batch_results"`
	Summary         TestRoundSummary  `json:"summary"`
}

// TestBatchResult represents the result of a batch of tests
type TestBatchResult struct {
	BatchID     string              `json:"batch_id"`
	StartTime   time.Time           `json:"start_time"`
	EndTime     time.Time           `json:"end_time"`
	Duration    time.Duration       `json:"duration"`
	ClientCount int                 `json:"client_count"`
	TCPResults  []client.TestResult `json:"tcp_results"`
	UDPResults  []client.TestResult `json:"udp_results"`
	Errors      []TestError         `json:"errors"`
}

// TestBatchConfig represents configuration for a batch of tests
type TestBatchConfig struct {
	BatchID    string             `json:"batch_id"`
	TCPConfig  *client.TestConfig `json:"tcp_config"`
	UDPConfig  *client.TestConfig `json:"udp_config"`
	BatchDelay time.Duration      `json:"batch_delay"`
}

// TestRoundSummary represents a summary of test round results
type TestRoundSummary struct {
	TCPSummary TestTypeSummary `json:"tcp_summary"`
	UDPSummary TestTypeSummary `json:"udp_summary"`
}

// TestTypeSummary represents summary statistics for a test type
type TestTypeSummary struct {
	TestCount       int     `json:"test_count"`
	SuccessfulTests int     `json:"successful_tests"`
	FailedTests     int     `json:"failed_tests"`
	SuccessRate     float64 `json:"success_rate"`
	AvgMbps         float64 `json:"avg_mbps"`
	MinMbps         float64 `json:"min_mbps"`
	MaxMbps         float64 `json:"max_mbps"`
	TotalBytes      int64   `json:"total_bytes"`
}

// TestError represents an error that occurred during testing
type TestError struct {
	ClientID  string    `json:"client_id"`
	TestType  string    `json:"test_type"`
	Error     string    `json:"error"`
	Timestamp time.Time `json:"timestamp"`
}

// CoordinatorStatus represents the current status of the coordinator
type CoordinatorStatus struct {
	IsRunning        bool       `json:"is_running"`
	CurrentRound     *string    `json:"current_round,omitempty"`
	LastRoundTime    *time.Time `json:"last_round_time,omitempty"`
	TotalRounds      int        `json:"total_rounds"`
	SuccessfulRounds int        `json:"successful_rounds"`
	FailedRounds     int        `json:"failed_rounds"`
	LastError        *string    `json:"last_error,omitempty"`
}

// NewCoordinator creates a new instance of the Coordinator.
func NewCoordinator(clientManager *client.Manager, logger *logrus.Logger) Coordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &defaultCoordinator{
		clientManager: clientManager,
		logger:        logger,
	}
}

type defaultCoordinator struct {
	clientManager *client.Manager
	logger        *logrus.Logger
}

// PrepareClient implements Coordinator.
func (dc *defaultCoordinator) PrepareClient(ctx context.Context, clientID string) error {
	dc.logger.WithField("client_id", clientID).Info("Preparing client")

	client, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("failed to get client %s: %w", clientID, err)
	}

	if err := client.Prepare(ctx); err != nil {
		return fmt.Errorf("failed to prepare client %s: %w", clientID, err)
	}

	dc.logger.WithField("client_id", clientID).Info("Client prepared successfully")
	return nil
}

// ExecuteTCPTest implements Coordinator.
func (dc *defaultCoordinator) ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error) {
	dc.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"target":    target,
		"duration":  duration,
	}).Info("Executing TCP test")

	clientObj, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return "", fmt.Errorf("failed to get client %s: %w", clientID, err)
	}

	// Prepare client for testing
	if err := clientObj.Prepare(ctx); err != nil {
		return "", fmt.Errorf("failed to prepare client %s: %w", clientID, err)
	}

	// Create TCP test configuration using the new TestCoordinator approach
	// Note: This uses the DefaultTestCoordinator's createTCPTestConfig method
	coordinator := NewTestCoordinator(dc.clientManager, 4, dc.logger).(*DefaultTestCoordinator)
	testConfig := coordinator.createTCPTestConfig()
	testConfig.Duration = time.Duration(duration) * time.Second

	// Execute test
	result, err := clientObj.StartTest(ctx, *testConfig)
	if err != nil {
		// Try to stop client even if test failed
		clientObj.Stop(ctx)
		return "", fmt.Errorf("TCP test failed for client %s: %w", clientID, err)
	}

	// Stop client after test
	if err := clientObj.Stop(ctx); err != nil {
		dc.logger.WithField("client_id", clientID).Warn("Failed to stop client after test")
	}

	// Format result as string
	resultStr := fmt.Sprintf("TCP Test Result - Client: %s, Speed: %.2f Mbps, Bytes: %d, Duration: %v",
		result.ClientID, result.MbitsPerSecond, result.BytesTransferred, result.Duration)

	dc.logger.WithFields(logrus.Fields{
		"client_id":  clientID,
		"speed_mbps": result.MbitsPerSecond,
		"bytes":      result.BytesTransferred,
	}).Info("TCP test completed successfully")

	return resultStr, nil
}

// ExecuteUDPTest implements Coordinator.
func (dc *defaultCoordinator) ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error) {
	dc.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"target":    target,
		"duration":  duration,
		"bandwidth": bandwidth,
	}).Info("Executing UDP test")

	clientObj, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return "", fmt.Errorf("failed to get client %s: %w", clientID, err)
	}

	// Prepare client for testing
	if err := clientObj.Prepare(ctx); err != nil {
		return "", fmt.Errorf("failed to prepare client %s: %w", clientID, err)
	}

	// Create UDP test configuration using the new TestCoordinator approach
	// Note: This uses the DefaultTestCoordinator's createUDPTestConfig method
	coordinator := NewTestCoordinator(dc.clientManager, 4, dc.logger).(*DefaultTestCoordinator)
	testConfig := coordinator.createUDPTestConfig()
	testConfig.Duration = time.Duration(duration) * time.Second
	testConfig.UDP.Bandwidth = bandwidth

	// Execute test
	result, err := clientObj.StartTest(ctx, *testConfig)
	if err != nil {
		// Try to stop client even if test failed
		clientObj.Stop(ctx)
		return "", fmt.Errorf("UDP test failed for client %s: %w", clientID, err)
	}

	// Stop client after test
	if err := clientObj.Stop(ctx); err != nil {
		dc.logger.WithField("client_id", clientID).Warn("Failed to stop client after test")
	}

	// Format result as string
	resultStr := fmt.Sprintf("UDP Test Result - Client: %s, Speed: %.2f Mbps, Bytes: %d, Duration: %v",
		result.ClientID, result.MbitsPerSecond, result.BytesTransferred, result.Duration)

	dc.logger.WithFields(logrus.Fields{
		"client_id":  clientID,
		"speed_mbps": result.MbitsPerSecond,
		"bytes":      result.BytesTransferred,
	}).Info("UDP test completed successfully")

	return resultStr, nil
}

// StopClient implements Coordinator.
func (dc *defaultCoordinator) StopClient(ctx context.Context, clientID string) error {
	dc.logger.WithField("client_id", clientID).Info("Stopping client")

	client, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("failed to get client %s: %w", clientID, err)
	}

	if err := client.Stop(ctx); err != nil {
		return fmt.Errorf("failed to stop client %s: %w", clientID, err)
	}

	dc.logger.WithField("client_id", clientID).Info("Client stopped successfully")
	return nil
}

// DefaultTestCoordinator implements the TestCoordinator interface
type DefaultTestCoordinator struct {
	clientManager *client.Manager
	concurrency   ConcurrencyController
	parser        ResultParser
	logger        *logrus.Logger

	// Status tracking
	status    CoordinatorStatus
	isRunning bool
	mu        sync.RWMutex

	// Statistics
	totalRounds      int
	successfulRounds int
	failedRounds     int
	lastError        error
}

// NewTestCoordinator creates a new test coordinator
func NewTestCoordinator(
	clientManager *client.Manager,
	maxConcurrency int,
	logger *logrus.Logger,
) TestCoordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &DefaultTestCoordinator{
		clientManager: clientManager,
		concurrency:   NewConcurrencyController(maxConcurrency),
		parser:        NewResultParser(),
		logger:        logger,
		status: CoordinatorStatus{
			IsRunning: false,
		},
	}
}

// Start starts the test coordinator
func (c *DefaultTestCoordinator) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Starting test coordinator")

	c.isRunning = true
	c.status.IsRunning = true

	c.logger.Info("Test coordinator started successfully")
	return nil
}

// Stop stops the test coordinator
func (c *DefaultTestCoordinator) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Stopping test coordinator")

	c.isRunning = false
	c.status.IsRunning = false

	c.logger.Info("Test coordinator stopped")
	return nil
}

// GetStatus returns the current status of the coordinator
func (c *DefaultTestCoordinator) GetStatus() *CoordinatorStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()

	status := c.status
	status.TotalRounds = c.totalRounds
	status.SuccessfulRounds = c.successfulRounds
	status.FailedRounds = c.failedRounds

	if c.lastError != nil {
		errStr := c.lastError.Error()
		status.LastError = &errStr
	}

	return &status
}
