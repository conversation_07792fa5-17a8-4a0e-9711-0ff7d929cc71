package coordinator

import "context"

// Coordinator defines the interface for managing iperf3 test flows.
type Coordinator interface {
	// PrepareClient notifies a client to prepare for an iperf3 test (e.g., start iperf3 server).
	PrepareClient(ctx context.Context, clientID string) error

	// ExecuteTCPTest initiates a TCP iperf3 test between the client and a target.
	ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error)

	// ExecuteUDPTest initiates a UDP iperf3 test between the client and a target.
	ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error)

	// StopClient notifies a client to stop its iperf3 process.
	StopClient(ctx context.Context, clientID string) error
}

// NewCoordinator creates a new instance of the Coordinator.
func NewCoordinator() Coordinator {
	return &defaultCoordinator{}
}

type defaultCoordinator struct {
	// Add dependencies here, e.g., client manager, iperf3 executor
}

// PrepareClient implements Coordinator.
func (dc *defaultCoordinator) PrepareClient(ctx context.Context, clientID string) error {
	// TODO: Implement logic to notify client to prepare
	return nil
}

// ExecuteTCPTest implements Coordinator.
func (dc *defaultCoordinator) ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error) {
	// TODO: Implement logic to execute TCP test
	return "TCP test result", nil
}

// ExecuteUDPTest implements Coordinator.
func (dc *defaultCoordinator) ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error) {
	// TODO: Implement logic to execute UDP test
	return "UDP test result", nil
}

// StopClient implements Coordinator.
func (dc *defaultCoordinator) StopClient(ctx context.Context, clientID string) error {
	// TODO: Implement logic to notify client to stop
	return nil
}
