package coordinator

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/client"

	"github.com/sirupsen/logrus"
)

// TestCoordinator 协调多个客户端的 iperf3 测试执行
type TestCoordinator interface {
	// Start 启动测试协调器
	Start(ctx context.Context) error

	// Stop 停止测试协调器
	Stop() error

	// ExecuteTestRound 为当前小时执行一个完整的测试回合
	ExecuteTestRound(ctx context.Context, hour int) (*TestRoundResult, error)

	// ExecuteTestBatch 为一批客户端执行测试
	ExecuteTestBatch(ctx context.Context, clients []client.Client, testConfig TestBatchConfig) (*TestBatchResult, error)

	// GetStatus 返回协调器的当前状态
	GetStatus() *CoordinatorStatus
}

// Coordinator 接口，用于向后兼容
type Coordinator interface {
	// PrepareClient 通知客户端准备进行 iperf3 测试（例如，启动 iperf3 服务器）。
	PrepareClient(ctx context.Context, clientID string) error

	// ExecuteTCPTest 在客户端和目标之间启动一个 TCP iperf3 测试。
	ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error)

	// ExecuteUDPTest 在客户端和目标之间启动一个 UDP iperf3 测试。
	ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error)

	// StopClient 通知客户端停止其 iperf3 进程。
	StopClient(ctx context.Context, clientID string) error
}

// TestRoundResult 表示一个完整测试回合的结果
type TestRoundResult struct {
	Hour            int               `json:"hour"`
	StartTime       time.Time         `json:"start_time"`
	EndTime         time.Time         `json:"end_time"`
	Duration        time.Duration     `json:"duration"`
	TotalTests      int               `json:"total_tests"`
	SuccessfulTests int               `json:"successful_tests"`
	FailedTests     int               `json:"failed_tests"`
	BatchResults    []TestBatchResult `json:"batch_results"`
	Summary         TestRoundSummary  `json:"summary"`
}

// TestBatchResult 表示一批测试的结果
type TestBatchResult struct {
	BatchID     string              `json:"batch_id"`
	StartTime   time.Time           `json:"start_time"`
	EndTime     time.Time           `json:"end_time"`
	Duration    time.Duration       `json:"duration"`
	ClientCount int                 `json:"client_count"`
	TCPResults  []client.TestResult `json:"tcp_results"`
	UDPResults  []client.TestResult `json:"udp_results"`
	Errors      []TestError         `json:"errors"`
}

// TestBatchConfig 表示一批测试的配置
type TestBatchConfig struct {
	BatchID    string             `json:"batch_id"`
	TCPConfig  *client.TestConfig `json:"tcp_config"`
	UDPConfig  *client.TestConfig `json:"udp_config"`
	BatchDelay time.Duration      `json:"batch_delay"`
}

// TestRoundSummary 表示测试回合结果的摘要
type TestRoundSummary struct {
	TCPSummary TestTypeSummary `json:"tcp_summary"`
	UDPSummary TestTypeSummary `json:"udp_summary"`
}

// TestTypeSummary 表示测试类型的摘要统计信息
type TestTypeSummary struct {
	TestCount       int     `json:"test_count"`
	SuccessfulTests int     `json:"successful_tests"`
	FailedTests     int     `json:"failed_tests"`
	SuccessRate     float64 `json:"success_rate"`
	AvgMbps         float64 `json:"avg_mbps"`
	MinMbps         float64 `json:"min_mbps"`
	MaxMbps         float64 `json:"max_mbps"`
	TotalBytes      int64   `json:"total_bytes"`
}

// TestError 表示测试期间发生的错误
type TestError struct {
	ClientID  string    `json:"client_id"`
	TestType  string    `json:"test_type"`
	Error     string    `json:"error"`
	Timestamp time.Time `json:"timestamp"`
}

// CoordinatorStatus 表示协调器的当前状态
type CoordinatorStatus struct {
	IsRunning        bool       `json:"is_running"`
	CurrentRound     *string    `json:"current_round,omitempty"`
	LastRoundTime    *time.Time `json:"last_round_time,omitempty"`
	TotalRounds      int        `json:"total_rounds"`
	SuccessfulRounds int        `json:"successful_rounds"`
	FailedRounds     int        `json:"failed_rounds"`
	LastError        *string    `json:"last_error,omitempty"`
}

// NewCoordinator 创建 Coordinator 的新实例。
func NewCoordinator(clientManager *client.Manager, logger *logrus.Logger) Coordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &defaultCoordinator{
		clientManager: clientManager,
		logger:        logger,
	}
}

type defaultCoordinator struct {
	clientManager *client.Manager
	logger        *logrus.Logger
}

// PrepareClient 实现了 Coordinator 接口的 PrepareClient 方法。
func (dc *defaultCoordinator) PrepareClient(ctx context.Context, clientID string) error {
	dc.logger.WithField("client_id", clientID).Info("正在准备客户端")

	client, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("获取客户端 %s 失败: %w", clientID, err)
	}

	if err := client.Prepare(ctx); err != nil {
		return fmt.Errorf("准备客户端 %s 失败: %w", clientID, err)
	}

	dc.logger.WithField("client_id", clientID).Info("客户端准备成功")
	return nil
}

// ExecuteTCPTest 实现了 Coordinator 接口的 ExecuteTCPTest 方法。
func (dc *defaultCoordinator) ExecuteTCPTest(ctx context.Context, clientID, target string, duration int) (string, error) {
	dc.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"target":    target,
		"duration":  duration,
	}).Info("正在执行 TCP 测试")

	clientObj, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return "", fmt.Errorf("获取客户端 %s 失败: %w", clientID, err)
	}

	// 准备客户端进行测试
	if err := clientObj.Prepare(ctx); err != nil {
		return "", fmt.Errorf("准备客户端 %s 失败: %w", clientID, err)
	}

	// 创建 TCP 测试配置
	testConfig := client.TestConfig{
		Type:     "tcp",
		Duration: time.Duration(duration) * time.Second,
		TCP: &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		},
	}

	// 执行测试
	result, err := clientObj.StartTest(ctx, testConfig)
	if err != nil {
		// 即使测试失败也尝试停止客户端
		clientObj.Stop(ctx)
		return "", fmt.Errorf("客户端 %s 的 TCP 测试失败: %w", clientID, err)
	}

	// 测试后停止客户端
	if err := clientObj.Stop(ctx); err != nil {
		dc.logger.WithField("client_id", clientID).Warn("测试后停止客户端失败")
	}

	// 格式化结果为字符串
	resultStr := fmt.Sprintf("TCP 测试结果 - 客户端: %s, 速度: %.2f Mbps, 字节: %d, 持续时间: %v",
		result.ClientID, result.MbitsPerSecond, result.BytesTransferred, result.Duration)

	dc.logger.WithFields(logrus.Fields{
		"client_id":  clientID,
		"speed_mbps": result.MbitsPerSecond,
		"bytes":      result.BytesTransferred,
	}).Info("TCP 测试成功完成")

	return resultStr, nil
}

// ExecuteUDPTest 实现了 Coordinator 接口的 ExecuteUDPTest 方法。
func (dc *defaultCoordinator) ExecuteUDPTest(ctx context.Context, clientID, target string, duration int, bandwidth string) (string, error) {
	dc.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"target":    target,
		"duration":  duration,
		"bandwidth": bandwidth,
	}).Info("正在执行 UDP 测试")

	clientObj, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return "", fmt.Errorf("获取客户端 %s 失败: %w", clientID, err)
	}

	// 准备客户端进行测试
	if err := clientObj.Prepare(ctx); err != nil {
		return "", fmt.Errorf("准备客户端 %s 失败: %w", clientID, err)
	}

	// 创建 UDP 测试配置
	testConfig := client.TestConfig{
		Type:     "udp",
		Duration: time.Duration(duration) * time.Second,
		UDP: &client.UDPTestConfig{
			Bandwidth:  bandwidth,
			PacketSize: 1472,
		},
	}

	// 执行测试
	result, err := clientObj.StartTest(ctx, testConfig)
	if err != nil {
		// 即使测试失败也尝试停止客户端
		clientObj.Stop(ctx)
		return "", fmt.Errorf("客户端 %s 的 UDP 测试失败: %w", clientID, err)
	}

	// 测试后停止客户端
	if err := clientObj.Stop(ctx); err != nil {
		dc.logger.WithField("client_id", clientID).Warn("测试后停止客户端失败")
	}

	// 格式化结果为字符串
	resultStr := fmt.Sprintf("UDP 测试结果 - 客户端: %s, 速度: %.2f Mbps, 字节: %d, 持续时间: %v",
		result.ClientID, result.MbitsPerSecond, result.BytesTransferred, result.Duration)

	dc.logger.WithFields(logrus.Fields{
		"client_id":  clientID,
		"speed_mbps": result.MbitsPerSecond,
		"bytes":      result.BytesTransferred,
	}).Info("UDP 测试成功完成")

	return resultStr, nil
}

// StopClient 实现了 Coordinator 接口的 StopClient 方法。
func (dc *defaultCoordinator) StopClient(ctx context.Context, clientID string) error {
	dc.logger.WithField("client_id", clientID).Info("正在停止客户端")

	client, err := dc.clientManager.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("获取客户端 %s 失败: %w", clientID, err)
	}

	if err := client.Stop(ctx); err != nil {
		return fmt.Errorf("停止客户端 %s 失败: %w", clientID, err)
	}

	dc.logger.WithField("client_id", clientID).Info("客户端停止成功")
	return nil
}

// DefaultTestCoordinator 实现了 TestCoordinator 接口
type DefaultTestCoordinator struct {
	clientManager *client.Manager
	concurrency   ConcurrencyController
	parser        ResultParser
	logger        *logrus.Logger

	// 状态跟踪
	status    CoordinatorStatus
	isRunning bool
	mu        sync.RWMutex

	// 统计数据
	totalRounds      int
	successfulRounds int
	failedRounds     int
	lastError        error
}

// NewTestCoordinator 创建一个新的测试协调器
func NewTestCoordinator(
	clientManager *client.Manager,
	maxConcurrency int,
	logger *logrus.Logger,
) TestCoordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &DefaultTestCoordinator{
		clientManager: clientManager,
		concurrency:   NewConcurrencyController(maxConcurrency),
		parser:        NewResultParser(),
		logger:        logger,
		status: CoordinatorStatus{
			IsRunning: false,
		},
	}
}

// Start 启动测试协调器
func (c *DefaultTestCoordinator) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("正在启动测试协调器")

	c.isRunning = true
	c.status.IsRunning = true

	c.logger.Info("测试协调器启动成功")
	return nil
}

// Stop 停止测试协调器
func (c *DefaultTestCoordinator) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("正在停止测试协调器")

	c.isRunning = false
	c.status.IsRunning = false

	c.logger.Info("测试协调器已停止")
	return nil
}

// GetStatus 返回协调器的当前状态
func (c *DefaultTestCoordinator) GetStatus() *CoordinatorStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()

	status := c.status
	status.TotalRounds = c.totalRounds
	status.SuccessfulRounds = c.successfulRounds
	status.FailedRounds = c.failedRounds

	if c.lastError != nil {
		errStr := c.lastError.Error()
		status.LastError = &errStr
	}

	return &status
}
