#!/bin/bash

# iperf3-controller 构建脚本
# 支持多平台交叉编译

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="iperf3-controller"
VERSION="1.0.0"
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建目录
BUILD_DIR="build"
DIST_DIR="dist"

echo -e "${BLUE}🚀 iperf3-controller 构建脚本${NC}"
echo -e "${BLUE}版本: ${VERSION}${NC}"
echo -e "${BLUE}构建时间: ${BUILD_TIME}${NC}"
echo -e "${BLUE}Git提交: ${GIT_COMMIT}${NC}"
echo ""

# 清理旧的构建文件
echo -e "${YELLOW}🧹 清理旧的构建文件...${NC}"
rm -rf ${BUILD_DIR}
rm -rf ${DIST_DIR}
mkdir -p ${BUILD_DIR}
mkdir -p ${DIST_DIR}

# 构建标志
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -w -s"

# 支持的平台
declare -a PLATFORMS=(
    "linux/amd64"
    "linux/arm64"
    "linux/arm"
    "windows/amd64"
    "darwin/amd64"
    "darwin/arm64"
)

# OpenWRT特定平台
declare -a OPENWRT_PLATFORMS=(
    "linux/mips"
    "linux/mipsle"
    "linux/mips64"
    "linux/mips64le"
)

echo -e "${GREEN}📦 开始构建...${NC}"

# 构建主程序
for platform in "${PLATFORMS[@]}"; do
    IFS='/' read -r -a array <<< "$platform"
    GOOS="${array[0]}"
    GOARCH="${array[1]}"
    
    output_name="${PROJECT_NAME}-${GOOS}-${GOARCH}"
    if [ $GOOS = "windows" ]; then
        output_name+='.exe'
    fi
    
    echo -e "${BLUE}构建 ${GOOS}/${GOARCH}...${NC}"
    
    env GOOS=$GOOS GOARCH=$GOARCH go build \
        -ldflags="${LDFLAGS}" \
        -o ${BUILD_DIR}/${output_name} \
        ./cmd/iperf3-controller
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ${GOOS}/${GOARCH} 构建成功${NC}"
    else
        echo -e "${RED}❌ ${GOOS}/${GOARCH} 构建失败${NC}"
        exit 1
    fi
done

# 构建OpenWRT版本
echo -e "${YELLOW}🔧 构建OpenWRT版本...${NC}"
for platform in "${OPENWRT_PLATFORMS[@]}"; do
    IFS='/' read -r -a array <<< "$platform"
    GOOS="${array[0]}"
    GOARCH="${array[1]}"
    
    output_name="${PROJECT_NAME}-openwrt-${GOARCH}"
    
    echo -e "${BLUE}构建 OpenWRT ${GOOS}/${GOARCH}...${NC}"
    
    env GOOS=$GOOS GOARCH=$GOARCH CGO_ENABLED=0 go build \
        -ldflags="${LDFLAGS}" \
        -tags="netgo" \
        -o ${BUILD_DIR}/${output_name} \
        ./cmd/iperf3-controller
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ OpenWRT ${GOOS}/${GOARCH} 构建成功${NC}"
    else
        echo -e "${RED}❌ OpenWRT ${GOOS}/${GOARCH} 构建失败${NC}"
        exit 1
    fi
done

# 复制静态文件
echo -e "${YELLOW}📁 复制静态文件...${NC}"
if [ -d "web/static" ]; then
    cp -r web/static ${BUILD_DIR}/
    echo -e "${GREEN}✅ 静态文件复制完成${NC}"
fi

# 复制配置文件
if [ -f "config.example.yaml" ]; then
    cp config.example.yaml ${BUILD_DIR}/
    echo -e "${GREEN}✅ 配置文件复制完成${NC}"
fi

# 复制文档
if [ -f "README.md" ]; then
    cp README.md ${BUILD_DIR}/
fi

if [ -f "LICENSE" ]; then
    cp LICENSE ${BUILD_DIR}/
fi

# 创建发布包
echo -e "${YELLOW}📦 创建发布包...${NC}"

# 为每个平台创建压缩包
for platform in "${PLATFORMS[@]}"; do
    IFS='/' read -r -a array <<< "$platform"
    GOOS="${array[0]}"
    GOARCH="${array[1]}"
    
    output_name="${PROJECT_NAME}-${GOOS}-${GOARCH}"
    if [ $GOOS = "windows" ]; then
        output_name+='.exe'
    fi
    
    package_name="${PROJECT_NAME}-${VERSION}-${GOOS}-${GOARCH}"
    
    # 创建临时目录
    temp_dir="${BUILD_DIR}/temp_${GOOS}_${GOARCH}"
    mkdir -p ${temp_dir}
    
    # 复制文件
    cp ${BUILD_DIR}/${output_name} ${temp_dir}/
    if [ -d "${BUILD_DIR}/static" ]; then
        cp -r ${BUILD_DIR}/static ${temp_dir}/
    fi
    if [ -f "${BUILD_DIR}/config.example.yaml" ]; then
        cp ${BUILD_DIR}/config.example.yaml ${temp_dir}/
    fi
    if [ -f "${BUILD_DIR}/README.md" ]; then
        cp ${BUILD_DIR}/README.md ${temp_dir}/
    fi
    
    # 创建压缩包
    if [ $GOOS = "windows" ]; then
        (cd ${temp_dir} && zip -r ../../${DIST_DIR}/${package_name}.zip .)
    else
        tar -czf ${DIST_DIR}/${package_name}.tar.gz -C ${temp_dir} .
    fi
    
    # 清理临时目录
    rm -rf ${temp_dir}
    
    echo -e "${GREEN}✅ ${package_name} 打包完成${NC}"
done

# 创建OpenWRT发布包
echo -e "${YELLOW}📦 创建OpenWRT发布包...${NC}"
openwrt_dir="${BUILD_DIR}/openwrt"
mkdir -p ${openwrt_dir}

for platform in "${OPENWRT_PLATFORMS[@]}"; do
    IFS='/' read -r -a array <<< "$platform"
    GOARCH="${array[1]}"
    
    output_name="${PROJECT_NAME}-openwrt-${GOARCH}"
    cp ${BUILD_DIR}/${output_name} ${openwrt_dir}/
done

# 复制OpenWRT相关文件
if [ -d "${BUILD_DIR}/static" ]; then
    cp -r ${BUILD_DIR}/static ${openwrt_dir}/
fi

# 创建OpenWRT压缩包
tar -czf ${DIST_DIR}/${PROJECT_NAME}-${VERSION}-openwrt.tar.gz -C ${openwrt_dir} .
echo -e "${GREEN}✅ OpenWRT包创建完成${NC}"

# 生成校验和
echo -e "${YELLOW}🔐 生成校验和...${NC}"
(cd ${DIST_DIR} && sha256sum * > checksums.sha256)
echo -e "${GREEN}✅ 校验和生成完成${NC}"

# 显示构建结果
echo ""
echo -e "${GREEN}🎉 构建完成！${NC}"
echo -e "${BLUE}构建文件位置: ${BUILD_DIR}/${NC}"
echo -e "${BLUE}发布包位置: ${DIST_DIR}/${NC}"
echo ""
echo -e "${YELLOW}📊 构建统计:${NC}"
echo "二进制文件:"
ls -lh ${BUILD_DIR}/${PROJECT_NAME}-* | awk '{print "  " $9 " (" $5 ")"}'
echo ""
echo "发布包:"
ls -lh ${DIST_DIR}/*.tar.gz ${DIST_DIR}/*.zip 2>/dev/null | awk '{print "  " $9 " (" $5 ")"}'
echo ""
echo -e "${GREEN}✅ 所有平台构建成功！${NC}"
echo -e "${BLUE}支持平台: Linux (amd64/arm64/arm), Windows (amd64), macOS (amd64/arm64), OpenWRT (mips/mipsle/mips64/mips64le)${NC}"
