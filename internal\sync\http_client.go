package sync

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// HTTPSyncClient HTTP同步客户端
type HTTPSyncClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewHTTPSyncClient 创建新的HTTP同步客户端
func NewHTTPSyncClient(host string, port int, timeout time.Duration, logger *logrus.Logger) *HTTPSyncClient {
	if logger == nil {
		logger = logrus.New()
	}
	
	baseURL := fmt.Sprintf("http://%s:%d", host, port)
	
	return &HTTPSyncClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		logger: logger,
	}
}

// HealthCheckRequest 健康检查请求
type HealthCheckRequest struct {
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	Uptime    string    `json:"uptime"`
}

// CheckHealth 检查对端健康状态
func (c *HTTPSyncClient) CheckHealth(ctx context.Context) (*HealthCheckResponse, error) {
	url := c.baseURL + "/api/v1/health"
	
	request := &HealthCheckRequest{
		Timestamp: time.Now(),
		Version:   "1.0.0",
	}
	
	var response HealthCheckResponse
	if err := c.doRequest(ctx, "POST", url, request, &response); err != nil {
		return nil, fmt.Errorf("健康检查失败: %w", err)
	}
	
	return &response, nil
}

// DataVersionRequest 数据版本请求
type DataVersionRequest struct {
	Tables []string `json:"tables"`
}

// DataVersionResponse 数据版本响应
type DataVersionResponse struct {
	Versions map[string]int64 `json:"versions"`
	Timestamp time.Time       `json:"timestamp"`
}

// GetDataVersion 获取数据版本
func (c *HTTPSyncClient) GetDataVersion(ctx context.Context, tables []string) (*DataVersionResponse, error) {
	url := c.baseURL + "/api/v1/sync/version"
	
	request := &DataVersionRequest{
		Tables: tables,
	}
	
	var response DataVersionResponse
	if err := c.doRequest(ctx, "POST", url, request, &response); err != nil {
		return nil, fmt.Errorf("获取数据版本失败: %w", err)
	}
	
	return &response, nil
}

// SyncDataRequest 同步数据请求
type SyncDataRequest struct {
	Table     string                 `json:"table"`
	Operation string                 `json:"operation"` // "push" or "pull"
	Since     int64                  `json:"since"`     // 时间戳，获取此时间之后的变更
	Data      []map[string]interface{} `json:"data,omitempty"`
}

// SyncDataResponse 同步数据响应
type SyncDataResponse struct {
	Success   bool                     `json:"success"`
	Message   string                   `json:"message"`
	Data      []map[string]interface{} `json:"data,omitempty"`
	Count     int                      `json:"count"`
	Timestamp time.Time                `json:"timestamp"`
}

// PushData 推送数据到对端
func (c *HTTPSyncClient) PushData(ctx context.Context, table string, data []map[string]interface{}) (*SyncDataResponse, error) {
	url := c.baseURL + "/api/v1/sync/data"
	
	request := &SyncDataRequest{
		Table:     table,
		Operation: "push",
		Data:      data,
	}
	
	var response SyncDataResponse
	if err := c.doRequest(ctx, "POST", url, request, &response); err != nil {
		return nil, fmt.Errorf("推送数据失败: %w", err)
	}
	
	return &response, nil
}

// PullData 从对端拉取数据
func (c *HTTPSyncClient) PullData(ctx context.Context, table string, since int64) (*SyncDataResponse, error) {
	url := c.baseURL + "/api/v1/sync/data"
	
	request := &SyncDataRequest{
		Table:     table,
		Operation: "pull",
		Since:     since,
	}
	
	var response SyncDataResponse
	if err := c.doRequest(ctx, "POST", url, request, &response); err != nil {
		return nil, fmt.Errorf("拉取数据失败: %w", err)
	}
	
	return &response, nil
}

// ConflictResolutionRequest 冲突解决请求
type ConflictResolutionRequest struct {
	Table     string                 `json:"table"`
	Conflicts []ConflictRecord       `json:"conflicts"`
	Strategy  string                 `json:"strategy"` // "local_wins", "remote_wins", "merge", "manual"
}

// ConflictRecord 冲突记录
type ConflictRecord struct {
	ID         string                 `json:"id"`
	LocalData  map[string]interface{} `json:"local_data"`
	RemoteData map[string]interface{} `json:"remote_data"`
	Timestamp  time.Time              `json:"timestamp"`
}

// ConflictResolutionResponse 冲突解决响应
type ConflictResolutionResponse struct {
	Success      bool                     `json:"success"`
	Message      string                   `json:"message"`
	ResolvedData []map[string]interface{} `json:"resolved_data"`
	Timestamp    time.Time                `json:"timestamp"`
}

// ResolveConflicts 解决数据冲突
func (c *HTTPSyncClient) ResolveConflicts(ctx context.Context, table string, conflicts []ConflictRecord, strategy string) (*ConflictResolutionResponse, error) {
	url := c.baseURL + "/api/v1/sync/conflicts"
	
	request := &ConflictResolutionRequest{
		Table:     table,
		Conflicts: conflicts,
		Strategy:  strategy,
	}
	
	var response ConflictResolutionResponse
	if err := c.doRequest(ctx, "POST", url, request, &response); err != nil {
		return nil, fmt.Errorf("解决冲突失败: %w", err)
	}
	
	return &response, nil
}

// doRequest 执行HTTP请求
func (c *HTTPSyncClient) doRequest(ctx context.Context, method, url string, request interface{}, response interface{}) error {
	// 序列化请求体
	var body io.Reader
	if request != nil {
		jsonData, err := json.Marshal(request)
		if err != nil {
			return fmt.Errorf("序列化请求失败: %w", err)
		}
		body = bytes.NewReader(jsonData)
	}
	
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "iperf3-controller-sync/1.0")
	
	c.logger.WithFields(logrus.Fields{
		"method": method,
		"url":    url,
	}).Debug("发送HTTP请求")
	
	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}
	
	c.logger.WithFields(logrus.Fields{
		"status_code": resp.StatusCode,
		"response_size": len(respBody),
	}).Debug("收到HTTP响应")
	
	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}
	
	// 反序列化响应体
	if response != nil {
		if err := json.Unmarshal(respBody, response); err != nil {
			return fmt.Errorf("反序列化响应失败: %w", err)
		}
	}
	
	return nil
}

// GetBaseURL 获取基础URL
func (c *HTTPSyncClient) GetBaseURL() string {
	return c.baseURL
}

// SetTimeout 设置超时时间
func (c *HTTPSyncClient) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}

// Close 关闭客户端
func (c *HTTPSyncClient) Close() error {
	// HTTP客户端不需要显式关闭
	c.logger.Debug("HTTP同步客户端已关闭")
	return nil
}
