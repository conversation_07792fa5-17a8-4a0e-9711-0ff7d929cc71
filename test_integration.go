package main

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"iperf3-controller/internal/api"
	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/scheduler"
	"iperf3-controller/internal/sync"
)

func main() {
	fmt.Println("🚀 iperf3-controller 集成测试")
	fmt.Println("===============================")
	
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // 减少输出
	
	// 测试计数器
	totalTests := 0
	passedTests := 0
	
	runTest := func(name string, testFunc func() error) {
		totalTests++
		fmt.Printf("🧪 测试: %s\n", name)
		
		if err := testFunc(); err != nil {
			fmt.Printf("❌ 失败: %v\n", err)
		} else {
			fmt.Printf("✅ 通过\n")
			passedTests++
		}
		fmt.Println()
	}
	
	// 测试1: 客户端管理器基础功能
	runTest("客户端管理器基础功能", func() error {
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}
		
		clientManager := client.NewManager(clientConfig, logger)
		if clientManager == nil {
			return fmt.Errorf("客户端管理器创建失败")
		}
		
		// 添加测试客户端
		testClient := client.ClientInfo{
			ID:      "integration-test-client",
			Name:    "集成测试客户端",
			Host:    "*************",
			Port:    55200,
			Enabled: true,
		}
		
		if err := clientManager.AddClient(testClient); err != nil {
			return fmt.Errorf("添加客户端失败: %w", err)
		}
		
		// 获取客户端
		_, err := clientManager.GetClient("integration-test-client")
		if err != nil {
			return fmt.Errorf("获取客户端失败: %w", err)
		}
		
		// 获取启用的客户端列表
		clients := clientManager.GetEnabledClients()
		if len(clients) == 0 {
			return fmt.Errorf("启用的客户端列表为空")
		}
		
		return nil
	})
	
	// 测试2: 测试协调器基础功能
	runTest("测试协调器基础功能", func() error {
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}
		
		clientManager := client.NewManager(clientConfig, logger)
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)
		
		if testCoordinator == nil {
			return fmt.Errorf("协调器创建失败")
		}
		
		// 启动协调器
		ctx := context.Background()
		if err := testCoordinator.Start(ctx); err != nil {
			return fmt.Errorf("启动协调器失败: %w", err)
		}
		
		// 获取状态
		status := testCoordinator.GetStatus()
		if !status.IsRunning {
			return fmt.Errorf("协调器状态显示未运行")
		}
		
		// 停止协调器
		if err := testCoordinator.Stop(); err != nil {
			return fmt.Errorf("停止协调器失败: %w", err)
		}
		
		return nil
	})
	
	// 测试3: 调度器基础功能
	runTest("调度器基础功能", func() error {
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}
		
		clientManager := client.NewManager(clientConfig, logger)
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)
		
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "always", // 使用always模式便于测试
			Timezone: "Asia/Shanghai",
		}
		
		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			return fmt.Errorf("创建调度器失败: %w", err)
		}
		
		// 获取状态
		status := testScheduler.GetStatus()
		if status.IsRunning {
			return fmt.Errorf("调度器初始状态应为未运行")
		}
		
		// 获取下次调度时间
		nextTime := testScheduler.GetNextScheduledTime()
		if nextTime.IsZero() {
			return fmt.Errorf("下次调度时间不应为零值")
		}
		
		return nil
	})
	
	// 测试4: 同步管理器基础功能
	runTest("同步管理器基础功能", func() error {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}
		
		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		if syncManager == nil {
			return fmt.Errorf("同步管理器创建失败")
		}
		
		// 获取状态
		status := syncManager.GetStatus()
		if status.IsRunning {
			return fmt.Errorf("同步管理器初始状态应为未运行")
		}
		
		// 获取对端状态
		peerStatus := syncManager.GetPeerStatus()
		if peerStatus.Host != "*************" {
			return fmt.Errorf("对端主机地址不匹配")
		}
		
		return nil
	})
	
	// 测试5: API服务器基础功能
	runTest("API服务器基础功能", func() error {
		// 创建核心组件
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}
		
		clientManager := client.NewManager(clientConfig, logger)
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)
		
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "always",
			Timezone: "Asia/Shanghai",
		}
		
		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			return fmt.Errorf("创建调度器失败: %w", err)
		}
		
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}
		
		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		
		// 创建API服务器
		serverConfig := &api.ServerConfig{
			Host:         "localhost",
			Port:         8081, // 使用不同端口避免冲突
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
			EnableCORS:   true,
			EnableAuth:   false,
			StaticDir:    "web/static",
		}
		
		apiServer := api.NewServer(
			serverConfig,
			clientManager,
			testCoordinator,
			testScheduler,
			syncManager,
			nil,
			logger,
		)
		
		if apiServer == nil {
			return fmt.Errorf("API服务器创建失败")
		}
		
		// 获取路由器
		router := apiServer.GetRouter()
		if router == nil {
			return fmt.Errorf("获取路由器失败")
		}
		
		return nil
	})
	
	// 显示测试结果
	fmt.Println("===============================")
	fmt.Printf("📊 测试结果: %d/%d 通过\n", passedTests, totalTests)
	
	if passedTests == totalTests {
		fmt.Println("🎉 所有集成测试通过！")
		fmt.Println("")
		fmt.Println("✅ 系统组件验证:")
		fmt.Println("   - 客户端管理器: 正常")
		fmt.Println("   - 测试协调器: 正常")
		fmt.Println("   - 调度器: 正常")
		fmt.Println("   - 同步管理器: 正常")
		fmt.Println("   - API服务器: 正常")
		fmt.Println("")
		fmt.Println("🚀 系统已准备好部署！")
	} else {
		fmt.Printf("❌ %d 个测试失败\n", totalTests-passedTests)
		fmt.Println("请检查失败的组件并修复问题")
	}
}
